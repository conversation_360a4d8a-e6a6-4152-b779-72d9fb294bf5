
START - automation started - Mon Mar 18 2024 10:02:08 GMT+0800 (CST)

LIVE MODE - type done to quit
 [RPA][STARTED]
[RPA][0] - listening for inputs
[RPA][1] - https://www.zhipin.com/web/geek/job-recommend?city=101010100&salary=406&experience=105,106,107&industry=100015&scale=303,304,305,306&jobType=1901
[RPA][1] - listening for inputs
[RPA][2] - count_result = count('//*[@class="job-card-box"]').toString()
[RPA][2] - listening for inputs
[RPA][3] - dump count_result to rpa_python.txt
[RPA][3] - listening for inputs
[RPA][4] - exist_result = exist('//*[@id="wrap"]/div[2]/div[1]/div/div[1]/a[3]').toString()
[RPA][4] - listening for inputs
[RPA][5] - dump exist_result to rpa_python.txt
[RPA][5] - listening for inputs
[RPA][6] - click //*[@id="wrap"]/div[2]/div[1]/div/div[1]/a[3]
[RPA][6] - listening for inputs
[RPA][7] - present_result = present('//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]').toString()
[RPA][7] - listening for inputs
[RPA][8] - dump present_result to rpa_python.txt
[RPA][8] - listening for inputs
[RPA][9] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]').toString()
[RPA][9] - listening for inputs
[RPA][10] - dump exist_result to rpa_python.txt
[RPA][10] - listening for inputs
[RPA][11] - click //*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]
[RPA][11] - listening for inputs
[RPA][12] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[1]/div[1]/div/span[1]').toString()
[RPA][12] - listening for inputs
[RPA][13] - dump exist_result to rpa_python.txt
[RPA][13] - listening for inputs
[RPA][14] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[1]/div[1]/ul').toString()
[RPA][14] - listening for inputs
[RPA][15] - dump exist_result to rpa_python.txt
[RPA][15] - listening for inputs
[RPA][16] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/p').toString()
[RPA][16] - listening for inputs
[RPA][17] - dump exist_result to rpa_python.txt
[RPA][17] - listening for inputs
[RPA][18] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/div/h2').toString()
[RPA][18] - listening for inputs
[RPA][19] - dump exist_result to rpa_python.txt
[RPA][19] - listening for inputs
[RPA][20] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/div[1]/div[2]').toString()
[RPA][20] - listening for inputs
[RPA][21] - dump exist_result to rpa_python.txt
[RPA][21] - listening for inputs
[RPA][22] - read page to read_result
[RPA][22] - listening for inputs
[RPA][23] - dump read_result to rpa_python.txt
[RPA][23] - listening for inputs
[RPA][24] - https://www.zhipin.com/web/geek/job-recommend?city=101010100&salary=406&experience=105,106,107&industry=100015&scale=303,304,305,306&jobType=1901
