import math

# import rpa as r
import datetime
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
import tagui_2 as r
import signal

import easyocr


reader = easyocr.Reader(['ch_sim', 'en'])


def ocr_read(img_file):
    result = reader.readtext(img_file, detail=0)
    return result

def run():
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)

    r.url('https://leetcode.cn/problemset/all/')
    r.wait(2)
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    table = soup.find(class_="table-striped")
    link = soup.select_one('#__next > div > div.mx-auto.mt-\[50px\].w-full.grow.p-4.md\:mt-0.md\:max-w-\[888px\].md\:p-6.lg\:max-w-screen-xl > div.grid.grid-cols-4.gap-4.md\:grid-cols-3.lg\:grid-cols-4.lg\:gap-6 > div.col-span-4.z-base.md\:col-span-2.lg\:col-span-3 > div:nth-child(7) > div.-mx-4.md\:mx-0 > div > div > div:nth-child(2) > div:nth-child(1) > div:nth-child(2) > div > div > div > div > a')
    link = 'https://leetcode.cn' + link['href']
    r.url(link)
    r.wait(2)
    r.url(link)
    r.wait(2)
    r.click('/html/body/div[4]/div/div[1]/div/button[1]')
    r.keyboard('[f11]')
    file_name = datetime.datetime.today().strftime('%Y-%m-%d') + '.png'
    r.snap('//*[@id="question-detail-main-tabs"]/div[2]/div', file_name)
    r.wait(2)
    # res = ocr_read(file_name)
    # print(res)
    r.keyboard('[f11]')




if __name__ == '__main__':
    # res = ocr_read('2023-01-16.png')
    # print(res)
    r.init(True)
    run()
    r.close()