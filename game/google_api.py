from googlesearch import search
import requests
from bs4 import BeautifulSoup
import re
import rpa as r

def search(keyword):
    r.init(True)
    results = []
    try:
        r.url(f'https://www.google.com/search?q={keyword}')
        r.wait(2)
        result = r.read("page")
        soup = BeautifulSoup(result, features="html.parser")
        main = soup.find('div', {"id": "search"})
        list = main.find_all('a', href=True)
        for result in list:
            link = result['href']
            if link.startswith('https://translate.google.com'):
                continue
            results.append(link)
    finally:
        r.close()
    return {
        "result:": results
    }

def search_by_request(keyword):
    try:
        response = requests.get(f"http://127.0.0.1:7777/api/rpa/search?keyword={keyword}")
        items = response.json()
        return items["result"]
    except requests.RequestException as e:
        print(f"请求出错: {e}")
        return []

def get_download_link(url, type = "meta"):
    try:
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        links = soup.find_all('a', href=True)
        mega_links = [link['href'] for link in links if re.search(r'mega', link['href'], re.IGNORECASE)]
        mega_link = mega_links[0] if len(mega_links)>0 else None
        return mega_link
    except requests.RequestException as e:
        print(f"请求出错: {e}")
        return []


# 使用示例
if __name__ == "__main__":
    res = search_by_request("A Boy and His Blob site:skidrowreloaded.com")
    for url in res:
        print(get_download_link(url))
        break