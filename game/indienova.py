import requests
import xml.etree.ElementTree as ET
import re

def get_steam_app_ids():
    url = "http://***********:1200/indienova/column/"
    results = []

    try:
        # 发送请求获取RSS内容
        response = requests.get(url)
        response.raise_for_status()

        # 解析XML
        root = ET.fromstring(response.content)

        # 遍历所有item元素
        for item in root.findall(".//item"):
            title = item.find("title").text
            description = item.find("description").text

            # 检查标题是否包含"steam"（不区分大小写）
            if "steam" in title.lower():
                # 使用正则表达式匹配Steam应用链接
                steam_links = re.findall(r'store\.steampowered\.com/app/(\d+)', description)

                if steam_links:
                    # 为每个找到的AppID创建一个结果
                    for app_id in steam_links:
                        results.append({
                            "app_id": app_id,
                            "article_title": title.strip()
                        })

    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
    except Exception as e:
        print(f"发生错误: {e}")

    return results


def get_game_details(app_id):
    url = f'http://store.steampowered.com/api/appdetails?appids={app_id}'
    try:
        # 发送API请求并获取响应数据
        response = requests.get(url,headers={
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"  # 指定所需的语言
        })
        data = response.json()
        # 提取游戏详情
        game_data = data[str(app_id)]
        if game_data['success']:
            game_details = game_data['data']
            min_req = None
            recm_req = None
            try:
                min_req = game_details.get('pc_requirements',{}).get('minimum')
                recm_req = game_details.get('pc_requirements',{}).get('recommended')
            except:
                pass
            return {
                "steam_name": game_details.get("name"),
                "steam_appid": game_details.get("steam_appid"),
                'steam_detailed_description': game_details.get('detailed_description'),
                'steam_supported_languages': game_details.get('supported_languages'),
                'steam_header_image': game_details.get('header_image'),
                'steam_pc_requirements_minimum': min_req,
                'steam_pc_requirements_recommended': recm_req,
                'steam_developers': ", ".join(game_details.get('developers',[])),
                'steam_publishers': ", ".join(game_details.get('publishers',[])),
                'steam_price_overview': game_details.get('price_overview'),
                'steam_screenshots': list(map(lambda x: x.get("path_full"),game_details.get('screenshots',[]))), # url list
                'steam_movies': list(map(lambda x: x.get("mp4", {}).get("max"),game_details.get('movies',[]))), # url list
                'steam_genres': list(map(lambda x: x.get("description"),game_details.get('genres',[]))), # string list,
                'steam_categories': list(map(lambda x: x.get("description"),game_details.get('categories',[]))), # string list, dont use this
                'steam_background': game_details.get('background'), # string list, dont use this
            }
        else:
            print("未找到游戏详情")
    except Exception as e:
        # 请求发生错误
        print(f"请求发生错误: {e}")

# 使用示例
if __name__ == "__main__":
    results = get_steam_app_ids()
    for result in results:
        print(f"AppID: {result['app_id']}")
        print(f"文章标题: {result['article_title']}")
        print("---")
        d = get_game_details(result['app_id'])
        print(d)