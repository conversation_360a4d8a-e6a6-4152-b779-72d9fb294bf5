from mega import Mega
import sys

import mongo.MongoDBHelper as MongoDBHelper
import traceback

def download(dest_dir="/Volumes/aigo2/rss-game/"):
    mongo = MongoDBHelper.MongoDBHelper()
    mongo.connect()
    list = mongo.find_data('my_game_download', {"status": "Not Started"})
    for item in list:
        url = item["download_url"]
        app_id = item["steam_app_id"]
        try:
            info = download_mega(url,dest_dir)
            mongo.update_data("my_game_downlaod",
                              {"app_id": app_id},
                              {"status": "Finished", "path": dest_dir + info['name'], "size": info['size']})
        except Exception as e:
            print(traceback.format_exc())
            mongo.update_data("my_game_downlaod", {"app_id": app_id}, {"status": e})
    mongo.close()

def download_mega(url, dest_path=None):
    mega = Mega()
    info = mega.get_public_url_info(url)
    total_size = info['size']

    print(f"start downloading {info['name']}！ size: {info['size']}")
    # 开始下载
    mega.download_url(url, dest_path)
    return info


# 使用示例
if __name__ == "__main__":
    download()
    # download_mega('https://mega.co.nz/#!EcQXgRLI!o-Sx2N7W0zDfIicAchqbgGqpHig47ikKWMV-wV7J238', '/Volumes/aigo2/rss-game/')

