import datetime as datetime
import sys

import requests
from bs4 import BeautifulSoup
from wordpress import api, API
import json
import cat_dict
import cat_keyword
from nudenetImpl import NudeDetectorProcessor
from wordpressHelper.nginx__file_struct import NginxProcess
from urllib.parse import urlparse
import os
from PIL import Image
import math
import pytz
import datetime
from hangul_utils import split_syllables

IST = pytz.timezone('Asia/Shanghai')

keywords = cat_keyword.KEYWORD
cat_no = cat_dict.CAT_LIST
cat_no_map = {obj['name']: obj['term_id'] for obj in cat_no}
keywords = list(map(lambda item: [item[0],item[1],cat_no_map[item[1]]], keywords))



class WordPressUtil:
    # wpapi = API(
    #     url="http://192.168.0.33:6980",
    #     consumer_key='',
    #     consumer_secret='',
    #     api="wp-json",
    #     version='wp/v2',
    #     wp_user="jiml",
    #     wp_pass="Litiantong1995;",
    #     basic_auth=True,
    #     user_auth=True,
    # )

    wpapi = API(
        url="http://jim-nas.internal.site:28889",
        consumer_key='',
        consumer_secret='',
        api="wp-json",
        version='wp/v2',
        wp_user="jiml",
        wp_pass="19950102",
        basic_auth=True,
        user_auth=True,
    )
    n = NginxProcess()

    def write_article(self, urls, title, cats=None, tags=None):
        if len(urls) == 0:
            return
        urls = list(map(lambda x: urlparse(x).path, urls))
        url0 = "/thumb" + urls[0]
        str_url = ','.join(urls)
        content = f"""
        <img style="height: 0;" src="{url0}" />
        [myImg src="{str_url}"]
        """
        return {
            "title": title,
            "content": content,
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "format": "standard",
            "status": "publish",
            "tags": tags,
            "categories": cats
        }

    def read(self):
        post = self.wpapi.get("posts")
        j = json.loads(post.text)
        # print(j)

    def post(self, article):
        if article is None:
            return
        res = self.wpapi.post("posts", article)
        return res
        # self.wpapi.post("posts", {
        #     "title": "test",
        #     "content": "test content"
        # })
    def new_media(self, img_url):
        img_filename = img_url.split("/")[-1]

        response = requests.get(img_url)

        files = {'file': response.content}
        headers = {'Content-Disposition': 'attachment; filename="file.jpg"'}
        response = self.wpapi.post("media",data={},files=files, headers={
            "Content-Disposition": f'attachment; filename="{img_filename}"',
            'Content-Type': 'image/jpeg'
        })

        print(response)

    def add_cat_by_keyword(self, title):
        cats = []
        for keyword in keywords:
            for key in keyword[0].split(" "):
                if key in title:
                    cats.append(keyword[2])
        return cats

    def update_post_tags(self, post_id, tag_ids):
        """
        为已发布的文章更新标签

        参数:
            post_id (int): 文章ID
            tag_ids (list): 标签ID列表

        返回:
            dict: WordPress API的响应结果
        """
        try:
            # 准备更新数据
            update_data = {
                "tags": tag_ids
            }

            # 使用API直接更新文章
            endpoint = f'posts/{post_id}'
            response = self.wpapi.put(endpoint, update_data)

            print(f"Successfully updated tags for post {post_id}")
            return response

        except Exception as e:
            print(f"Error updating tags for post {post_id}: {str(e)}")
            return None

    def get_post_images(self, post_id):
        """
        获取文章内容并提取所有图片src

        参数:
            post_id (int): 文章ID

        返回:
            list: 图片src列表
        """
        try:
            # 获取文章内容
            endpoint = f'posts/{post_id}'
            post = self.wpapi.get(endpoint)

            if not post:
                print(f"No content found for post {post_id}")
                return []

            # 使用BeautifulSoup解析文章内容
            content = post.content
            soup = BeautifulSoup(content, 'html.parser')

            # 查找所有img标签
            images = soup.find_all('a')

            # 提取src属性
            image_srcs = []
            for img in images:
                src = img.get('href')
                if src:
                    image_srcs.append(src)

            image_srcs = list(map(lambda x: self.wpapi.url + x.strip('\\"\\"').replace('\\/', '/'), image_srcs))
            print(f"Found {len(image_srcs)} images in post {post_id}")

            image_list = []
            for src in image_srcs:
                response = requests.get(
                    url=src,
                    auth=('jiml','19950102'),  # basic auth认证
                    headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
                )
                # nparr = np.frombuffer(response.content, np.uint8)
                # # 解码图片
                # mat = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                image_list.append(response.content)
            return image_list
        except Exception as e:
            print(f"Error getting images from post {post_id}: {str(e)}")
            return []

    def get_or_create_tag(self, tag_name):
        # 检查标签是否已存在
        tags = self.wpapi.get('tags', params={'search': tag_name}).json()
        if tags:
            return tags[0]['id']

        # 创建新的标签
        new_tag = {
            'name': tag_name
        }
        response = self.wpapi.post('tags', data=new_tag)
        if response.status_code == 201:
            print(f"新标签 '{tag_name}' 创建成功！")
            return response.json()['id']
        else:
            print(f"新标签 '{tag_name}' 创建失败。")
            return None

    def batch_add_nudenet_tags(self,single=True, per_page=20):
        all_post_ids = []
        page = 1

        while True:
            try:
                # 获取文章列表
                posts = self.wpapi.get('posts', params={
                    'per_page': per_page,
                    'page': page,
                    'status': 'publish'
                })
                content = json.loads(posts.content)
                total = int(posts.headers.get("X-WP-TotalPages"))
                if page > total:
                    break
                # 提取文章ID
                post_ids = [post['id'] for post in content]
                for id in post_ids:
                    try:
                        img_list = self.get_post_images(id)
                        n = NudeDetectorProcessor()
                        res = n.detect_url_batch(img_list)
                        tags = []
                        for k, v in res.items():
                            if v > 0.5:
                                tag_id = self.get_or_create_tag(k)
                                tags.append(tag_id)
                        self.update_post_tags(id, tags)
                    except:
                        pass

            except Exception as e:
                print(f"Error getting posts for page {page}: {e}")
                return None, None
            page += 1
            if single:
                break
        return
    def sync_wordpress_article(self, url, process_full=False, cats=[], date = datetime.datetime.now(),
                               startDate = (datetime.datetime.now() - datetime.timedelta(days=1))):
        n = NginxProcess()
        a = n.loadFolders(url, not process_full, date, startDate)
        if not process_full:
            a = list(filter(lambda s: s["isCurrentDay"] is not False, a))
        i=0
        # nudenet = NudeDetectorProcessor()
        for aa in a:
            re = n.processFolder(aa["href"], aa["content"])
            cats2 = []
            cats2.extend(cats)
            cats2.extend(self.add_cat_by_keyword(re["title"]))
            body = w.write_article(re["img_urls"], re["title"], cats=cats2)
            article = w.post(body)
            # self.add_nudenet_model_as_tag(article["id"], nudenet)
            print(f"Done:{i}/ {re['title']}")
            i +=1

    def mount(self):
        smbpath = 'smb://*************/MySSD'
        os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
        smbpath = 'smb://*************/MyHDD'
        os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
        print("Done")

    def compress_img_folder(self, dir_path=r'/Volumes/MyHDD/tg-image/', target_path = r'/Volumes/MySSD/thumb/tg-image/'):
        os.makedirs(target_path, exist_ok=True)
        dir_list = os.listdir(dir_path)
        for folder in dir_list:
            sub_folder = os.path.join(dir_path, folder)
            tar_sub_folder = os.path.join(target_path, folder)
            if not os.path.isdir(sub_folder):
                continue
            self.compress_img(sub_folder, tar_sub_folder)

    def compress_img(self, dir_path=r'/Volumes/MyHDD/tg-image/', target_path = r'/Volumes/MySSD/thumb/tg-image/'):
        formats = ('.jpg', '.jpeg', '.png')
        target_size = 50 * 1024  # bytes

        os.makedirs(target_path, exist_ok=True)
        dir_list2 = []
        filesystem_encoding = sys.getfilesystemencoding()

        # 使用 os.scandir() 列出文件和文件夹，并处理文件名编码
        for entry in os.scandir(dir_path):
            filename = entry.name
            dir_list2.append(filename)


        dir_list = set(os.listdir(dir_path))
        tar_list = os.listdir(target_path)


        for x in tar_list:
            if x in dir_list:
                dir_list.remove(x)
            if x in dir_list2:
                dir_list2.remove(x)

        dir_list2 = list(map(lambda x: split_syllables(x),dir_list2))

        for folder in dir_list:
            target_folder_path = os.path.join(target_path,folder)
            folder_path = os.path.join(dir_path,folder)
            os.makedirs(target_folder_path, exist_ok=True)

            if not os.path.isdir(folder_path):
                continue

            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path,file)
                target_file_path = os.path.join(target_folder_path , file)
                if file.startswith("."):
                    continue
                if os.path.splitext(file)[1].lower() in formats:
                    try:
                        file_size = os.path.getsize(file_path)
                        picture = Image.open(file_path)
                        resize_rate = min(1.0, math.sqrt(float(target_size) / file_size))

                        picture = picture.resize((int(picture.width * resize_rate), int(picture.height * resize_rate)))

                        picture.save(target_file_path, optimize=True)
                    except:
                        continue



if __name__ == '__main__':
    date = datetime.datetime.now()
    startDate = (datetime.datetime.now() - datetime.timedelta(days=1))
    if len(sys.argv) > 1:
        date = datetime.datetime.strptime(sys.argv[1], "%Y-%m-%d_%H:%M:%S")
    if len(sys.argv) > 2:
        startDate = datetime.datetime.strptime(sys.argv[2], "%Y-%m-%d_%H:%M:%S")
    w = WordPressUtil()
    # w.sync_wordpress_article("http://192.168.0.33:2345/Photos/girldreamy/", False, [13])
    # w.sync_wordpress_article("http://192.168.0.33:2345/Photos/everia/", True, [11])
    # w.sync_wordpress_article("http://192.168.0.33:2345/Photos/xasiat/", True, [12])
    # w.sync_wordpress_article("http://192.168.0.33:2345/TG-Image/", True, [10])
    today = datetime.date.today().strftime("%Y-%m")
    w.mount()
    w.compress_img_folder(r'/Volumes/MyHDD/tg-blog-image/everia/', r'/Volumes/MySSD/thumb/tg-blog-image/everia/')
    w.compress_img_folder(r'/Volumes/MyHDD/tg-blog-image/girldreamy/', r'/Volumes/MySSD/thumb/tg-blog-image/girldreamy/')
    w.compress_img_folder(r'/Volumes/MyHDD/tg-blog-image/xasiat/', r'/Volumes/MySSD/thumb/tg-blog-image/xasiat/')
    w.compress_img_folder(r'/Volumes/MyHDD/tg-image/', r'/Volumes/MySSD/thumb/tg-image/')
    # w.compress_img_folder(r'/Volumes/MyHDD/Photos/', r'/Volumes/MySSD/thumb/Photos/')
    # w.compress_img(r'/Volumes/MyHDD/TG-Image/', r'/Volumes/MySSD/thumb/tg-image-old/')
    #

    w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/girldreamy/{today}/", False, [13], date= date, startDate=startDate)
    w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/everia/{today}/", False, [11], date= date, startDate=startDate)
    w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/xasiat/{today}/", False, [12], date= date, startDate=startDate)
    w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-image/{today}/", False, [10], date= date, startDate=startDate)


    # w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/girldreamy/{today}/", False, [13], date= date)
    # w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/everia/{today}/", False, [11], date= date)
    # w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-blog-image/xasiat/{today}/", False, [12], date= date)
    # w.sync_wordpress_article(f"http://jim-nas.internal.site:2345/tg-image/{today}/", False, [10], date= date)

    w.batch_add_nudenet_tags()

    # a = n.loadImageUrls("http://192.168.0.33:2345/Photos/girldreamy/An%20Ko%20%E6%9D%8F%E5%AD%90%20Vol.003%20%E5%86%85%E8%B4%AD%E6%97%A0%E6%B0%B4%E5%8D%B0%20%E4%BE%8B%E8%A1%8C%E4%BD%93%E6%A3%80%20A/")
