import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
current_time = datetime.now()


class NginxProcess:

    def loadImageUrls(self, parent_url):

        anchors = self.loadFolders(parent_url)
        url_list = [anchor['href'] for anchor in anchors]

        title = ''
        if len(url_list) > 0:
            url_list.pop(0)
            title = anchors[0]['title']
        return {
            "title": title,
            "img_urls": url_list
        }

    def processFolder(self, url, title):
        return self.loadImageUrls(url)
    def loadFolders(self, url,reverse=False, date = current_time, previous_day = None):
        response = requests.get(url, headers={
            "Accept-Encoding": "identity",  # 禁用响应的压缩编码
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"  # 指定所需的语言
        })
        response.encoding = response.apparent_encoding  # 设置响应的字符编码为自动推测的编码
        html_data = response.content
        soup = BeautifulSoup(html_data, 'html.parser')

        if previous_day is None:
            previous_day = date - timedelta(days=1)

        title = soup.find('h1').getText()[9:]
        title_path = title.split('/')
        if len(title_path[len(title_path)-1])==0:
            title = title_path[-2]
        else:
            title = title_path[-1]


        anchors = []
        for a in soup.find_all('a'):
            href = a.get('href')
            content = a.get_text()
            file_info = a.next_sibling.strip().split()

            # 提取日期、时间和大小
            if len(file_info) >= 3:
                file_date = file_info[0]
                file_time = file_info[1]
                file_size = file_info[2]
                timestamp = datetime.strptime(f"{file_date} {file_time}", "%d-%b-%Y %H:%M")
                is_current_day = (previous_day < timestamp < date)
            else:
                file_size = ''
                timestamp = None
                is_current_day = False

            anchor = {
                'href': url + href,
                'content': content,
                'date': timestamp,
                'size': file_size,
                'isCurrentDay': is_current_day,
                'title': title
            }
            anchors.append(anchor)
        if len(anchors) > 0:
            anchors.pop(0)
        anchors = sorted(anchors, key=lambda x: x['date'], reverse=reverse)
        return anchors
