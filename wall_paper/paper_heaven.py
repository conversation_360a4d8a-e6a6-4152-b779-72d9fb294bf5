import datetime
import os

import feedparser
import xml.etree.ElementTree as ET
from mongo.MongoDBHelper import MongoDBHelper
import requests


def read_feed(save_dir):
    url = "http://192.168.0.2:1200/wallhaven/categories=010&purity=100&atleast=1920x1080&ratios=landscape&sorting=date_added/needDetails=Y"  # 替换为实际的RSS/XML URL
    feed = feedparser.parse(url)
    c = MongoDBHelper()
    print(f"wallpaper haven len: {len(feed.entries)}")
    for entry in feed.entries:
        db_title = c.my_find_data('wall_paper', {"title": entry.title})
        if db_title is not None and len(db_title) > 0:
            continue
        root = ET.fromstring(entry['summary'])
        src = root.attrib['src']
        entry_title = src.split('/')[-1]
        img_data = requests.get(src).content
        img_path = os.path.join(save_dir, entry_title)
        try:
            with open(img_path, "wb") as f:
                f.write(img_data)
            c.my_insert_data('wall_paper', {
                        "link": entry.link,
                        "title": entry.title,
                        "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "status": "Success",
                        "path": img_path
                    })
            print(f"download wallpaper{img_path}")
        except Exception as e:
            print(f"download failed: {e}")
            c.my_insert_data('wall_paper', {
                "link": entry.link,
                "title": entry.title,
                "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": e,
                "path": img_path
            })



def run():
    read_feed('/Volumes/新加卷/wallPaper/anime/wallhaven')


if __name__ == '__main__':
    run()

