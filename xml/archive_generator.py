from datetime import datetime
import xml.etree.ElementTree as ET
import xml.dom.minidom
import os
import re

def statistics(thred, do, path):
    list_of_files = os.listdir(path)
    list_of_files = list(filter(lambda x: not x.startswith("._"), list_of_files))
    keyword_dict = {}

    ban_keyword = {'Telegraph', '2023'}

    for i in list_of_files:
        split_array = re.split(r'[ \\_\-()\[\]]+', i)
        split_array = list(filter(lambda x: len(x)>2 and x not in ban_keyword, split_array))
        for word in split_array:
            if word in keyword_dict:
                keyword_dict[word] += 1
            else:
                keyword_dict[word] = 1
    sorted_dict = dict(sorted(keyword_dict.items(), key=lambda item: item[1], reverse=True))
    for key, value in sorted_dict.items():
        if value > thred:
            if not do:
                print(key,value)
            else:
                createFavourite(key, path)

def createFavourite(keyword, path):
    list_of_files = os.listdir(path)
    list_of_files = list(filter(lambda x: not x.startswith("._"), list_of_files))
    list_of_files = list(filter(lambda x: keyword in x, list_of_files))
    list_of_files = list(map(lambda x: path + x, list_of_files))
    createXml(keyword, list_of_files)


def createXml(title, path_list):

    file_path = '/Users/<USER>/.local/share/jellyfin/data/collections/' + title + ' [boxset]'
    if not os.path.exists(file_path):
        os.mkdir(file_path)
    # 创建根节点
    root = ET.Element("Item")

    # 添加子元素
    added = ET.SubElement(root, "Added")
    added.text = datetime.now().strftime('%m/%d/%Y %H:%M:%S')

    lockdata = ET.SubElement(root, "LockData")
    lockdata.text = "true"

    localtitle = ET.SubElement(root, "LocalTitle")
    localtitle.text = title

    displayorder = ET.SubElement(root, "DisplayOrder")
    displayorder.text = "PremiereDate"

    collectionitems = ET.SubElement(root, "CollectionItems")
    for path in path_list:
        collectionitem = ET.SubElement(collectionitems, "CollectionItem")
        path_elem = ET.SubElement(collectionitem, "Path")
        path_elem.text = path

    # 美化输出
    xml_string = ET.tostring(root, encoding="utf-8", method="xml")
    xml_string = xml_string.decode()
    temp = xml.dom.minidom.parseString(xml_string)
    new_xml = temp.toprettyxml()

    # 生成XML文件
    with open(file_path+"/collection.xml", "w", encoding="utf-8") as file:
        file.write(new_xml)


if __name__ == '__main__':

    statistics(5,False,r'/Volumes/新加卷/telegra.ph/')
    # statistics(5,False,r'/Volumes/新加卷/eh/')

    # createFavourite('柒柒不可爱')


    # createXml('test', ["/Volumes/新加卷/telegra.ph/[Bimilstory]_Nara_-_Vol.25_Pure_daily_life_–_Telegraph",
    #              "/Volumes/新加卷/telegra.ph/[Bimilstory]_Nara_Vol.05_After_the_flight_102P_–_Telegraph",
    #              "/Volumes/新加卷/telegra.ph/[Bimilstory]_Dasol_(다솔)_-_Debut_Work_Secret_secretary_127P_–_Telegraph"])
