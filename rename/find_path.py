import glob
import os
import sys
import re
file_dir = '/Volumes/新加卷/telegra.ph/*'
from pymongo import MongoClient
from fuzzywuzzy import process, fuzz
from collections import defaultdict
from functools import cmp_to_key


def suffix_array_lcs(texts, min_length, max_length):
    """
    使用后缀数组找出高频的最长公共子串,子串长度在指定范围内。

    参数:
    texts (list of str) - 文本列表
    min_length (int) - 最小子串长度
    max_length (int) - 最大子串长度

    返回:
    dict - 键为公共子串,值为出现频次的字典
    """

    def compare(a, b):
        """
        比较两个后缀的字典序
        """
        i, j = 0, 0
        while i < len(a) and j < len(b):
            if a[i] != b[j]:
                return a[i] - b[j]
            i += 1
            j += 1
        return len(a) - len(b)

    # 构建联合后缀数组
    suffixes = []
    for i, text in enumerate(texts):
        suffixes.extend((c, i, idx) for idx, c in enumerate(text))
    suffixes.sort(key=cmp_to_key(compare))

    # 找出最长公共子串
    lcs_freq = defaultdict(int)
    prev_suffix = None
    for suffix, i, idx in suffixes:
        if prev_suffix is not None:
            lcs = ''
            j = 0
            while j < min(len(prev_suffix), len(suffix)) and prev_suffix[j] == suffix[j]:
                lcs += prev_suffix[j]
                j += 1
            if min_length <= len(lcs) <= max_length:
                lcs_freq[lcs] += 1
        prev_suffix = suffix

    return lcs_freq
def getData(date, collection):
    if date is None:
        data = collection.find()
    else:
        data = collection.find({"date": date})
    return list(data)


def remove_char(string):
    special_chars = "!@#$%^&*()_+[]{};:,./<>?\|`~-='"

    for char in special_chars:
        string = string.replace(char, "")
    return string

def oneTimeRun():
    client = MongoClient("mongodb://localhost:27017")
    keyword_dict = {}
    thred = 5
    do = False
    ban_keyword = {'Telegraph', '2023'}
    try:
        db = client["mydatabase"]
        collection = db["tele_img"]
        dbRows = getData(None, collection)
        dbRows = list(map(lambda x: x["title"],dbRows))

        for i in dbRows:
            split_array = re.split(r'[ \\_\-()\[\]]+', i)
            split_array = list(filter(lambda x: len(x) > 2 and x not in ban_keyword, split_array))
            for word in split_array:
                if word in keyword_dict:
                    keyword_dict[word] += 1
                else:
                    keyword_dict[word] = 1
        sorted_dict = dict(sorted(keyword_dict.items(), key=lambda item: item[1], reverse=True))
        for key, value in sorted_dict.items():
            if value > thred:
                if not do:
                    print(key, value)
        min_length = 2  # 2个中文字符或5个英文字符
        max_length = 10  # 10个中文字符或20个英文字符
        # result = longest_common_substring(texts, min_length, max_length)

        # 输出结果
        # for substring, freq in sorted(result.items(), key=lambda x: x[1], reverse=True):
        #     print(f"{substring}: {freq}")
    finally:
        client.close()



if __name__ == '__main__':
    oneTimeRun()

