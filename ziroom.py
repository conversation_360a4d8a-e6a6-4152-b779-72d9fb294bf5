import rpa as r
from bs4 import BeautifulSoup
import os
import datetime
import random
import glob
import json
from PIL import Image

import pytesseract
import xlsxwriter


now = datetime.datetime.now().strftime('%Y-%m-%d')

prefix = 'https://www.ziroom.com/z/z1-'

class Rental:
    def __init__(self, name, age):
        self.name = name
        self.age = age


def search_page(link, sheet, suffix, startRow):
    r.url(link)
    r.wait()
    pageNo = r.read('//*[@id="page"]/span[2]')
    pageNo = int(pageNo[1:-1])
    row = startRow
    row = search(link, row, sheet)
    for i in range(2, pageNo+1):
        link = prefix + 'p' + str(i) + suffix
        row = search(link, row, sheet)






def search(link, startRow, sheet):
    r.url(link)
    r.wait(10)
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    main_content = soup.find('div', {"class": "Z_list-box"})
    items = main_content.find_all('div', {"class": "item"})
    if len(items) == 0:
        return startRow



    urls = list()
    row = startRow
    for item in items:
        title = item.find('h5',{"class": "title"}).getText()
        desc = item.find('div', {"class": "desc"}).getText()
        desc1,desc2 = list(filter(lambda x: len(x)>0, desc.split('\n')))
        desc2 = desc2.strip()
        square, floor = desc1.split('|')
        url = 'https://' + item.find('h5',{"class": "title"}).find('a')['href'].strip('/')
        urls.append(url)
        sheet.write_row(row, 0, [title, square, floor, desc2, url])
        row+=1

    row = startRow
    for url in urls:
        r.url(url)
        r.wait()
        file_name = 'room#' + str(row) + '.png'
        price_name = 'price#' + str(row) + '.png'
        r.snap('/html/body/div[1]/section/aside/div[1]', price_name)
        r.snap('page', file_name)
        sheet.insert_image(row, 5, price_name)
        row+=1

    return row




def init_excel():
    book = xlsxwriter.Workbook('test' + now + '.xlsx')
    sheet = book.add_worksheet("test")
    sheet.set_default_row(height=45)
    sheet.set_column(0, 0, width=36)
    sheet.set_column(3, 5, width=40)


    # 定义一下两列的name,再把要匹配的昵称填充进去。
    sheet.write("A1", "name")
    sheet.write("B1", "square")
    sheet.write("C1", "floor")
    sheet.write("D1", "distance")
    sheet.write("E1", "url")
    return book,sheet


def detail_search(rental):
    pass


if __name__ == '__main__':
    r.init(True)
    book,sheet = init_excel()
    lists = [
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E5%8D%8E%E7%9B%9B%E5%AE%B6%E5%9B%AD&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E9%83%BD%E6%99%AF%E8%8B%91&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E4%B8%AD%E9%91%AB%E5%98%89%E5%9B%AD&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E5%8D%8E%E4%B8%A5%E9%87%8C&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E5%8D%8E%E4%B8%A5%E9%87%8C&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E5%8D%8E%E4%B8%A5%E5%8C%97%E9%87%8C&isOpen=1',
        'https://www.ziroom.com/z/z1/?p=a1|2|3|4&qwd=%E8%A3%95%E4%B8%AD%E8%A5%BF%E9%87%8C&isOpen=1'


    ]
    row = 1
    try:
        for link in lists:
            row = search(link, row, sheet)
    finally:
        book.close()
        r.close()
''
