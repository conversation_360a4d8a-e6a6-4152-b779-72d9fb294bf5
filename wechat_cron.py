
import math

import rpa as r
import datetime
from bs4 import BeautifulSoup
import random
from PIL import Image
import os


greeting_words = []

def bili():
    r.url('https://space.bilibili.com/417567377/dynamic')
    r.wait(5)
    page = r.read("page")
    title = r.read('//*[@id="page-dynamic"]/div[1]/div/div[1]/div[1]/div/div/div[3]/div/div/div/a/div[2]/div[1]')
    author = r.read('//*[@id="h-name"]')
    soup = BeautifulSoup(page, features="html.parser")
    a = soup.find_all(class_='bili-dyn-card-video')[0]
    link = a['href']
    return title, author, link

def wechat():
    title, author, link = bili()
    r.click(182,1418)
    r.click(1300, 494)
    r.type(1400, 416,'xiaowei')
    r.keyboard('[enter]')
    r.keyboard('[enter]')
    r.click(1430,480)

    r.clipboard(greeting())
    r.keyboard('[ctrl][v]')
    r.keyboard('[enter]')

    r.clipboard('今天给您推荐视频：')
    r.keyboard('[ctrl][v]')
    r.keyboard('[enter]')

    r.clipboard(author + " - " + title)
    r.keyboard('[ctrl][v]')
    r.keyboard('[enter]')

    r.clipboard(link)
    r.keyboard('[ctrl][v]')
    r.keyboard('[enter]')

    r.click(2156,390)
    return



def greeting():
    words = list()
    with open('greeting_words.txt', 'rt', encoding = 'utf-8') as log:
        for line in log.readlines():
            if len(line) > 3:
                words.append(line)
    return random.choice(words)



if __name__ == '__main__':
    r.init(True)
    wechat()
    r.close()