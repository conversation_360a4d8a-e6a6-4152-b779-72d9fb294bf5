import asyncio
from pyrogram import Client
from pyrogram.errors import FloodWait, ChannelInvalid, PeerIdInvalid, UserNotParticipant

# --- 配置你的 Telegram API 凭据 ---
# 请替换为你在 my.telegram.org 获取到的 API ID 和 API Hash

api_hash= '792cfdcecaeab811010c87c98c200ec7'
api_id= '21786636'
API_ID = api_id # 例如: 12345678
API_HASH = api_hash # 例如: "abcdef0123456789abcdef0123456789"

# 用于存储会话的名称。你可以随意命名，这将生成一个名为 'my_session.session' 的文件
SESSION_NAME = "test"

async def get_channel_id_and_access():
    """
    第一步：登录并获取所有频道ID
    第二步：根据用户选择的ID访问私人频道内容
    """
    app = Client(SESSION_NAME, api_id=API_ID, api_hash=API_HASH)

    try:
        print("正在连接到 Telegram...")
        await app.start()
        print("连接成功！")

        print("\n正在获取您已加入的所有聊天 (包括群组和频道)...")
        channels_found = {}
        list = []
        async for dialog in app.get_dialogs():
            list.append(dialog)
            # Pyrogram 将频道识别为 'channel' 类型
            if dialog.chat.type.value == "channel":
                channels_found[str(dialog.chat.id)] = dialog.chat.title
                print(f"  ID: {dialog.chat.id}, 名称: {dialog.chat.title}")

        if not channels_found:
            print("未找到任何已加入的频道。请确保您已加入一些频道。")
            await app.stop()
            return

        print("\n-------------------------------------")
        print("请从上方列表中选择您想要访问的私人频道ID。")
        print("例如，输入: -100123456789")
        print("-------------------------------------")

        selected_channel_id_str = '-1002393003114'

        try:
            # 尝试将输入的ID转换为整数
            selected_channel_id = int(selected_channel_id_str)
        except ValueError:
            print(f"'{selected_channel_id_str}' 不是一个有效的数字ID。请确保输入正确的数字ID。")
            await app.stop()
            return

        print(f"\n正在尝试访问频道 (ID: {selected_channel_id})...")

        try:
            # 获取频道信息
            chat = await app.get_chat(selected_channel_id)



            # 获取最新消息
            print("\n--- 最近 5 条消息 ---")
            messages_count = 0
            async for message in app.get_chat_history(selected_channel_id, offset_id=6168, limit=5,reverse=True):
                messages_count += 1
                # 频道消息没有 from_user，因为是频道本身发送的
                text_content = message.text or message.caption or '[非文本消息]'
                print(f"  [{message.date.strftime('%Y-%m-%d %H:%M:%S')}] {text_content[:100]}{'...' if len(text_content) > 100 else ''}") # 显示前100字符

            if messages_count == 0:
                print("  该频道没有找到任何消息，或者您的权限不足。")

            print("\n--- 注意 ---")
            print("作为私人频道的普通成员，您通常只能获取消息历史。")
            print("您无法向频道发送消息或获取完整的成员列表 (除非您是管理员)。")
            print("-------------------------------------")

        except UserNotParticipant:
            print(f"错误: 您似乎不是ID为 {selected_channel_id} 的私人频道的成员。请确保您已加入该频道。")
        except ChannelInvalid:
            print(f"错误: 频道ID {selected_channel_id} 无效或不存在。")
        except PeerIdInvalid:
            print(f"错误: 输入的ID {selected_channel_id} 无效。请检查ID是否正确。")
        except Exception as e:
            print(f"访问频道时发生未知错误: {e}")

    except FloodWait as e:
        print(f"您被 Telegram 暂时限制了，请等待 {e.value} 秒后重试。")
    except Exception as e:
        print(f"启动 Pyrogram 客户端时发生错误: {e}")
    finally:
        print("\n正在断开连接...")
        await app.stop()
        print("脚本执行完毕。")

if __name__ == "__main__":
    # 在运行前，请务必替换上面的 API_ID 和 API_HASH 为你的真实凭据！
    if API_ID == 'YOUR_API_ID_HERE' or API_HASH == "YOUR_API_HASH_HERE":
        print("错误: 请在脚本顶部替换 YOUR_API_ID_HERE 和 YOUR_API_HASH_HERE 为你的真实 API 凭据。")
    else:
        asyncio.run(get_channel_id_and_access())