import datetime

import requests
import os
import pyrogram
from pyrogram import Client, filters
from MyRPA.util.MongoDBHelper import MongoDBHelper
# import telegram_img_download
import rpa as r
from collections import defaultdict
import concurrent.futures

# 获取Telegram Bot API的token
# bot_token = '**********************************************'
# 准备下载目录
download_dir = '/Volumes/新加卷/tg-image'
# download_dir = '/Users/<USER>/Desktop/test/'
api_hash= '792cfdcecaeab811010c87c98c200ec7'
api_id= '21786636'

# get login code: visit https://web.telegram.org/?legacy=1#/im?p=u777000_3881963525695541602
app = pyrogram.Client(
        "media_downloader",
        api_id=api_id,
        api_hash=api_hash,
        # bot_token=bot_token,
        proxy={
            "hostname": "127.0.0.1",
            "port": 7890,
            "scheme": "socks5"
        })

def get_dict(channel, object_list):
    result_dict = defaultdict(list)
    result_dict2 = defaultdict(list)
    dict3 = {}
    for obj in object_list:
        result_dict[obj.media_group_id].append(obj)
    result_dict = dict(result_dict)

    for key, messages in result_dict.items():
        try:
            comments = list(app.get_discussion_replies(channel, messages[0].id))
            comments.sort(key=lambda obj: obj.id)
            messages.extend(comments)
        except Exception as e:
            pass
        message_info_f = list(filter(lambda x: (x.media is not None and x.media.name == 'PHOTO') or (x.sender_chat is not None and x.sender_chat.photo is not None), messages))
        if len(message_info_f) < 20:
            continue
        result_dict2[key] = message_info_f

    return result_dict2


def delete_files_starting_with_underscore(directory):
    # 遍历指定的目录
    for filename in os.listdir(directory):
        # 检查文件名是否以 "_" 开头
        if filename.startswith('_'):
            file_path = os.path.join(directory, filename)
            # 检查路径是否为文件（避免删除目录）
            if os.path.isfile(file_path):
                # 删除文件
                os.remove(file_path)
def getTitle(post_id, chatId,replyMsgId):
    r.url('https://www.bing.com')
    # r.url(f'https://web.telegram.org/a/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3D{channel}%26post%3D{post_id}')
    r.url(f'https://web.telegram.org/a/#{chatId}_{replyMsgId}')
    # title = r.read(f'//*[@id="message-{post_id}"]/div[3]/div/div[1]/div[2]')
    title = r.read(f'//*[@id="MiddleColumn"]/div[4]/div[1]/div[2]')
    t = 0
    while (title is None or title == '') and t < 5:
        r.wait()
        # title = r.read(f'//*[@id="message-{post_id}"]/div[3]/div/div[1]/div[2]')
        title = r.read(f'//*[@id="MiddleColumn"]/div[4]/div[1]/div[2]')
        t+=1
    if title.find('标签') != -1:
        title = title[:title.find('标签')]
    return title


def get_img_message_id(channel, def_msg_id, debug=False):
    print(f"start process channel{channel}")
    c = MongoDBHelper()
    videos = c.my_find_data('tele_img', {"channel": channel, "tg_msg_id": {"$ne": None}, "link": None})
    init_load = True
    message_info = None
    if debug or videos is None or len(videos) == 0:
        last_msg_id = def_msg_id + 1
    else:
        last_msg_id = max(v["tg_msg_id"] for v in videos) + 1
    with app:
        while message_info is None or message_info[len(message_info)-1].empty is None:
            init_load = False
            message_ids = [last_msg_id + x for x in range(100)]
            message_info = list(app.get_messages(channel, message_ids=message_ids))
            last_msg_id = message_info[len(message_info)-1].id

            group_dict = get_dict(channel, message_info)
            for _, messages in group_dict.items():
                try:
                    caption = next((obj.caption for obj in messages if obj.caption), 'UnTitled')
                    title = caption
                    if title == 'UnTitled':

                        chatId,replyMsgId = next(((obj.chat.id, obj.reply_to_message_id) for obj in messages if obj.chat and obj.chat.id and obj.reply_to_message_id), 'nil')
                        title = getTitle(messages[0].id, chatId,replyMsgId)
                        if title is None:
                            continue
                    title = title.replace('#', '-').replace(' ', '').replace(' -Coser', '').replace('-Cosplay', '')
                    title = title.lstrip('!@#-_ ')
                    if title.find('图片') != -1:
                        title = title[:title.find('图片')]
                    if title.find('视频') != -1:
                        title = title[:title.find('视频')]
                    if title is None or title == '':
                        continue
                    title = channel + "-_-" + title
                    print(f"start download {title}.")
                    dir_name = download_dir + '/' + datetime.datetime.now().strftime('%Y-%m') + "/" + title
                    os.makedirs(dir_name, exist_ok=True)


                    # def download_media(media, index):
                    #     if media.photo is None:
                    #         return
                    #     file_path = os.path.join(dir_name, f"{'{:06d}'.format(index)}.jpg")
                    #     app.download_media(media.photo, file_path)
                    #
                    # with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                    #     futures = [executor.submit(download_media, media, i+1) for i, media in enumerate(messages)]
                    #     concurrent.futures.wait(futures)

                    i = 0
                    for media in messages:
                        i+=1
                        if media.photo is not None:
                            file_path = os.path.join(dir_name, f"{'{:06d}'.format(i)}.jpg")
                            app.download_media(media.photo, file_path)
                        # if media.photo is None:
                        #     continue
                        # file_path = os.path.join(dir_name, f"{'{:06d}'.format(i)}.jpg")
                        # app.download_media(media.photo, file_path)


                    delete_files_starting_with_underscore(dir_name)
                    # telegram_img_download.load_page2(url, title, download_dir)
                    c.my_insert_data('tele_img', {
                        "link": None,
                        "title": title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": messages[0].id,
                        "status": "Success",
                        "path": dir_name
                    })

                except Exception as e:
                    print(e)
                    c.my_insert_data('tele_img', {
                        "link": None,
                        "title": title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": messages[0].id,
                        "status": str(e),
                        "path": dir_name
                    })


class R:
    def run(self):
        r.init(True)
        try:
            # get_img_message_id('AnchorPic', 7628)
            # get_img_message_id('COSYiMi', 19471)
            get_img_message_id('WMQBOSS', 9158)
            get_img_message_id('MarioBase', 45130, False)
            # get_img_message_id('fanchaboss', 2311)
        finally:
        # telegram_img_download.startScrapy()
            r.close()

if __name__ == '__main__':
    # r.init(True)
    # try:
    rr = R()
    rr.run()
    # finally:
    #     r.close()