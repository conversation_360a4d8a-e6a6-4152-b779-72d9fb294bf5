import requests
import rpa as r
import datetime
import elastic_search.esUtil as esUtil
from bs4 import BeautifulSoup
from qbittorrentapi import Client

class QBitTorrentHelper:
    api_url = 'http://jim-nas.internal.site:8085'
    username = 'admin'
    password = '19950102'
    client = Client(host='http://jim-nas.internal.site:8085',username=username, password=password)


    def addJob(self, torrent):
        save_path = '/volume2/MyHDD/rss-game'
        result = self.client.torrents_add(urls=torrent, save_path=save_path, category="game")

    def getInfo(self):
        result = self.client.torrents_info()
        print(result)


if __name__ == '__main__':
    # torrent = "magnet:?xt=urn:btih:bd66760caafc012865a5e8a35ad8e1d81bdefdcf&dn=White.Night.Dream.rar&tr=udp://tracker.torrent.eu.org:451/announce&tr=udp://exodus.desync.com:6969/announce&tr=udp://tracker.moeking.me:6969/announce&tr=udp://tracker.opentrackr.org:1337/announce&tr=udp://open.stealth.si:80/announce&tr=udp://tracker.theoks.net:6969/announce&tr=udp://movies.zsw.ca:6969/announce&tr=udp://tracker.tiny-vps.com:6969/announce&tr=udp://tracker-udp.gbitt.info:80/announce&tr=http://tracker.gbitt.info:80/announce&tr=https://tracker.gbitt.info:443/announce&tr=http://tracker.ccp.ovh:6969/announce&tr=udp://tracker.ccp.ovh:6969/announce&tr=udp://tracker.dler.com:6969/announce&tr=http://tracker.bt4g.com:2095/announce"
    q = QBitTorrentHelper()
    q.getInfo()
    # q.addJob(torrent)
