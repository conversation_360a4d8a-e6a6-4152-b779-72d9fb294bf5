import requests

# 替换为您自己的Steam Web API密钥
api_key = 'EBB866779FB3DEFC35B563380C53B960'
def search_steam_game(game_name):

    # 创建API请求的URL
    url = f'http://api.steampowered.com/ISteamApps/GetAppList/v0002/?key={api_key}'

    try:
        # 发送API请求并获取响应数据
        response = requests.get(url)
        data = response.json()

        # 获取游戏列表
        app_list = data['applist']['apps']

        # 在游戏列表中搜索匹配的游戏
        for app in app_list:
            if game_name.lower() in app['name'].lower():
                # 打印游戏名称和App ID
                print(f"游戏名称: {app['name']}")
                print(f"App ID: {app['appid']}")
                print('---')

    except requests.exceptions.RequestException as e:
        # 请求发生错误
        print(f"请求发生错误: {e}")


def get_game_details(app_id):

    # 创建API请求的URL
    url = f'http://store.steampowered.com/api/appdetails?appids={app_id}&key={api_key}&cc=cn&l=chinese'

    try:
        # 发送API请求并获取响应数据
        response = requests.get(url,headers={
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"  # 指定所需的语言
        })
        data = response.json()

        # 提取游戏详情
        game_data = data[str(app_id)]
        if game_data['success']:
            game_details = game_data['data']
            print(game_details)
            return game_details
        else:
            print("未找到游戏详情")

    except requests.exceptions.RequestException as e:
        # 请求发生错误
        print(f"请求发生错误: {e}")

if __name__ == '__main__':
    # 调用函数进行游戏搜索
    # search_steam_game('Parry Nightmare')
    search_steam_game("Pepper Grinder")
    # get_game_details(2076580)