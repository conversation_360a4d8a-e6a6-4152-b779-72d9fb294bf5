import html


if __name__ == '__main__':
    text = '<h1>Check out the indirect prequel!</h1><p><a href="https://store.steampowered.com/app/939790/Royal_Alchemist/" target="_blank" rel=""  id="dynamiclink_0" >https://store.steampowered.com/app/939790/Royal_Alchemist/</a></p><br><h1>关于游戏</h1><img src="https://cdn.akamai.steamstatic.com/steam/apps/1749010/extras/story.png?t=1712072015" /><br>The Sval, pirates of the utmost north, are wreaking havoc all over the continent of Igrith.<br><br>Vowing to bring an end to their endless plundering, three kingdoms send forth their royal heirs to the High Council, a powerful and influential faction of magicians, to plead for their help during this time of crisis.<br><br>The High Council oblige, creating the Royal Order, and the heirs remain; however, political tensions are high as old grudges resurface between the three kingdoms. And, as the pirates continue to have their way, they are determinedly bound to reach their breaking point.<br><br>Play as <PERSON><PERSON><PERSON>, the newest member of the High Council, and help manage the Royal Order.<br><br>Can you put an end to the <PERSON>val’s pillaging while keeping the kingdoms at peace? Or will you bring nothing but war upon them?<br><br><img src="https://cdn.akamai.steamstatic.com/steam/apps/1749010/extras/features.png?t=1712072015" /><br>◆ Customizable Protagonist (Appearance, Pronoun, Name)<br>◆ 3 romantic love interests (2 m, 1 f)<br>◆ 2 stats raising systems<br>◆ Time management &amp; Quest system (60 Weeks in-game)<br>◆ 10 Endings<br>◆ And many deaths! :D<br><br><strong>This game is an indirect sequel to our Visual Novel &quot;Royal Alchemist&quot;. The story takes place in the same world, but follows a different cast. Both games can be played separately from each other.</strong>'
    html_text = html.escape(text)
    print(text.replace("\\",""))
