import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
import re

class MongoDBHelper:
    def __init__(self, host='localhost', port=27017, db_name='mydatabase'):
        self.host = host
        self.port = port
        self.db_name = db_name
        self.client = None
        self.db = None

    async def connect(self):
        self.client = AsyncIOMotorClient(host=self.host, port=self.port)
        self.db = self.client[self.db_name]

    async def insert_data(self, collection_name, data):
        collection = self.db[collection_name]
        await collection.insert_one(data)

    async def find_data(self, collection_name, query):
        collection = self.db[collection_name]
        result = collection.find(query)
        return result

    async def count_data(self, collection_name, query):
        collection = self.db[collection_name]
        count = await collection.count_documents(query)
        return count

    async def find_regex(self, collection_name, query):
        collection = self.db[collection_name]
        query = {"name": {"$regex": re.compile(query, re.IGNORECASE)}}
        cursor = collection.find(query)
        return cursor

    async def update_data(self, collection_name, query, update_data):
        collection = self.db[collection_name]
        await collection.update_many(query, {'$set': update_data})
        print("Data updated successfully.")

    async def delete_data(self, collection_name, query):
        collection = self.db[collection_name]
        await collection.delete_many(query)
        print("Data deleted successfully.")

    async def close(self):
        self.client.close()
        print("Database connection closed.")

    async def my_insert_data(self, collection_name, data):
        await self.connect()
        await self.insert_data(collection_name, data)
        await self.close()

    async def my_find_data(self, collection_name, query):
        await self.connect()
        ret = await self.find_data(collection_name, query)
        if ret is not None:
            ret = await ret.to_list(length=None)  # 将游标转换为列表
        await self.close()
        return ret