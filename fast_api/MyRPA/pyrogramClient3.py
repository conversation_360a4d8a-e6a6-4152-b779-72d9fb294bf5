import asyncio
import datetime
import re
import sys
from time import sleep

from bs4 import <PERSON><PERSON><PERSON>p
from concurrent.futures import ThreadPoolExecutor

import requests
import os
import pyrogram
from pyrogram import Client, filters
from pyrogram.errors import FloodWait
import motor.motor_asyncio

# import telegram_img_download
import rpa as r
from collections import defaultdict
import concurrent.futures

# 获取Telegram Bot API的token
# bot_token = '**********************************************'
# 准备下载目录
download_dir = '/Volumes/新加卷/tg-image'
# download_dir = '/Users/<USER>/Desktop/test/'
api_hash= '792cfdcecaeab811010c87c98c200ec7'
api_id= '21786636'

# get login code: visit https://web.telegram.org/?legacy=1#/im?p=u777000_3881963525695541602


client = motor.motor_asyncio.AsyncIOMotorClient("localhost", 27017)
# client.get_io_loop = asyncio.get_event_loop
db = client["mydatabase"]
collection = db["tele_img"]

async def get_dict(app, channel, object_list):
    result_dict = defaultdict(list)
    result_dict2 = defaultdict(list)
    dict3 = {}
    for obj in object_list:
        result_dict[obj.media_group_id].append(obj)
    result_dict = dict(result_dict)

    for key, messages in result_dict.items():
        try:
            comments_as = app.get_discussion_replies(channel, messages[0].id)
            comments = []
            async for comment in comments_as:  # 处理异步生成器
                comments.append(comment)
            comments.sort(key=lambda obj: obj.id)
            messages.extend(comments)
        except FloodWait as e:
            print(f"获取讨论回复时遇到FloodWait错误 (420): 需要等待 {e.value} 秒")
            print("停止下载，关闭程序")
            sys.exit(1)
        except Exception as e:
            pass
        message_info_f = list(filter(lambda x: x.media is not None and x.media.name in ['PHOTO', 'VIDEO'],
                                           messages))
        if len(message_info_f) < 20:
            continue
        result_dict2[key] = message_info_f

    return result_dict2

def remove_special_characters(text):
    if not text:
        return text

    # 首先移除不可见字符和控制字符
    # 包括零宽字符、不间断空格等
    text = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f\ufeff\u00a0\u180e\u2000-\u200a\u2060\u3000]', '', text)

    # 移除问号和其他可能的占位符字符
    text = re.sub(r'[?？\ufffd\u00bf]', '', text)

    # 保留字母、数字、中文、日文、韩文字符，以及中划线、下划线、空格
    # 移除其他特殊符号
    cleaned_text = re.sub(r'[^\w\s\u4e00-\u9fa5\uff00-\uffef\u3040-\u309f\u30a0-\u30ff\-_]', '', text)

    # 清理多余的空格
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

    return cleaned_text

def delete_files_starting_with_underscore(directory):
    # 遍历指定的目录
    for filename in os.listdir(directory):
        # 检查文件名是否以 "_" 开头
        if filename.startswith('_'):
            file_path = os.path.join(directory, filename)
            # 检查路径是否为文件（避免删除目录）
            if os.path.isfile(file_path):
                # 删除文件
                os.remove(file_path)
def getTitle(post_id, chatId, replyMsgId):
    try:
        r.url('https://www.bing.com')
        # r.url(f'https://web.telegram.org/a/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3D{channel}%26post%3D{post_id}')
        r.url(f'https://web.telegram.org/a/#{chatId}_{replyMsgId}')
        # title = r.read(f'//*[@id="message-{post_id}"]/div[3]/div/div[1]/div[2]')
        title = r.read(f'//*[@id="MiddleColumn"]/div[4]/div[2]/div[2]/div/div[3]')
        t = 0
        while (title is None or title == '') and t < 5:
            r.wait()
            # title = r.read(f'//*[@id="message-{post_id}"]/div[3]/div/div[1]/div[2]')
            title = r.read(f'//*[@id="MiddleColumn"]/div[4]/div[2]/div[2]/div/div[3]')
            t += 1

        if title is None or title == '':
            return None

        title = remove_special_characters(title)
        if title and title.find('标签') != -1:
            title = title[:title.find('标签')]
        return title
    except Exception as e:
        print(f"Error getting title: {e}")
        return None

def download_first_photo(media_id, channel, dir_name):
    r.url('https://www.bing.com')
    r.url(f'https://t.me/{channel}/{media_id}')
    r.keyboard('[esc]')
    if (r.present(f'//*[@id="widget_actions"]/div/div[1]/div/a')):
        r.click(f'//*[@id="widget_actions"]/div/div[1]/div/a')
    else:
        r.wait()
        r.keyboard('[esc]')
        r.click(f'//*[@id="widget_actions"]/div/div[1]/div/a')
    t = 0
    res = r.present(f'//*[@id="album-media-message-{media_id}"]')
    while (not res) and t < 5:
        r.wait()
        # title = r.read(f'//*[@id="message-{post_id}"]/div[3]/div/div[1]/div[2]')
        res = r.present(f'//*[@id="album-media-message-{media_id}"]')
        t+=1
    if not res:
        return
    r.click(f'//*[@id="album-media-message-{media_id}"]')
    soup = BeautifulSoup(r.read('page'), features="html.parser")
    main_image = soup.select_one('.MediaViewerSlide--active img')
    if main_image:
        src = main_image['src']
        file_path = os.path.join(dir_name, f"{'{:06d}'.format(1)}.jpg")
        r.download(src, file_path)

async def download_media(app, media, index, dir_name):
    """
    同步下载媒体文件
    """
    if media.photo is not None:
        file_path = os.path.join(dir_name, f"{'{:06d}'.format(index)}.jpg")
        file = media.photo
        media_type = "photo"
    elif media.video is not None:
        if media.video.file_size >= 20971520:
            print(f"跳过大文件 (索引 {index}): 文件大小 {media.video.file_size} 字节")
            return
        if media.video.mime_type == 'video/mp4':
            file_type = "mp4"
        elif media.video.mime_type == 'video/webm':
            file_type = "webm"
        elif media.video.mime_type == 'video/quicktime':
            file_type = "mov"
        elif media.video.mime_type == 'video/x-msvideo' or media.video.mime_type == 'video/avi':
            file_type = "avi"
        elif media.video.mime_type == 'video/x-matroska':
            file_type = "mkv"
        elif media.video.mime_type == 'video/x-ms-wmv':
            file_type = "wmv"
        elif media.video.mime_type == 'video/mpeg':
            file_type = "mpeg"
        elif media.video.mime_type == 'video/x-flv':
            file_type = "flv"
        else:
            file_type = media.video.mime_type.split('/')[-1]
        file_path = os.path.join(dir_name, f"{'{:06d}'.format(index)}.{file_type}")
        file = media.video
        media_type = "video"
    else:
        return

    print(f"下载 {media_type} (索引 {index}): {os.path.basename(file_path)}")
    # 三次重试，每次等待10分钟后执行，否则跳出：
    for i in range(3):
        try:
            await app.download_media(file, file_path)
            print(f"成功下载: {os.path.basename(file_path)}")
            break
        except FloodWait as e:
            print(f"遇到FloodWait错误 (420): 需要等待 {e.value} 秒")
            print("停止下载，关闭程序")
            sys.exit(1)
        except Exception as e:
            print(f"下载失败: {e}")
            if i < 2:
                print(f"等待10分钟后重试...")
                sleep(600)
            else:
                raise e


async def process_messages(channel, def_msg_id, debug=False):
    print(f"start process channel {channel}")


    # c = MongoDBHelper()
    videos_cursor = collection.find({"channel": channel, "tg_msg_id": {"$ne": None}, "link": None})
    videos = await videos_cursor.to_list(length=None)  # 将游标转换为列表

    if debug or videos is None or len(videos) == 0:
        last_msg_id = def_msg_id + 1
    else:
        last_msg_id = max(v["tg_msg_id"] for v in videos) + 1

    message_info = None
    async with pyrogram.Client(
        "media_downloader",
        api_id=api_id,
        api_hash=api_hash,
        # bot_token=bot_token,
        proxy={
            "hostname": os.getenv("PROXY_HOST", "127.0.0.1"),
            "port": int(os.getenv("PROXY_PORT", "7890")),
            "scheme": os.getenv("PROXY_SCHEME", "socks5")
        }) as app:

        # list = []
        # async for dialog in app.get_dialogs():
        #     list.append(dialog)

        while message_info is None or len(message_info)==0 or message_info[-1].empty is None:
            try:
                message_ids = [last_msg_id + x for x in range(100)]
                if type(channel) == int:
                    message_info = [
                        message
                        async for message in app.get_chat_history(channel, offset_id=last_msg_id,
                                                                  limit=100, reverse=True)
                    ]
                else:
                    message_info = await app.get_messages(channel, message_ids=message_ids)
                if message_info[-1].id==last_msg_id:
                    return
                last_msg_id = message_info[-1].id

                group_dict = await get_dict(app, channel, message_info)
            except FloodWait as e:
                print(f"获取消息时遇到FloodWait错误 (420): 需要等待 {e.value} 秒")
                print("停止下载，关闭程序")
                sys.exit(1)
            timer = 0
            for _, messages in group_dict.items():
                timer += 1
                if timer % 2 == 0:
                    sleep(60)
                if timer > 10:
                    return
                title = "Unknown"  # 初始化title变量
                dir_name = ""      # 初始化dir_name变量
                try:
                    caption = next((obj.caption for obj in messages if obj.caption), 'UnTitled')
                    title = caption
                    if title == 'UnTitled':
                        chatId,replyMsgId = next(((obj.chat.id, obj.reply_to_message_id) for obj in messages if obj.chat and obj.chat.id and obj.reply_to_message_id), 'nil')
                        title = getTitle(messages[0].id, chatId,replyMsgId)
                        if title is None or title == "":
                            continue

                    # 首先清理特殊字符
                    title = remove_special_characters(title)
                    if not title or title.strip() == "":
                        continue

                    title = title.replace('\n', '_').replace('\r', '')
                    title = title.replace('#', '-').replace(' ', '').replace(' -Coser', '').replace('-Cosplay', '')
                    title = title.lstrip('!@#-_ ')
                    title = title.strip()
                    title = title.replace('cosxwo-_-cos','')
                    if '图片' in title:
                        title = title.split('图片')[0]
                    if '视频' in title:
                        title = title.split('视频')[0]
                    if not title:
                        continue

                    # title = f"{channel}-_-{title}"
                    print(f"start download {title}.")
                    dir_name = os.path.join(download_dir, datetime.datetime.now().strftime('%Y-%m'), title)
                    os.makedirs(dir_name, exist_ok=True)
                    # 使用同步下载
                    media_messages = [media for media in messages if media.photo is not None or media.video is not None]
                    print(f"开始下载 {len(media_messages)} 个媒体文件...")

                    for i, media in enumerate(media_messages):
                        await download_media(app, media, i + 1, dir_name)

                    delete_files_starting_with_underscore(dir_name)

                    await collection.insert_one({
                        "link": None,
                        "title": title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": messages[0].id,
                        "status": "Success",
                        "path": dir_name
                    })

                except Exception as e:
                    print(e)
                    # 确保变量已定义，避免NameError
                    safe_title = title if 'title' in locals() else "Unknown"
                    safe_dir_name = dir_name if 'dir_name' in locals() else ""
                    await collection.insert_one({
                        "link": None,
                        "title": safe_title,
                        "channel": channel,
                        "date": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "tg_msg_id": messages[0].id,
                        "status": str(e),
                        "path": safe_dir_name
                    })

async def run2():
    # await process_messages('COSYiMi', 19471)
    await process_messages('WMQBOSS', 9158)
    await process_messages(-1002393003114, 6167)
    await process_messages('cosxwo', 14590)
    # await process_messages('simisebaisi', 28584)
    await process_messages('taotuxueyuan', 20922)
    await process_messages('ioopro2', 22611)
    await process_messages('GQ4KHD', 2059)
    # await process_messages('MarioBase', 45130, False)
    # await process_messages('fanchaboss', 2311)
    # await process_messages('miaotuya', 67300)
    return "Success"

class R:
    async def run(self):
        r.init(True)
        try:
            # get_img_message_id('AnchorPic', 7628)
            return await run2()
            # loop = asyncio.get_event_loop()
            # loop.run_until_complete(run2())
        finally:
        # telegram_img_download.startScrapy()
            r.close()

if __name__ == '__main__':
    # r.init(True)
    # try:
    rr = R()
    loop = asyncio.get_event_loop()
    loop.run_until_complete(rr.run())

    # finally:
    #     r.close()