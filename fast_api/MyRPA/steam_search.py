import re

import requests
from bs4 import BeautifulSoup
from mega import Mega
import sys
import rpa as r
from MyRPA.util import MongoDBHelper

def search_steam_app_id(app_id, download=False):
    google_search_url = f"https://store.steampowered.com/app/{app_id}/ site:skidrowreloaded.com"
    web_urls = search(google_search_url)
    print(web_urls)
    for web_url in web_urls:
        url = get_download_link(web_url)
        if url is not None:
            if download:
                download_mega(url)
            record(app_id, url, download)
            break
    return {
        "download": "success" if url is not None else "fail",
        "url": url
    }

def record(app_id, download_url, downloaded=False):
    mongo = MongoDBHelper.MongoDBHelper()
    record = mongo.my_find_data('my_game_download', {"steam_app_id": app_id})
    if len(record)>0:
        return
    mongo.my_insert_data('my_game_download',{
        "steam_app_id": app_id,
        "download_url": download_url,
        "status": "Not Started" if not downloaded else "Finished"
    });


def search(keyword):
    r.init(True)
    results = []
    try:
        r.url(f'https://www.google.com/search?q={keyword}')
        r.wait(2)
        result = r.read("page")
        soup = BeautifulSoup(result, features="html.parser")
        main = soup.find('div', {"id": "search"})
        list = main.find_all('a', href=True)
        for result in list:
            link = result['href']
            if link.startswith('https://translate.google.com'):
                continue
            results.append(link)
    finally:
        r.close()
    return results


def get_download_link(url, type = "meta"):
    try:
        print(f"start get downlaod link for {url}")
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        links = soup.find_all('a', href=True)
        mega_links = [link['href'] for link in links if re.search(r'https://mega', link['href'], re.IGNORECASE)]
        mega_link = mega_links[0] if len(mega_links)>0 else None
        print(f"download link is  {mega_link}")
        return mega_link
    except requests.RequestException as e:
        print(f"请求出错: {e}")
        return None

def download_mega(url, dest_path='/Volumes/aigo2/rss-game/'):
    mega = Mega()
    try:
        # 获取文件信息
        info = mega.get_public_url_info(url)
        total_size = info['size']

        print(f"start downloading {info['name']}！ size: {info['size']}")
        # 开始下载
        mega.download_url(url, dest_path)
        print("下载完成！")

    except Exception as e:
        print(f"下载出错: {e}")


# 使用示例
if __name__ == "__main__":
    record(11129012,'http://test.download.link')