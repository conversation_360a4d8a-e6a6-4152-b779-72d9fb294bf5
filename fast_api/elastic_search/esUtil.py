from elasticsearch import Elasticsearch

from elasticsearch.helpers import bulk

client = Elasticsearch(
  "http://192.168.0.33:9200",
  api_key="X1NjYzRJMEJZa2hFSkMteEJ0djk6VXBzOExpVHJRY2VDemxFcTdZcjR3QQ=="
)

def create_index(index):
    client.indices.create(index=index, ignore=400)
def write_es(documents):
    res = bulk(client, documents)
    print(res)

def write_es_single(documents,index="search-job-zh"):
    res = client.index(index=index,body=documents)

def exists(index, field, value):
    query = {
        "query": {
            "term": {
                field: value
            }
        }
    }
    result = client.search(index=index, body=query)
    if result["hits"]["total"]["value"] > 0:
        return True
    return False

def run():
    documents = [
    {
        "_index": 'search-job-zh',
        "_source": {
            "title": "Document 3",
            "content": "This is document 3"
        }
    }
]
    write_es(documents)
    # res = read("snow")
    # print(res)

def read(q, index="search-job-zh"):
    res = client.search(index=index, q=q)
    return res

if __name__ == '__main__':
    run()