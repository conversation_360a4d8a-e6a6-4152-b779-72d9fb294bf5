from fastapi import FastAPI
from MyRPA import RPA as rpa
from MyRPA.steam_search import search_steam_app_id

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.post("/api/rpa")
def execute_job(type: str):
    if type == "job":
        r = rpa()
        r.read()
    elif type == "bing":
        r = rpa()
        r.bing()
    elif type == 'game':
        r = rpa()
        r.game()
    elif type == 'photo':
        r = rpa()
        r.photo()
    else:
        return {"message": "Invalid job type"}


@app.get("/api/rpa/search")
def execute_job(keyword: str):
    r = rpa()
    result = r.search(keyword)
    return result

@app.get("/api/rpa/download_steam_game")
def download_game(steam_app_id: int, download: bool = False):
    result = search_steam_app_id(steam_app_id, download)
    return result

@app.post("/api/rpa/photo")
async def execute_job_photo(type: str):
    r = rpa()
    await r.photo()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=7777)