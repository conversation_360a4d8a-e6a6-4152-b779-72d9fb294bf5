
import csv
import shutil
import os


# 定义新行数据
# new_row = ['image1.jpg', 'prompt1', '2021-08-01', 'C:\\images\\image1.jpg']

def add_row(new_row, csv_file_path):
    # 如果原始文件存在，将其备份
    if os.path.exists(csv_file_path):
        shutil.copy2(csv_file_path, csv_file_path + '.bak')

    # 将新行数据写入文件
    with open(csv_file_path, 'a', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(new_row)



if __name__ == '__main__':
    add_row( ['image1.jpg', 'prompt1', '2021-08-01', 'C:\\images\\image1.jpg'], r'C:\Users\<USER>\Desktop\autorun\sd\data.csv')

