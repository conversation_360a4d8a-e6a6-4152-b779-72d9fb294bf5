import math

import rpa as r
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
# import tagui_2 as r
import signal
import datetime
import pytz

utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)

def run(pageNo):
    r.url("https://igg-games.com/page/" + pageNo)
    r.wait(2)
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    articles = soup.findAll("article")
    send_list = []
    outdate = False

    for article in articles:
        titleCell = article.find("h2").find("a")
        title = titleCell.getText()

        href = titleCell["href"]
        tags = article.find("p").findAll("a", rel="category tag")
        tags = list(map(lambda x: x.getText(), tags))
        if not 'Popular Game' in tags:
            continue
        time = article.find("time").getText()
        timeX = article.find("time")['datetime']
        date = datetime.datetime.strptime(timeX, '%Y-%m-%dT%H:%M:%S%z')
        if date < yesterday:
            outdate = True
            break
        str_1 = title + "\n" + str(tags) + "\n" + time + "\n" + href
        send_list.append(str_1)


    for str_1 in send_list:
        r.telegram(-786900993, str_1)
    return outdate




if __name__ == '__main__':
    r.init(False)
    for i in ["1","2","3"]:
        if run(i):
            break

    r.close()