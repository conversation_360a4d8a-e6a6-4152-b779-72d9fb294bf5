import os

# encoding=utf-8
import jieba
import glob


def run():
    # list_of_files = glob.glob(r'/Volumes/新加卷/telegra.ph/*')
    list_of_files = os.listdir(r'/Volumes/新加卷/telegra.ph/')

    for i in range(20):
        seg_list = jieba.cut(list_of_files[i].replace("_"," "))  # 默认是精确模式

        print("Default Mode: " + ",".join(seg_list))  # 精确模式


    # strs=["我来到北京清华大学","乒乓球拍卖完了","中国科学技术大学"]
    # for str in strs:
    #     seg_list = jieba.cut(str,use_paddle=True) # 使用paddle模式
    #     print("Paddle Mode: " + '/'.join(list(seg_list)))
    #
    # seg_list = jieba.cut("我来到北京清华大学", cut_all=True)
    # print("Full Mode: " + "/ ".join(seg_list))  # 全模式
    #
    # seg_list = jieba.cut("我来到北京清华大学", cut_all=False)
    # print("Default Mode: " + "/ ".join(seg_list))  # 精确模式
    #
    # seg_list = jieba.cut("他来到了网易杭研大厦")  # 默认是精确模式
    # print(", ".join(seg_list))
    #
    # seg_list = jieba.cut_for_search("小明硕士毕业于中国科学院计算所，后在日本京都大学深造")  # 搜索引擎模式
    # print(", ".join(seg_list))


if __name__ == '__main__':
    run()
