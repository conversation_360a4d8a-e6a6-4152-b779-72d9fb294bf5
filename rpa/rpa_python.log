
START - automation started - Mon Apr 15 2024 06:13:21 GMT+0800 (CST)

LIVE MODE - type done to quit
 [RPA][STARTED]
[RPA][0] - listening for inputs
[RPA][1] - https://www.zhipin.com/web/geek/job-recommend?city=101010100&salary=406&experience=105,106,107&industry=100015&scale=303,304,305,306&jobType=1901
[RPA][1] - listening for inputs
[RPA][2] - count_result = count('//*[@class="job-card-box"]').toString()
[RPA][2] - listening for inputs
[RPA][3] - dump count_result to rpa_python.txt
[RPA][3] - listening for inputs
[RPA][4] - exist_result = exist('//*[@id="wrap"]/div[2]/div[1]/div/div[1]/a[3]').toString()
[RPA][4] - listening for inputs
[RPA][5] - dump exist_result to rpa_python.txt
[RPA][5] - listening for inputs
[RPA][6] - click //*[@id="wrap"]/div[2]/div[1]/div/div[1]/a[3]
[RPA][6] - listening for inputs
[RPA][7] - present_result = present('//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]').toString()
[RPA][7] - listening for inputs
[RPA][8] - dump present_result to rpa_python.txt
[RPA][8] - listening for inputs
[RPA][9] - exist_result = exist('//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]').toString()
[RPA][9] - listening for inputs
[RPA][10] - dump exist_result to rpa_python.txt
[RPA][10] - listening for inputs
[RPA][11] - click //*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[1]
[RPA][11] - listening for inputs
