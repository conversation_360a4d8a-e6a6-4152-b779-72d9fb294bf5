import schedule
import time
import tg_img_download
import traceback
import bilibili.bilibili_download as bili
import zip.zip_manager as zip
from my_tg_img_downloader import telegram_img_download
from my_tg_img_downloader import video_download
from zip import fileHelper
from wall_paper import bing
from download import everia


def run(func):
    try:
        func()
    except Exception as e:
        print(traceback.format_exc())


schedule.every().day.at("01:00").do(run, everia.run)
schedule.every().day.at("01:55").do(run, bing.run)
schedule.every().day.at("02:00").do(run, telegram_img_download.run)
schedule.every().day.at("02:36").do(run, tg_img_download.run)
schedule.every().day.at("09:00").do(run, fileHelper.run)
schedule.every().friday.at("12:00").do(run, bili.run)
schedule.every().day.at("23:00").do(run, video_download.run)



if __name__ == '__main__':
    while True:
        schedule.run_pending()
        time.sleep(1)
