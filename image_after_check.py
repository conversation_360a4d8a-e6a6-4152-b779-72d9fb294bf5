import math

# import rpa as r
import datetime
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
import tagui_2 as r
import signal
import tg_img_download


file_path_dir = 'C:\\Users\\<USER>\\Desktop\\autorun\\download\\telegra.ph\\*'
download_list_path_dir = 'C:\\Users\\<USER>\\Desktop\\autorun\\download_list\\download'
now = datetime.datetime.now().strftime('%Y-%m-%d')
tele_export = 'C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\' + 'ChatExport_' + now + '\\result.json'

def check_download_list():
    if not os.path.isfile(download_list_path_dir + now + ".log"):
        r.telegram(-1001497463795, "download list not exists!")
        check_telegram_export()
    else:
        redo_download()
        list_of_files = glob.glob(file_path_dir)
        latest_file = max(list_of_files, key=os.path.getctime)
        unix_time = os.path.getctime(latest_file)
        last_day_utime = time.mktime((datetime.datetime.now() - datetime.timedelta(days=1)).timetuple())
        if last_day_utime > unix_time:
            msg = r.telegram(-1001497463795, "file download failed again!")

    return


def check_telegram_export():
    if not os.path.isfile(tele_export):
        r.telegram(-1001497463795, "telegram export file not exists!")
        main.main_func()
    else:
        redo_download_file()
        redo_download()
    return


def check_file_exist():
    list_of_files = glob.glob(file_path_dir)
    latest_file = max(list_of_files, key=os.path.getctime)
    unix_time = os.path.getctime(latest_file)
    last_day_utime = time.mktime((datetime.datetime.now() - datetime.timedelta(days = 1)).timetuple())
    if last_day_utime > unix_time:
        msg = r.telegram(-1001497463795, "file download failed! Start re-download process!")
        check_download_list()
        return False
    return True


def redo_download2():
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\web_download_icon.png')

    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\multi_url.png')

    r.keyboard('[space]')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\text_input.png')
    r.keyboard('[ctrl]v')

    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\batch_submit.png')
    r.wait(10)
    # r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\x800.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\get_all.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\downloaddd.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\auto_download.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\do_download.png')
    # # 下载
    # r.click(2150, 380)
    r.wait(60 * 20)
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\small_screen\done_download.png')


def redo_download():
    # r.run(r'"C:\Program Files\Google\Chrome\Application\chrome.exe"')
    r.run(r'start /MAX "" "C:\Program Files\Google\Chrome\Application\chrome.exe"')

    file_path = 'C:\\Users\\<USER>\\Desktop\\autorun\\download_list\\download'
    content = r.load(file_path + now + ".log")
    r.clipboard(content)

    if not r.present(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\web_download_icon.png'):
        redo_download2()

    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\web_download_icon.png')

    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\multi_url.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\text_input.png')
    r.keyboard('[ctrl]v')

    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\batch_submit.png')
    r.wait(10)
    # r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\x800.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\get_all.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\downloaddd.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\auto_download.png')
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\do_download.png')
    # # 下载
    # r.click(2150, 380)
    r.wait(60 * 20)
    r.click(r'C:\Users\<USER>\Desktop\autorun\rpa_photo\done_download.png')

    # r.click(3400, 20)


def getHistorySet():
    list_of_files = glob.glob(r'C:\Users\<USER>\Downloads\Telegram Desktop\*')
    latest_file_dir = max(list_of_files, key=os.path.getctime)
    titles = set()
    with open(latest_file_dir + '\\result.json', 'r', encoding="utf-8") as log:
        data = log.read()
    json_object = json.loads(data)
    for msg in json_object['messages']:
        if type(msg['text']) is str:
            continue
        for attrs in msg['text']:
            if type(attrs) is dict and 'href' in attrs:
                if (type(attrs['href']) is str):
                    if attrs['href'].startswith('https://telegra.ph') and attrs['text'] is not None:
                        titles.add(attrs['text'])
    return titles


def getHistorySetV2():
    list_of_files = glob.glob(file_path_dir)
    list_of_files = list(map(lambda x:x.replace('C:\\Users\\<USER>\\Desktop\\autorun\\download\\telegra.ph\\','')
                             .replace('_–_Telegraph','')
                             .replace('_', ' '), list_of_files))
    return list_of_files


file_path = 'C:\\Users\\<USER>\\Desktop\\autorun\\download_list\\download'
def get_tele_download_url(his_title):
    file_dir = 'ChatExport_' + now
    urls = list()
    names = set()
    total = set()
    with open('C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\' + file_dir + '\\result.json', 'r', encoding="utf-8") as log:
        data = log.read()
    json_object = json.loads(data)
    for msg in json_object['messages']:
        if type(msg['text']) is str:
            continue
        for attrs in msg['text']:
            if type(attrs) is dict and 'href' in attrs:
                if type(attrs['href']) is str:
                    if attrs['href'].startswith('https://telegra.ph'):
                        attrs['text'] = attrs['text'].strip()
                        if (attrs['text'] not in his_title or attrs['text'] == '🌐') and attrs['text'] not in names:
                            urls.append(attrs['href'])
                            names.add(attrs['text'])

    with open(file_path + now + ".log", 'a+') as log:
        for link in urls:
            log.write(link)
            log.write("\n")
    return urls

def redo_download_file():
    get_tele_download_url(getHistorySetV2())

if __name__ == '__main__':
    r.init(True)
    check_file_exist()
    # redo_download()
    r.close()