
import tagui_2 as r

def fetch_torrent(url):
    pass


def download(torrent):
    r.init(True)
    doDownload(torrent)
    r.close()

def doDownload(torrent):
    if not torrent.endswith(".torrent"):
        r.url(torrent)

    r.run(r'//Applications/qbittorrent.app/Contents/MacOS/qbittorrent')
    r.clipboard(torrent)
    r.keyboard('[shift][cmd]o')
    r.wait()
    r.keyboard('[cmd]a')
    r.keyboard('[delete]')
    r.keyboard('[cmd]v')
    r.keyboard('[cmd][enter]')
    r.wait()

    # Press the green button in the gutter to run the script.
if __name__ == '__main__':
    download('https://ehtracker.org/get/1481675/2b99b5ffdaf615828e59a341a15e48481faa9993.torrent')