
import tagui_2 as r
from bs4 import BeautifulSoup
import qBitTorrent
import traceback
import glob

failed_title_list = []
success_title_list = []

HIS_EH_FILE = '/Users/<USER>/Desktop/code/eh-title.log'

def download(torrent):
    r.init(True)
    doDownload(torrent)
    r.close()



def doDownload(torrent):
    if not torrent.endswith(".torrent"):
        r.url(torrent)
    r.run(r'//Applications/qbittorrent.app/Contents/MacOS/qbittorrent')
    r.clipboard(torrent)
    r.keyboard('[shift][cmd]o')
    r.wait()
    r.keyboard('[cmd][enter]')
    r.wait()
    r.keyboard('[enter]')

def singleTorDetail(title, href):
    r.url(href)
    r.wait()
    result = r.read("page")
    try:
        soup = BeautifulSoup(result, features="html.parser")
        main_content = soup.find('div', {"id": "gd5"})
        tor_boxes = main_content.findAll('p', {"class": "g2"})
        tor_box = None
        for box in tor_boxes:
            if box.getText().startswith(' Torrent Download'):
                tor_box = box
        if tor_box is None:
            raise Exception('No torrent!')
        prompt = tor_box.find('a')['onclick']
        link = str(prompt)[prompt.find("\'")+1:prompt.rfind("\'")]
        getTorrent(link)

    except Exception as e:
        print(traceback.format_exc())
        r.telegram(-1001497463795, f"Failed to download torrent for:\n {title}:\n {e}")
    finally:
        with open(HIS_EH_FILE, 'a+') as log:
            log.write(title)
            log.write("\n")


def getTorrent(link):
    r.url(link)
    r.wait()
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    forms = soup.findAll("form")
    forms.pop()
    torrent_list = []
    for form in forms:
        td = form.findAll("td")
        download_count = 0
        msg = td[5].getText()
        msg = msg[msg.find(' '):]
        download_count = int(msg)
        try:
            torrent = form.find("a")["href"]
        except:
            continue
        torrent_list.append((download_count, torrent))
        # for t in td:
        #     span = t.find("span")
        #     if span.getText() == 'Downloads:':
        #         download_count = int(td.getText())
        #     torrent = td.find("a")["href"]
        #     torrent_list.append((download_count,torrent))
    torrent_list.sort(key=lambda x: x[0], reverse=True)
    link = torrent_list[0][1]
    qBitTorrent.doDownload(link)


def getTorDetail(ehList):
    for title,href in ehList:
        singleTorDetail(title, href)





def getEHDetail():
    with open(HIS_EH_FILE, 'r') as log:
        lines = log.readlines()

    url_list = set(lines)
    his_title = set(map(lambda x: x.rstrip('\n'), url_list))

    r.url('https://e-hentai.org/favorites.php?favcat=2')
    r.wait()
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    main_content = soup.find('table', {"class": "gltc"})
    data = main_content.find_all('tr')
    data.pop(0)
    hrefs = list(map(lambda x: x.find('td', {"class": "glname"}).find('a')['href'], data))
    titles = list(map(lambda x: x.find('div', {"class": "glink"}).getText(), data))
    ehList = list(zip(titles, hrefs))
    ehList = list(filter(lambda x: x[0] not in his_title, ehList))

    getTorDetail(ehList)

    # Press the green button in the gutter to run the script.
if __name__ == '__main__':
    # download('https://ehtracker.org/get/1481675/2b99b5ffdaf615828e59a341a15e48481faa9993.torrent')
    r.init(True)
    getEHDetail()
    # singleTorDetail('test', 'https://e-hentai.org/g/2357132/b35f5fb02e/')
    r.telegram(-1001497463795, "done")
    r.close()