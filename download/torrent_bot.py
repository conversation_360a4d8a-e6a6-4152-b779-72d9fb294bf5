import logging
from telegram import Update
from telegram.ext import ApplicationBuilder, ContextTypes, CommandHandler
import qBitTorrent

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await context.bot.send_message(chat_id=update.effective_chat.id, text="I'm a bot, please talk to me!")

async def caps(update: Update, context: ContextTypes.DEFAULT_TYPE):
    link = '\n'.join(context.args)
    qBitTorrent.download(link)
    await context.bot.send_message(chat_id=update.effective_chat.id, text='Done!')

if __name__ == '__main__':
    application = ApplicationBuilder().token('5867120350:AAGY6K12tNOTXVp-93iJf6I743oxC76ROxc').build()

    start_handler = CommandHandler('start', start)
    caps_handler = CommandHandler('tor', caps)
    application.add_handler(start_handler)
    application.add_handler(caps_handler)

    application.run_polling()