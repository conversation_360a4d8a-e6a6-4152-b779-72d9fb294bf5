import os
import datetime
import inspect
# dir = "D:\\crawler\\resources\\"
# name="[MakeModel] SERA [세라] 매혹의 블랙 스타킹 02"
# now_time = datetime.datetime.now()
# format_time = datetime.datetime.now().strftime('%Y-%m-%d_%H:%M')
# # print(format_time)
#
#
# def myFunc(name, year, test):
#     print(name+","+year+","+test)
#     pass
#
# out = inspect.getfullargspec(myFunc).args
# print (myFunc)
import re
import shutil
import glob
import os.path
import tg_img_download
from my_tg_img_downloader import video_download


if __name__ == '__main__':
    video_download.run()
    # main.compress_img('telegra.ph')


