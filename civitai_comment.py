import math

# import rpa as r
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk

import csv_processor
import tagui_2 as r
import signal
import datetime
import pytz
import telegram_sender
import img_download_helper

utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)

CIVITAI_API_KEY = "c095b415899f95d9a681c1b54290f9f2"

his_url_list = []
temp_url_list = []

process_prompt = set()
def process(page_url):
    global process_prompt
    r.wait(1)
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    photo_div = soup.findAll("img")
    photo_link = None
    for t in photo_div:
        src = t.attrs.get("src", None)
        if src is not None and src.startswith("https://imagecache") and t.attrs.get('alt',None) is not None and 'Avatar' not in t.attrs.get('alt',None):
            photo_link = src
    if not r.present('//*[@id="freezeBlock"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div[4]/div/button/div/span[2]'):
        return
    click_res = r.click('//*[@id="freezeBlock"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div[4]/div/button/div/span[2]')
    prmp = r.clipboard()
    if click_res and photo_link is not None and len(photo_link) > 1 and len(prmp) > 1 and prmp not in process_prompt and "Negative prompt" in prmp:
        process_prompt.add(prmp)
        local_save(prmp, photo_link, page_url)
        # TODO 暂时不用
        # try:
        #     telegram_sender.send_img2(photo_link, "-1001907627017")
        #     r.telegram(-1001907627017, prmp)
        # except Exception as e:
        #     print(e)


def local_save(prmpt, web_url, page_url):
    now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    path = r'C:\Users\<USER>\Desktop\autorun\sd' + "\\" + now
    row = [now, prmpt, web_url, path, page_url]
    if not os.path.exists(path):
        os.mkdir(path)
        os.mkdir(path + "\\" + "output")
        csv_processor.add_row(row, r'C:\Users\<USER>\Desktop\autorun\sd\data.csv')
        with open(path + '\\' + "prompt.txt", 'a+') as log:
            log.write(prmpt)
        img_download_helper.download(web_url, path + "\\" + "img.png")



def query_sub_page(link):
    r.url(link)
    r.wait(5)

    # process()

    iter = 1
    next_btn_path = '//*[@id="freezeBlock"]/div/div[1]/div[2]/button[' + str(iter) + ']'

    process(link)
    return

    # if not r.present(next_btn_path):
    #     process(link)
    #     return
    #
    # while r.present(next_btn_path):
    #     r.click(next_btn_path)
    #     process(link)
    #     iter += 1
    #     next_btn_path = '//*[@id="freezeBlock"]/div/div[1]/div[2]/button[' + str(iter) + ']'
    #
    # print(iter)


def run():
    global his_url_list
    r.url("https://civitai.com/models/6424/chilloutmix?tags=5133")
    r.wait(5)
    r.keyboard('[esc]')
    r.keyboard('[esc]')
    r.keyboard('[space]')
    r.wait(3)
    r.keyboard('[space]')
    r.wait(3)
    sub_page_list = []
    for i in range(12):
        r.keyboard('[space]')
        r.wait(10)
        result = r.read("page")
        soup = BeautifulSoup(result, features="html.parser")
        links = soup.findAll("a")
        links2 = list(filter(lambda x: 'href' in x.attrs and "postId" in x.attrs['href'], links))

        links2 = list(map(lambda x: "https://civitai.com" + x.attrs['href'], links2))
        outdate = False
        for link in links2:
            if link not in his_url_list:
                sub_page_list.append(link)
                his_url_list.add(link)

    with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_temp.txt', 'a+',encoding="utf-8") as log:
        log.truncate(0)
        for item in sub_page_list:
            log.write(item)
            log.write('\n')

    for url in sub_page_list:
        query_sub_page(url)
    with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_comment_url.txt', 'a+',encoding="utf-8") as log:
        for item in sub_page_list:
            log.write(item)
            log.write('\n')
    return

def debug(list):
    for url in list:
        query_sub_page(url)
    with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_comment_url.txt', 'a+',encoding="utf-8") as log:
        for item in list:
            log.write(item)
            log.write('\n')

def get_his_url_list():
    global his_url_list, temp_url_list
    with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_comment_url.txt', 'r', encoding="utf-8") as log:
        his_url_list = set(log.readlines())
    his_url_list = set(map(lambda x: x.rstrip('\n'), his_url_list))
    with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_temp.txt', 'r',encoding="utf-8") as log:
        temp_url_list  = list(log.readlines())
    temp_url_list = set(map(lambda x: x.rstrip('\n'), temp_url_list))


if __name__ == '__main__':
    get_his_url_list()
    r.init(True)
    run()
    # if len(temp_url_list)>0:
    #     debug(temp_url_list)
    # else:
    #     run()
    # with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_temp.txt', 'a+',encoding="utf-8") as log:
    #     log.truncate(0)

    # list = ['https://civitai.com/images/419704?modelId=6424&postId=127837&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419740?modelId=6424&postId=127837&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419749?modelId=6424&postId=127837&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419776?modelId=6424&postId=127837&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419368?modelId=6424&postId=127736&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419255?modelId=6424&postId=127713&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419256?modelId=6424&postId=127713&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419439?modelId=6424&postId=127758&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419434?modelId=6424&postId=127755&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419383?modelId=6424&postId=127741&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419382?modelId=6424&postId=127741&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419384?modelId=6424&postId=127741&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419385?modelId=6424&postId=127741&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419438?modelId=6424&postId=127757&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419580?modelId=6424&postId=127808&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419463?modelId=6424&postId=127767&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419462?modelId=6424&postId=127767&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419461?modelId=6424&postId=127767&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419465?modelId=6424&postId=127767&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419464?modelId=6424&postId=127767&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419630?modelId=6424&postId=127820&tags=5133&id=6424&slug=chilloutmix', 'https://civitai.com/images/419777?modelId=6424&postId=127850&tags=5133&id=6424&slug=chilloutmix']
    # with open(r'C:\Users\<USER>\PycharmProjects\pythonProject\civitai_comment_url.txt', 'a+', encoding="utf-8") as log:
    #     for item in list:
    #         log.write(item)
    #         log.write('\n')
    # debug(list)

    r.close()
