import math

# import rpa as r
import datetime
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
import tagui_2 as r
import signal
import daily_v2ex



def run():
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)

    r.url('https://www.guowenku.com/news.php?upcache=1')
    r.wait(2)
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    main_content = soup.find(class_="tab_01")
    labels = main_content.findAll("tr")
    labels = labels[1:-1]
    grids = list(map(lambda x: (x.find('a'), x.findAll('td')[2], x.findAll('td')[3]), labels))
    infos = list(
        map(lambda x: (x[0].getText(), 'https://www.guowenku.com' + x[0]['href'], x[1].getText(), x[2].getText()),
            grids))
    infos = list(filter(lambda x: datetime.datetime.strptime(x[2], '%Y-%m-%d %H:%M:%S') > yesterday, infos))

    mesg = list(map(lambda x: '\n'.join(x), infos))

    for info in mesg:
        ret = r.telegram(-704958767, info)


if __name__ == '__main__':
    r.init(True)
    run()
    daily_v2ex.run()
    r.close()