import datetime
import traceback

import rpa as r
from bs4 import BeautifulSoup
import pytz
import requests
from translate import Translator
import time
import re
import emoji

from MyRPA.util.MongoDBHelper import MongoDBHelper
from MyRPA.util.qbitTorrentHelper import QBitTorrentHelper
from MyRPA.util.steam_search import SteamSearchUtil
from MyRPA.util.tag_translate import TagTranslator
from MyRPA.util.word_press_util2 import WordPressUtil2

import copy


utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)

three_days_before =  datetime.datetime.now() - datetime.timedelta(days=3)
three_days_before = utc.localize(three_days_before)

class DailyGameTranslator:

    translator = Translator(to_lang="zh", email="<EMAIL>")
    def translate_text(self, text):
        try:
            translation = self.translator.translate(text)
        except Exception as e:
            print(e)
            return text
        return translation

    def run(self):
        """
        translate IGG info
        """
        helper = MongoDBHelper()
        game_list = helper.my_find_data('my_game', {
            'content': {'$ne': None},
            'translate': None
        })
        for game in game_list:
            translate = self.translate_text(game.get('content'))
            print(translate)
            helper.my_update_data('my_game', {"_id": game.get("_id")}, {"translate": translate})

    def run2(self):
        """
        add IGG image
        """
        helper = MongoDBHelper()
        game_list = helper.my_find_data('my_game', {
            'link': {'$ne': None},
            'img_link': None
        })
        for game in game_list:
            try:
                response = requests.get(game.get("link"))
                soup = BeautifulSoup(response.content, features="html.parser")
                imgs = soup.findAll("img", {"class": "igg-image-content"})
                img_links = list(map(lambda x: x.attrs["src"], imgs))
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {"img_link": img_links})
            except:
                continue

    def run3(self):
        """
        add Steam info (if any)
        """
        helper = MongoDBHelper()
        s = SteamSearchUtil()
        game_list = helper.my_find_data('my_game', {
            'name': {'$ne': None},
            'steam_status': {'$ne': True},
            'process_date': {'$gt': three_days_before}
        })
        for game in game_list:
            steam_obj = s.search_steam_game(game.get("name"))
            steam_status = True if steam_obj is not None else False
            if steam_status:
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {"steam_status": steam_status, "steam_info": steam_obj, "published": False})
            else:
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {"steam_status": steam_status, "steam_info": steam_obj})
            # time.sleep(2)


    def run3_5(self, download_path="/volume2/MyHDD/rss-game/", new_path="/volume2/MyHDD/rss-game/"):
        q = QBitTorrentHelper()
        q.changeDownloadPathToMonth()

    def run4(self):
        """
        when download complete, add file position info
        """
        q = QBitTorrentHelper()
        result = q.getInfo()
        torrent_list = result.data
        torrent_list = list(filter(lambda x: x.get("category") == 'game' and x.get("progress") == 1, torrent_list))
        torrent_dict = {}
        for item in torrent_list:
            torrent_dict[item['magnet_uri'].split("&tr=udp")[0]] = item

        helper = MongoDBHelper()
        game_list = helper.my_find_data('my_game', {
            'torrent': {'$ne': ''},
            'position': None
        })
        for game in game_list:
            magnet_uri = game["torrent"].split("&tr=udp")[0]
            if magnet_uri in torrent_dict:
                data = torrent_dict.get(magnet_uri)
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {"position": data.get("content_path")})
                print(f"update position of {game.get('name')}: {data.get('content_path')}")

    def remove_emojis(self, text):
        emoji_pattern = re.compile("["
                                   u"\U0001F600-\U0001F64F"  # emoticons
                                    u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                    u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                    u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
                                   "]+", flags=re.UNICODE)
        clean_text = re.sub(emoji_pattern, '', text)

        return clean_text.strip()

    def remove_emoji2(self,text):
        if text==None:
            return text
        removed_string = emoji.demojize(text).encode('ascii', 'ignore').decode('ascii')
        return removed_string
    def processText(self, t):
        if t is None:
            return t
        t = t.replace("'", "\"").replace("["," ").replace("]", " ")
        return self.remove_emojis(t)
    def do_write_article(self,data2):

        data = copy.deepcopy(data2)

        if data.get("steam_status"):
            data.update(data.get("steam_info"))
        data["igg_link1"] = data.get("link")
        data["igg_link2"] = data.get("link").replace("https://pcgamestorrents.com/", "https://igg-games.com/")

        if data.get("position") is not None:
            data["position_url"] = "http://jim-nas.internal.site:2345/" + data.get("position").replace("/volume2/MyHDD/", "")
        if data.get("img_link") is not None:
            new_link = []
            for link in data.get("img_link"):
                new_link.append(link.replace("https://pcgamestorrents.com/", "https://igg-games.com/"))
            data["img_link"] = ",".join(new_link)

        if data.get("steam_header_image") is not None:
            data["header_image"] = data.get("steam_header_image")
        elif data.get("img_link") is not None:
            data["header_image"] = data.get("img_link").split(",")[0]

        if data.get("steam_screenshots") is not None:
            data["steam_screenshots"] = ",".join(data.get("steam_screenshots"))
        if data.get("steam_appid") is not None:
            data["steam_link"] = f"https://store.steampowered.com/app/{data.get('steam_appid')}"

        tags = data.get("genre", "")
        if tags.find(":") != -1:
            tags = tags[tags.find(":")+1:].split(",")
        else:
            tags = tags.split(",")
        t = TagTranslator()
        tags = list(map(lambda x: t.getTranslation(x), tags))
        if data.get("steam_genres") is not None:
            tags.extend(data.get("steam_genres"))
        tags = list(set(tags))
        tags = list(filter(lambda x: x is not None, tags))
        data["tags"] = ", ".join(tags)
        if data.get("steam_price_overview") is not None:
            data["steam_price_initial"] = data.get("steam_price_overview").get("initial_formatted")
            data["steam_price_now"] = data.get("steam_price_overview").get("final_formatted")
        if data.get("translate") is not None:
            data["content"] = data.get("translate")
        # if data.get("'steam_detailed_description'") is not None:
        #     data["steam_detailed_description"] = data.get("'steam_detailed_description'")

        data.pop('steam_price_overview', None)
        data.pop('steam_info', None)
        data.pop('steam_genres', None)
        data.pop('steam_categories', None)
        output = f'<img style="display: none" src="{data.get("header_image")}"/>'
        output += "\n[myGame "
        steam_info = data.get("steam_detailed_description")
        data.pop('steam_detailed_description', None)
        for key, value in data.items():
            if key == "steam_movies" or key == "steam_detailed_description":
                continue
            if value is None:
                continue
            if type(value) == str:
                value = self.processText(value)
                # if key == 'steam_detailed_description' or key == 'content' or key == 'translate':
                #     value = self.remove_emoji2(value)

            output += f"{key}='{value}' "
        output += f"steam_detailed_description='{self.processText(steam_info)}' "
        output += "]"

        video = """
        <div id="contact-tab-pane" class="tab-pane fade" tabindex="0" role="tabpanel" aria-labelledby="contact-tab">
        """
        if data.get("steam_movies") is not None:
            for v_link in data["steam_movies"]:
                video += f"[ri-video url='{v_link}']"
        video += """
            </div>
        """
        output += video

        return output

    def do_post_article(self,data, content):
        title = data.get("name")
        if data.get("steam_name") is not None:
            title = data.get("steam_name")
        # processed on writing articled
        if data.get("tags") is None:
            cat1 = []
        else:
            cat1 = data["tags"].split(", ")

        cat2 = ["IGG-Games"]
        if data.get("steam_status"):
            cat2.append("Steam")
        else:
            cat2.append("No-Steam")
        if data.get("torrent") is not None and data.get("torrent") != '':
            cat2.append("Torrent")
        else:
            cat2.append("No-Torr")
        if data.get("position") is not None:
            cat2.append("Local")
        else:
            cat2.append("No-Local")

        w = WordPressUtil2()
        body = w.write_article(title, content, tags=cat1, cats=cat2)
        return w.post(body)

    def post_article(self):
        helper = MongoDBHelper()
        game_list = helper.my_find_data('my_game', {
            'position': {'$ne': None},
            'steam_status': True,
            'published': {'$ne': True}
        })
        game_list2 = helper.my_find_data('my_game', {
            'can_download':  False,
            'steam_status': True,
            'published': {'$ne': True}
        })
        game_list3 = helper.my_find_data('my_game', {
            'position': {'$ne': None},
            'steam_status': {'$ne': True},
            # 'process_date': {'$lt': three_days_before},
            'published': {'$ne': True}
        })

        game_list4 = helper.my_find_data('my_game', {
            'can_download': False,
            'steam_status':  {'$ne': True},
            # 'process_date': {'$lt': three_days_before},
            'published': {'$ne': True}
        })
        game_list.extend(game_list2)
        game_list.extend(game_list3)
        game_list.extend(game_list4)
        i = 0
        for game in game_list:
            i+=1
            # if i==1:
            #     continue
            try:
                content = self.do_write_article(game)
                print("Post Article:")
                print(game)
                article_id = self.do_post_article(game, content)
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {'published': True, "article_id": article_id})
            except Exception as e2:
                try:
                    print("Error, Re-Post Article:")
                    print(e2)
                    if (game.get("steam_info") is not None):
                        game.get("steam_info")["steam_detailed_description"] = ""
                    game["content"] = ""
                    game["translate"] = ""
                    content = self.do_write_article(game)
                    article_id = self.do_post_article(game, content)
                    helper.my_update_data('my_game', {"_id": game.get("_id")}, {'published': True, "article_id": article_id})
                    print("RePrint Success")
                except Exception as e:
                    print("RePrint has Error")
                    print(e)
                    print(traceback.format_exc())
                    helper.my_update_data('my_game', {"_id": game.get("_id")}, {'Exception': True})


if __name__ == '__main__':
    d = DailyGameTranslator()
    d.run()
    try:
        d.run2()
        d.run3_5()
    except:
        pass
    d.run3()
    d.run4()
    d.post_article()



