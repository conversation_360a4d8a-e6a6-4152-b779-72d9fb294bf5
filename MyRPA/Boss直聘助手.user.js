// ==UserScript==
// @name         Boss直聘助手
// @namespace    https://github.com/Ocyss/boos-helper
// @version      0.1.6-fix2
// <AUTHOR>
// @description  优化UI去除广告,批量投递简历,高级筛选,GPT自动打招呼,多账号管理...
// @license      MIT
// @icon         https://img.bosszhipin.com/beijin/mcs/banner/3e9d37e9effaa2b6daf43f3f03f7cb15cfcd208495d565ef66e7dff9f98764da.jpg
// @homepage     https://github.com/Ocyss/boos-helper
// @match        https://*.zhipin.com/*
// @require      https://cdn.jsdelivr.net/npm/vue@3.4.21/dist/vue.global.prod.js
// @require      data:application/javascript,%3Bwindow.Vue%3DVue%3B
// @require      https://cdn.jsdelivr.net/npm/element-plus@2.7.0/dist/index.full.min.js
// @require      https://cdn.jsdelivr.net/npm/protobufjs@7.2.6/dist/light/protobuf.min.js
// @require      https://cdn.jsdelivr.net/npm/systemjs@6.14.3/dist/system.min.js
// @require      https://cdn.jsdelivr.net/npm/systemjs@6.14.3/dist/extras/named-register.min.js
// @require      data:application/javascript,%3B(typeof%20System!%3D'undefined')%26%26(System%3Dnew%20System.constructor())%3B
// @resource     element-plus/dist/index.css                 https://cdn.jsdelivr.net/npm/element-plus@2.7.0/dist/index.css
// @resource     element-plus/theme-chalk/dark/css-vars.css  https://cdn.jsdelivr.net/npm/element-plus@2.7.0/theme-chalk/dark/css-vars.css
// @grant        GM_addStyle
// @grant        GM_cookie
// @grant        GM_getResourceText
// @grant        GM_getValue
// @grant        GM_notification
// @grant        GM_setValue
// @grant        unsafeWindow
// @run-at       document-start
// @downloadURL https://update.greasyfork.org/scripts/491340/Boss%E7%9B%B4%E8%81%98%E5%8A%A9%E6%89%8B.user.js
// @updateURL https://update.greasyfork.org/scripts/491340/Boss%E7%9B%B4%E8%81%98%E5%8A%A9%E6%89%8B.meta.js
// ==/UserScript==
// 更新日志[只显示最新的10条,🌟🤡 分别代表新功能和bug修复]
// v0.1.6-fix2 🤡 修复公司名筛选报错,修复部分功能无法正确筛选
// v0.1.6-fix 🤡 修复自定义招呼语不发送，修复配置开关没文字，隐藏没找到vue的提示
// v0.1.6-2 🌟 更多优化和提醒，完善记录内容，筛选问题中加入公司福利内容更准
// v0.1.6-1 🌟🤡 更改元素查找逻辑更快更准，调整运行时期过渡更加流畅，使用CDN优化大小，修复部分样式问题
// v0.1.5 🤡 修复编译后拿不到window数据导致报错的严重Bug
// v0.1.4 🌟🤡 卡片状态适配白天，修复将gpt提示发送给boos

(r=>{if(typeof GM_addStyle=="function"){GM_addStyle(r);return}const a=document.createElement("style");a.textContent=r,document.head.append(a)})(' #boos-helper{position:fixed;top:55px;right:10px;z-index:999}.el-dropdown .el-avatar{border:2px solid #fff}.el-dropdown .el-avatar:hover{border:3px solid #c413e7}.el-dropdown-menu__item{justify-content:center}#loader{width:0;height:4.8px;display:inline-block;position:relative;background:#54f98d;box-shadow:0 0 10px #ffffff80;box-sizing:border-box;top:-14px}#loader:after,#loader:before{content:"";width:10px;height:1px;background:#fff;position:absolute;top:9px;right:-2px;opacity:0;transform:rotate(-45deg) translate(0);box-sizing:border-box;animation:coli1 .3s linear infinite}#loader:before{top:-4px;transform:rotate(45deg);animation:coli2 .3s linear infinite}@keyframes coli1{0%{transform:rotate(-45deg) translate(0);opacity:.7}to{transform:rotate(-45deg) translate(-45px);opacity:0}}@keyframes coli2{0%{transform:rotate(45deg) translate(0);opacity:1}to{transform:rotate(45deg) translate(-45px);opacity:.7}}.card-grid[data-v-d2ec6d12]{display:grid;gap:1rem;grid-template-columns:repeat(auto-fill,minmax(250px,1fr));margin:0 0 1.5rem;position:relative;overflow-x:scroll;scrollbar-color:#c6c6c6 #e9e9e9;scrollbar-gutter:always;padding:3rem 0 3rem 2rem;margin:0;display:flex;color:#000;-webkit-overflow-scrolling:touch}.card-grid[data-v-d2ec6d12]::-webkit-scrollbar{width:10px;height:10px}.card-grid[data-v-d2ec6d12]::-webkit-scrollbar-thumb{background:#434343;border-radius:10px;box-shadow:inset 2px 2px 2px #ffffff40,inset -2px -2px 2px #00000040}.card-grid[data-v-d2ec6d12]::-webkit-scrollbar-track{background:linear-gradient(90deg,#434343,#434343 1px,#262626 0,#262626)}.card[data-v-d2ec6d12]{--state-color: #f00;--state-show: block;padding:1.5rem;border-radius:16px;background:linear-gradient(85deg,#f2eeee,#eff0f6);color:#000;display:flex;flex-direction:column;transition:.2s;margin:0;position:relative;min-width:300px;min-height:350px;box-shadow:-2rem 0 1rem -2rem #cdb9b9}.card *[data-v-d2ec6d12]{-webkit-user-select:none;user-select:none}.card .card-status[data-v-d2ec6d12]{position:absolute;display:var(--state-show);border-radius:0 30px 30px 0;background-color:var(--state-color);color:#fff;padding:7px 17px;text-wrap:nowrap;font-size:12px;font-weight:700;letter-spacing:5px;left:-6px;bottom:-14px;transition:all .25s ease;box-shadow:1px -7px 12px -2px #a7a7a766}.card[data-v-d2ec6d12]:after{position:absolute;content:"";display:var(--state-show);left:-6px;bottom:18px;width:0;height:0;border-left:6px solid transparent;border-right:0px solid transparent;border-bottom:10px solid oklch(from var(--state-color) calc(l*.75) c h)}.card .card-tag[data-v-d2ec6d12]{display:block;margin:0 0 .25rem;color:#6a6868;font-size:.7rem}.card .card-title[data-v-d2ec6d12]{font-size:1.3rem;margin:0 0 8px}.card .card-salary[data-v-d2ec6d12]{font-size:1.1rem;margin:0 0 1rem;color:#ff442e}.card .card-footer[data-v-d2ec6d12]{-webkit-margin-before:auto;margin-block-start:auto;padding:5px 0}.card .avatar[data-v-d2ec6d12]{width:40px;height:40px;border-radius:50%;margin-right:.5rem}.card .author-row[data-v-d2ec6d12]{display:grid;grid-template-columns:40px 1fr;gap:.5rem;align-items:center;color:#a09f9f;line-height:1.3;padding-top:.5rem}.card .author-row .company-name[data-v-d2ec6d12]{color:#000}.card[data-v-d2ec6d12]:focus-within,.card[data-v-d2ec6d12]:hover{transform:translateY(-1rem) rotate(3deg)}.card:focus-within~.card[data-v-d2ec6d12],.card:hover~.card[data-v-d2ec6d12]{transform:translate(130px)}.card[data-v-d2ec6d12]:first-child:focus-within,.card[data-v-d2ec6d12]:first-child:hover{transform:translate(-.5rem,-1rem) rotate(3deg)}.card[data-v-d2ec6d12]:not(:first-child){margin-left:-130px;box-shadow:-3rem 0 3rem -3rem #b4adad}@media (max-width: 1200px){.card[data-v-d2ec6d12]{min-width:250px}.card[data-v-d2ec6d12]:not(:first-child){margin-left:-30px}.card[data-v-d2ec6d12]:hover{transform:translateY(-1rem)}.card:hover~.card[data-v-d2ec6d12]{transform:translate(30px)}}html.dark .card-grid[data-v-d2ec6d12]{scrollbar-color:#666 #201c29;color:#fff}html.dark .card-grid[data-v-d2ec6d12]::-webkit-scrollbar-thumb{background:#434343;box-shadow:inset 2px 2px 2px #ffffff40,inset -2px -2px 2px #00000040}html.dark .card-grid[data-v-d2ec6d12]::-webkit-scrollbar-track{background:linear-gradient(90deg,#434343,#434343 1px,#262626 0,#262626)}html.dark .card[data-v-d2ec6d12]{background:linear-gradient(85deg,#434343,#262626);color:#fff;box-shadow:-2rem 0 1rem -2rem #000}html.dark .card .card-status[data-v-d2ec6d12]{color:#fff;box-shadow:1px -7px 12px -2px #a7a7a766}html.dark .card .card-tag[data-v-d2ec6d12]{color:#b4b4b4}html.dark .card .card-salary[data-v-d2ec6d12]{color:#ff442e}html.dark .card .author-row[data-v-d2ec6d12]{color:#a09f9f}html.dark .card .author-row .company-name[data-v-d2ec6d12]{color:#fff}html.dark .card[data-v-d2ec6d12]:not(:first-child){box-shadow:-3rem 0 3rem -3rem #000}.el-alert--info.is-light,.el-alert--info.is-light .el-alert__description{white-space:pre-line}.el-space .el-button-group{display:flex}.el-space .el-button-group .el-button:first-child{flex:1}.el-table-v2__row-depth-0{height:50px}.el-table-v2__cell-text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}#boos-helper-job{margin-bottom:8px}#boos-helper-job *{-webkit-user-select:none;user-select:none}.hp-about-box{display:flex}.hp-about-box .hp-about{display:flex;flex-direction:column}html.dark .hp-about-box{color:#cfd3dc}.el-checkbox{color:#5e5e5e}.el-checkbox.is-checked .el-checkbox__label{color:#000!important}.dark .el-checkbox.is-checked .el-checkbox__label{color:#cfd3dc!important}.el-form .el-link{font-size:12px}.el-form .el-form-item__label{display:flex;align-items:center}.el-form .el-checkbox__label{padding-left:4px}.el-tabs__content{overflow:unset!important}#wrap{min-width:unset}#wrap .inner{width:unset}#wrap .page-job-wrapper{padding-top:0!important}#wrap .page-job-wrapper .page-job-inner,#wrap .page-job-wrapper .job-search-wrapper{width:65%;max-width:870px;min-width:320px;margin:20px auto}#wrap .page-job-wrapper .page-job-inner.fix-top,#wrap .page-job-wrapper .job-search-wrapper.fix-top{position:unset;margin-top:unset;box-shadow:unset}#wrap .page-job-content,#wrap .job-list-wrapper,#wrap .job-card-wrapper{width:100%!important}#wrap .page-job-inner .page-job-content{display:flex;flex-direction:column;order:2}#wrap .job-card-wrapper{border:3px solid transparent}#wrap .job-card-wrapper .job-card-footer,#wrap .job-card-wrapper .job-card-right,#wrap .job-card-wrapper .job-card-body{display:flex}#wrap .job-card-wrapper .job-card-body{border-radius:12px 12px 0 0}#wrap .job-card-wrapper .job-card-left .job-title,#wrap .job-card-wrapper .salary,#wrap .job-card-wrapper .job-card-right .company-name{font-size:clamp(.625rem,.407rem + 1.09vw,1rem)!important}#wrap .job-card-wrapper .tag-list li,#wrap .job-card-wrapper .company-tag-list li,#wrap .job-card-wrapper .info-desc{font-size:clamp(.531rem,.368rem + .82vw,.813rem)!important}#wrap .job-card-wrapper .job-card-left{height:unset;padding:16px 24px 12px}#wrap .job-card-wrapper .job-card-left .job-name{margin-right:12px}#wrap .job-card-wrapper .job-card-left .job-area-wrapper{margin-left:0!important}#wrap .job-card-wrapper .job-card-left .start-chat-btn,#wrap .job-card-wrapper .job-card-left .info-public{display:inline-block}#wrap .job-card-wrapper .job-card-left .job-info{height:unset;overflow:unset}#wrap .job-card-wrapper .job-card-left .job-info>*{margin:3px}#wrap .job-card-wrapper .job-card-right{flex-wrap:wrap}#wrap .job-card-wrapper .job-card-right .company-logo{margin-right:12px;width:unset;height:unset;border:unset;border-radius:15px}#wrap .job-card-wrapper .job-card-right .company-logo img{object-fit:contain;width:clamp(4.063rem,3.699rem + 1.82vw,4.688rem)}#wrap .job-card-wrapper .job-card-right .company-info{margin-left:0}#wrap .job-card-wrapper .job-card-footer{padding:8px 12px 14px}#wrap .job-card-wrapper .job-card-left .tag-list,#wrap .job-card-wrapper .company-tag-list{height:unset;border:unset}#wrap .search-job-result .job-list-box{display:flex;flex-direction:column}#wrap .search-job-result .job-list-box .job-card-wrapper{margin:16px auto}#wrap .job-search-box .job-search-form{width:100%!important;display:flex}#wrap .job-search-box .job-search-form .city-label,#wrap .job-search-box .job-search-form .search-input-box{width:unset}#wrap .job-search-box .job-search-form .search-input-box{flex:1}#wrap .job-search-box .job-search-form .search-btn{margin:0 15px}.el-input .el-input__inner{background-color:unset!important;border:unset!important}html.dark{--el-bg-color: #212020}html.dark body{background-color:#212121}html.dark #header .inner:before,html.dark .page-job:before{background:unset}html.dark .job-search-wrapper,html.dark .job-card-wrapper,html.dark .satisfaction-feedback-wrapper,html.dark .job-search-box .city-label,html.dark .job-search-box .search-input-box,html.dark .job-search-box .search-input-box input,html.dark .hot-link-wrapper,html.dark .filter-select-dropdown li{background-color:#292929!important}html.dark .filter-select-dropdown,html.dark .filter-select-dropdown ul,html.dark .filter-select-dropdown .condition-position-detail{background-color:#292929!important;border:1px solid #5a5a5a!important}html.dark .filter-select-dropdown *{color:#cfd3dc!important}html.dark .filter-select-dropdown .active{color:#00a6a7!important}html.dark .job-title,html.dark .info-desc,html.dark .tag-list li,html.dark .company-name a,html.dark .satisfaction-feedback-wrapper h3,html.dark .fast-next-btn,html.dark .search-map-btn,html.dark .city-label,html.dark .city-area-select .area-dropdown-item li,html.dark .city-area-select .city-area-tab li,html.dark .subway-select-wrapper .subway-line-list li,html.dark .condition-filter-select .current-select,html.dark .el-vl__wrapper,html.dark .el-checkbox__label,html.dark .placeholder-text,html.dark #boos-helper-job h2{color:#cfd3dc!important}html.dark .city-area-select .area-select-wrapper,html.dark .condition-filter-select,html.dark .condition-position-select.is-select .current-select,html.dark .job-card-body,html.dark .condition-industry-select{background-color:#434141}html.dark .job-card-wrapper{transition:all .3s ease;position:relative}html.dark .job-card-wrapper .job-card-footer{background:linear-gradient(90deg,#373737,#4d4b4b)}html.dark .job-card-wrapper .job-card-left .start-chat-btn{background:#00bebdb3}html.dark .job-card-wrapper .job-info .tag-list li,html.dark .job-card-wrapper .info-public,html.dark .job-card-wrapper .company-tag-list li{color:#cfd3dc!important;background:#44e1e326!important;border:.5px solid rgba(229,230,230,.4705882353)!important}html.dark .job-card-wrapper .info-public em:before{height:70%}html.dark .job-loading-wrapper .job-loading-list li{filter:invert(83%)} ');

System.addImportMap({ imports: {"vue":"user:vue","element-plus":"user:element-plus","protobufjs":"user:protobufjs"} });
System.set("user:vue", (()=>{const _=Vue;('default' in _)||(_.default=_);return _})());
System.set("user:element-plus", (()=>{const _=ElementPlus;('default' in _)||(_.default=_);return _})());
System.set("user:protobufjs", (()=>{const _=protobuf;('default' in _)||(_.default=_);return _})());

System.register("./__entry.js", ['./__monkey.entry-BeSQLw94.js'], (function (exports, module) {
	'use strict';
	return {
		setters: [null],
		execute: (function () {



		})
	};
}));

System.register("./__monkey.entry-BeSQLw94.js", ['vue', 'element-plus'], (function (exports, module) {
  'use strict';
  var reactive, ref, computed, toRaw, isRef, unref, watch, createApp, defineComponent, onMounted, openBlock, createElementBlock, Fragment, createVNode, withCtx, createTextVNode, renderList, toDisplayString, createBlock, Teleport, resolveDynamicComponent, useModel, createElementVNode, resolveComponent, getCurrentScope, onScopeDispose, ElMessage, ElDropdown, ElDropdownMenu, ElDropdownItem, ElAvatar, ElDialog, ElButton, ElPopconfirm, ElAlert, ElTable, ElTableColumn, ElTag;
  return {
    setters: [module => {
      reactive = module.reactive;
      ref = module.ref;
      computed = module.computed;
      toRaw = module.toRaw;
      isRef = module.isRef;
      unref = module.unref;
      watch = module.watch;
      createApp = module.createApp;
      defineComponent = module.defineComponent;
      onMounted = module.onMounted;
      openBlock = module.openBlock;
      createElementBlock = module.createElementBlock;
      Fragment = module.Fragment;
      createVNode = module.createVNode;
      withCtx = module.withCtx;
      createTextVNode = module.createTextVNode;
      renderList = module.renderList;
      toDisplayString = module.toDisplayString;
      createBlock = module.createBlock;
      Teleport = module.Teleport;
      resolveDynamicComponent = module.resolveDynamicComponent;
      useModel = module.useModel;
      createElementVNode = module.createElementVNode;
      resolveComponent = module.resolveComponent;
      getCurrentScope = module.getCurrentScope;
      onScopeDispose = module.onScopeDispose;
    }, module => {
      ElMessage = module.ElMessage;
      ElDropdown = module.ElDropdown;
      ElDropdownMenu = module.ElDropdownMenu;
      ElDropdownItem = module.ElDropdownItem;
      ElAvatar = module.ElAvatar;
      ElDialog = module.ElDialog;
      ElButton = module.ElButton;
      ElPopconfirm = module.ElPopconfirm;
      ElAlert = module.ElAlert;
      ElTable = module.ElTable;
      ElTableColumn = module.ElTableColumn;
      ElTag = module.ElTag;
    }],
    execute: (function () {

      exports({
        a: tryOnScopeDispose,
        c: notification,
        d: delay,
        j: deepmerge,
        t: toValue
      });

      const scriptRel = function detectScriptRel() {
        const relList = typeof document !== "undefined" && document.createElement("link").relList;
        return relList && relList.supports && relList.supports("modulepreload") ? "modulepreload" : "preload";
      }();
      const assetsURL = function(dep) {
        return "/" + dep;
      };
      const seen = {};
      const __vitePreload = function preload(baseModule, deps, importerUrl) {
        let promise = Promise.resolve();
        if (deps && deps.length > 0) {
          const links = document.getElementsByTagName("link");
          const cspNonceMeta = document.querySelector("meta[property=csp-nonce]");
          const cspNonce = (cspNonceMeta == null ? void 0 : cspNonceMeta.nonce) || (cspNonceMeta == null ? void 0 : cspNonceMeta.getAttribute("nonce"));
          promise = Promise.all(deps.map((dep) => {
            dep = assetsURL(dep);
            if (dep in seen)
              return;
            seen[dep] = true;
            const isCss = dep.endsWith(".css");
            const cssSelector = isCss ? '[rel="stylesheet"]' : "";
            const isBaseRelative = !!importerUrl;
            if (isBaseRelative) {
              for (let i = links.length - 1; i >= 0; i--) {
                const link2 = links[i];
                if (link2.href === dep && (!isCss || link2.rel === "stylesheet")) {
                  return;
                }
              }
            } else if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
              return;
            }
            const link = document.createElement("link");
            link.rel = isCss ? "stylesheet" : scriptRel;
            if (!isCss) {
              link.as = "script";
              link.crossOrigin = "";
            }
            link.href = dep;
            if (cspNonce) {
              link.setAttribute("nonce", cspNonce);
            }
            document.head.appendChild(link);
            if (isCss) {
              return new Promise((res, rej) => {
                link.addEventListener("load", res);
                link.addEventListener("error", () => rej(new Error(`Unable to preload CSS for ${dep}`)));
              });
            }
          }));
        }
        return promise.then(() => baseModule()).catch((err) => {
          const e = new Event("vite:preloadError", { cancelable: true });
          e.payload = err;
          window.dispatchEvent(e);
          if (!e.defaultPrevented) {
            throw err;
          }
        });
      };
      const cssLoader = (e) => {
        const t = GM_getResourceText(e);
        return GM_addStyle(t), t;
      };
      cssLoader("element-plus/dist/index.css");
      cssLoader("element-plus/theme-chalk/dark/css-vars.css");
      const icons = { debug: "🐞", info: "ℹ️", warn: "⚠", error: "❌️" };
      const Color = {
        debug: "#42CA8C;",
        info: "#37C5D6;",
        warn: "#EFC441;",
        error: "#FF6257;"
      };
      const logger = exports("l", {
        debug: console.log.bind(
          console,
          `%c${icons.debug} debug > `,
          `color:${Color.debug}; padding-left:1.2em; line-height:1.5em;`
        ),
        info: console.info.bind(
          console,
          `%c${icons.info} info > `,
          `color:${Color.info}; padding-left:1.2em; line-height:1.5em;`
        ),
        warn: console.warn.bind(
          console,
          `%c${icons.warn} warn > `,
          `color:${Color.warn}; padding-left:1.2em; line-height:1.5em;`
        ),
        error: console.error.bind(
          console,
          `%c${icons.error} error > `,
          `color:${Color.error}; padding-left:1.2em; line-height:1.5em;`
        )
      });
      var _GM_cookie = /* @__PURE__ */ (() => typeof GM_cookie != "undefined" ? GM_cookie : void 0)();
      var _GM_getValue = exports("_", /* @__PURE__ */ (() => typeof GM_getValue != "undefined" ? GM_getValue : void 0)());
      var _GM_notification = /* @__PURE__ */ (() => typeof GM_notification != "undefined" ? GM_notification : void 0)();
      var _GM_setValue = exports("p", /* @__PURE__ */ (() => typeof GM_setValue != "undefined" ? GM_setValue : void 0)());
      var _unsafeWindow = exports("o", /* @__PURE__ */ (() => typeof unsafeWindow != "undefined" ? unsafeWindow : void 0)());
      const _hoisted_1$2 = /* @__PURE__ */ createElementVNode("span", null, " 当使用多账户时候,该配置内含有敏感信息,导入导出一定要注意,避免泄露 ", -1);
      const _sfc_main$3 = /* @__PURE__ */ defineComponent({
        __name: "store",
        props: {
          "modelValue": { type: Boolean, ...{ required: true } },
          "modelModifiers": {}
        },
        emits: ["update:modelValue"],
        setup(__props) {
          const show = useModel(__props, "modelValue");
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElDialog), {
              modelValue: show.value,
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => show.value = $event),
              title: "存储配置",
              width: "500",
              "align-center": "",
              "destroy-on-close": "",
              "z-index": 20
            }, {
              footer: withCtx(() => [
                createElementVNode("div", null, [
                  createVNode(unref(ElButton), {
                    onClick: _cache[0] || (_cache[0] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("Cancel")
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    onClick: _cache[1] || (_cache[1] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("Confirm")
                    ]),
                    _: 1
                  })
                ])
              ]),
              default: withCtx(() => [
                _hoisted_1$2
              ]),
              _: 1
            }, 8, ["modelValue"]);
          };
        }
      });
      function tryOnScopeDispose(fn) {
        if (getCurrentScope()) {
          onScopeDispose(fn);
          return true;
        }
        return false;
      }
      function toValue(r) {
        return typeof r === "function" ? r() : unref(r);
      }
      function toReactive(objectRef) {
        if (!isRef(objectRef))
          return reactive(objectRef);
        const proxy = new Proxy({}, {
          get(_, p, receiver) {
            return unref(Reflect.get(objectRef.value, p, receiver));
          },
          set(_, p, value) {
            if (isRef(objectRef.value[p]) && !isRef(value))
              objectRef.value[p].value = value;
            else
              objectRef.value[p] = value;
            return true;
          },
          deleteProperty(_, p) {
            return Reflect.deleteProperty(objectRef.value, p);
          },
          has(_, p) {
            return Reflect.has(objectRef.value, p);
          },
          ownKeys() {
            return Object.keys(objectRef.value);
          },
          getOwnPropertyDescriptor() {
            return {
              enumerable: true,
              configurable: true
            };
          }
        });
        return reactive(proxy);
      }
      function reactiveComputed(fn) {
        return toReactive(computed(fn));
      }
      const isClient = exports("g", typeof window !== "undefined" && typeof document !== "undefined");
      typeof WorkerGlobalScope !== "undefined" && globalThis instanceof WorkerGlobalScope;
      const toString$1 = Object.prototype.toString;
      const isObject$1 = exports("i", (val) => toString$1.call(val) === "[object Object]");
      const noop$1 = exports("n", () => {
      });
      function createFilterWrapper(filter2, fn) {
        function wrapper(...args) {
          return new Promise((resolve, reject) => {
            Promise.resolve(filter2(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);
          });
        }
        return wrapper;
      }
      const bypassFilter = (invoke2) => {
        return invoke2();
      };
      function throttleFilter(...args) {
        let lastExec = 0;
        let timer;
        let isLeading = true;
        let lastRejector = noop$1;
        let lastValue;
        let ms;
        let trailing;
        let leading;
        let rejectOnCancel;
        if (!isRef(args[0]) && typeof args[0] === "object")
          ({ delay: ms, trailing = true, leading = true, rejectOnCancel = false } = args[0]);
        else
          [ms, trailing = true, leading = true, rejectOnCancel = false] = args;
        const clear = () => {
          if (timer) {
            clearTimeout(timer);
            timer = void 0;
            lastRejector();
            lastRejector = noop$1;
          }
        };
        const filter2 = (_invoke) => {
          const duration = toValue(ms);
          const elapsed = Date.now() - lastExec;
          const invoke2 = () => {
            return lastValue = _invoke();
          };
          clear();
          if (duration <= 0) {
            lastExec = Date.now();
            return invoke2();
          }
          if (elapsed > duration && (leading || !isLeading)) {
            lastExec = Date.now();
            invoke2();
          } else if (trailing) {
            lastValue = new Promise((resolve, reject) => {
              lastRejector = rejectOnCancel ? reject : resolve;
              timer = setTimeout(() => {
                lastExec = Date.now();
                isLeading = true;
                resolve(invoke2());
                clear();
              }, Math.max(0, duration - elapsed));
            });
          }
          if (!leading && !timer)
            timer = setTimeout(() => isLeading = true, duration);
          isLeading = false;
          return lastValue;
        };
        return filter2;
      }
      function watchWithFilter(source, cb, options = {}) {
        const {
          eventFilter = bypassFilter,
          ...watchOptions
        } = options;
        return watch(
          source,
          createFilterWrapper(
            eventFilter,
            cb
          ),
          watchOptions
        );
      }
      function watchThrottled(source, cb, options = {}) {
        const {
          throttle = 0,
          trailing = true,
          leading = true,
          ...watchOptions
        } = options;
        return watchWithFilter(
          source,
          cb,
          {
            ...watchOptions,
            eventFilter: throttleFilter(throttle, trailing, leading)
          }
        );
      }
      function isPlainObject$1(item) {
        if (typeof item !== "object" || item === null) {
          return false;
        }
        const prototype2 = Object.getPrototypeOf(item);
        return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);
      }
      function deepClone(source) {
        if (!isPlainObject$1(source)) {
          return source;
        }
        const output = {};
        Object.keys(source).forEach((key) => {
          output[key] = deepClone(source[key]);
        });
        return output;
      }
      function deepmerge(target, source, options = { clone: true }) {
        const output = options.clone ? { ...target } : target;
        if (isPlainObject$1(target) && isPlainObject$1(source)) {
          Object.keys(source).forEach((key) => {
            if (key === "__proto__") {
              return;
            }
            if (isPlainObject$1(source[key]) && key in target && isPlainObject$1(target[key])) {
              output[key] = deepmerge(
                target[key],
                source[key],
                options
              );
            } else if (options.clone) {
              output[key] = isPlainObject$1(source[key]) ? deepClone(source[key]) : source[key];
            } else {
              output[key] = source[key];
            }
          });
        }
        return output;
      }
      const formDataKey = "web-geek-job-FormData";
      const todayKey = "web-geek-job-Today";
      const statisticsKey = "web-geek-job-Statistics";
      const formInfoData = exports("f", {
        company: {
          label: "公司名",
          help: "投递工作的公司名一定包含或不包含在当前集合中，模糊匹配，可用于只投或不投某个公司/子公司。"
        },
        jobTitle: {
          label: "工作名",
          help: "投递工作的岗位名一定包含或不包含在当前集合中，模糊匹配，可用于只投或不投某个岗位名。"
        },
        jobContent: {
          label: "工作内容",
          help: "会自动检测上文(不是,不,无需等关键字),下文(系统,工具),例子：【外包,上门,销售,驾照】，如果写着是'不是外包''销售系统'那也不会被排除"
        },
        salaryRange: {
          label: "薪资范围",
          help: "投递工作的薪资范围一定在当前区间中，一定是区间，使用-连接范围。例如：【12-20】"
        },
        companySizeRange: {
          label: "公司规模范围",
          help: "投递工作的公司人员范围一定在当前区间中，一定是区间，使用-连接范围。例如：【500-20000000】"
        },
        customGreeting: {
          label: "自定义招呼语",
          help: "因为boss不支持将自定义的招呼语设置为默认招呼语。开启表示发送boss默认的招呼语后还会发送自定义招呼语, 使用&lt;br&gt;\\n 换行；例子：【你好\\n我...】"
        },
        greetingVariable: {
          label: "招呼语变量",
          help: "使用mitem模板引擎来对招呼语进行渲染;"
        },
        activityFilter: {
          label: "活跃度过滤",
          help: "打开后会自动过滤掉最近未活跃的Boss发布的工作。以免浪费每天的100次机会。"
        },
        notification: {
          label: "发送通知",
          help: "可以在网站管理中打开通知权限,当停止时会自动发送桌面端通知提醒。"
        },
        aiGreeting: {
          label: "AI招呼语",
          help: "即使前面招呼语开了也不会发送，只会发送AI生成的招呼语，让gpt来打招呼真是太棒了，毕竟开场白很重要。"
        },
        aiFiltering: {
          label: "AI过滤",
          help: "根据工作内容让gpt分析过滤，真是太稳健了，不放过任何一个垃圾"
        },
        aiReply: {
          label: "AI回复",
          help: "万一消息太多，回不过来了呢，也许能和AiHR聊到地球爆炸？魔法击败魔法"
        },
        record: {
          label: "内容记录",
          help: "拿这些数据去训练个Ai岂不是美滋滋咯？"
        }
      });
      const defaultFormData = {
        company: {
          include: false,
          value: [],
          options: [],
          enable: false
        },
        jobTitle: {
          include: false,
          value: [],
          options: [],
          enable: false
        },
        jobContent: {
          include: false,
          value: [],
          options: [],
          enable: false
        },
        salaryRange: {
          value: "",
          enable: false
        },
        companySizeRange: {
          value: "",
          enable: false
        },
        customGreeting: {
          value: "",
          enable: false
        },
        greetingVariable: {
          value: false
        },
        activityFilter: {
          value: false
        },
        notification: {
          value: false
        },
        aiGreeting: {
          enable: false,
          word: `我现在需要求职，所以请你来写求职招呼语来向boos或hr打招呼，你需要代入我的身份也就是一名求职者.
我的能力："我叫xxx,今年xx岁了，我会......"
要求:
1.我会告诉你岗位信息,你只需要回答招呼语，请优先礼貌为主不要过于使用书信格式而是聊天一样的招呼语，最好能根据岗位信息来改变语气。
2.一定不可以编造能力,我会啥上面写的很清楚了,如果有我没说的技术栈那就是不会,可以展示其他优势.不要乱写
3.我需要你在结束的时候告诉他这是ai生成的内容仅供参考
>>>
岗位名:{{ card.jobName }}
岗位描述:{{ card.postDescription }}
薪酬:{{ card.salaryDesc }}
经验要求:{{ card.experienceName }},学历要求:{{ card.degreeName }}
相关标签:{{ card.jobLabels }}
`
        },
        aiFiltering: {
          enable: false,
          word: "我想要早九晚五和双休，公司同事们最好年轻一些要有良好的氛围。我不希望岗位有任何的销售性质，我也不会上门去服务。我想要的是加分项而不是扣分项，没早九晚五也没事，我是社畜～"
        },
        aiReply: {
          enable: false,
          word: ""
        },
        record: {
          enable: false
        }
      };
      const formData = reactive(
        deepmerge(defaultFormData, _GM_getValue(formDataKey, {}))
      );
      watchThrottled(
        formData,
        (v) => {
          logger.debug("formData改变", toRaw(v));
        },
        { throttle: 2e3 }
      );
      function confSaving() {
        const v = toRaw(formData);
        _GM_setValue(formDataKey, v);
        logger.debug("formData保存", toRaw(v));
      }
      function confReload() {
        const v = deepmerge(defaultFormData, _GM_getValue(formDataKey, {}));
        deepmerge(formData, v, { clone: false });
        logger.debug("formData已重置");
      }
      function confExport() {
        alert("请你吃大饼啦...");
      }
      function confImport() {
        alert("请你吃大饼啦...");
      }
      function confDelete() {
        deepmerge(formData, defaultFormData, { clone: false });
        logger.debug("formData已清空");
      }
      const useConfFormData = exports("u", () => {
        return {
          confSaving,
          confReload,
          confExport,
          confImport,
          confDelete,
          formDataKey,
          defaultFormData,
          formData
        };
      });
      function notification(content) {
        _GM_notification({
          title: "Boss直聘批量投简历",
          image: "https://img.bosszhipin.com/beijin/mcs/banner/3e9d37e9effaa2b6daf43f3f03f7cb15cfcd208495d565ef66e7dff9f98764da.jpg",
          text: content,
          highlight: true,
          // 布尔值，是否突出显示发送通知的选项卡
          silent: true,
          // 布尔值，是否播放声音
          timeout: 1e4,
          // 设置通知隐藏时间
          onclick: function() {
            logger.info("点击了通知");
          },
          ondone() {
          }
          // 在通知关闭（无论这是由超时还是单击触发）或突出显示选项卡时调用
        });
      }
      function animate({
        duration,
        draw,
        timing,
        end,
        callId
      }) {
        let start2 = performance.now();
        callId(
          requestAnimationFrame(function animate2(time) {
            let timeFraction = (time - start2) / duration;
            if (timeFraction > 1)
              timeFraction = 1;
            let progress = timing(timeFraction);
            draw(progress);
            if (timeFraction < 1) {
              callId(requestAnimationFrame(animate2));
            } else if (end) {
              end();
            }
          })
        );
      }
      let delayLoadId = void 0;
      function delay(ms) {
        loader({ ms });
        return new Promise((resolve) => setTimeout(resolve, ms));
      }
      function loader({ ms = 1e4, color = "#54f98d", onDone = () => {
      } }) {
        var _a;
        let load = document.querySelector("#loader");
        if (!load) {
          const l = document.createElement("div");
          l.id = "loader";
          (_a = document.querySelector("#header")) == null ? void 0 : _a.appendChild(l);
          load = l;
        }
        load.style.background = color;
        if (delayLoadId) {
          cancelAnimationFrame(delayLoadId);
          delayLoadId = void 0;
        }
        if (load)
          animate({
            duration: ms,
            callId(id) {
              delayLoadId = id;
            },
            timing(timeFraction) {
              return timeFraction;
            },
            draw(progress) {
              if (load)
                load.style.width = progress * 100 + "%";
            },
            end() {
              if (load)
                load.style.width = "0%";
              onDone();
            }
          });
        return () => {
          if (delayLoadId)
            cancelAnimationFrame(delayLoadId);
          delayLoadId = void 0;
          const load2 = document.querySelector("#loader");
          if (load2)
            load2.style.width = "0%";
        };
      }
      function getCurDay() {
        const currentDate = /* @__PURE__ */ new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, "0");
        const day = String(currentDate.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      const todayData = reactiveComputed(() => {
        const date = getCurDay();
        const current = {
          date,
          success: 0,
          total: 0,
          company: 0,
          jobTitle: 0,
          jobContent: 0,
          salaryRange: 0,
          companySizeRange: 0,
          activityFilter: 0,
          repeat: 0
        };
        const g = _GM_getValue(todayKey, current);
        logger.debug("统计数据:", g);
        if (g.date === date) {
          return g;
        }
        const statistics = _GM_getValue(statisticsKey, []);
        _GM_setValue(statisticsKey, [g, ...statistics]);
        _GM_setValue(todayKey, current);
        return current;
      });
      const statisticsData = _GM_getValue(statisticsKey, []);
      watchThrottled(
        todayData,
        (v) => {
          _GM_setValue(todayKey, v);
        },
        { throttle: 200 }
      );
      const useStatistics = exports("b", () => {
        return {
          todayData,
          statisticsData
        };
      });
      const win = _unsafeWindow || document.defaultView || window;
      const doc = win.document;
      const listeners = /* @__PURE__ */ new WeakMap();
      const elProto = win.Element.prototype;
      const matches = elProto.matches || elProto.matchesSelector || elProto.webkitMatchesSelector || elProto.mozMatchesSelector || elProto.oMatchesSelector;
      const MutationObs = win.MutationObserver || win.WebkitMutationObserver || win.MozMutationObserver;
      function addObserver(target, callback) {
        const observer = new MutationObs((mutations) => {
          for (const mutation of mutations) {
            if (mutation.type === "attributes") {
              callback(mutation.target);
              if (observer.canceled)
                return;
            }
            for (const node of mutation.addedNodes) {
              if (node instanceof Element)
                callback(node);
              if (observer.canceled)
                return;
            }
          }
        });
        observer.canceled = false;
        observer.observe(target, {
          childList: true,
          subtree: true,
          attributes: true
        });
        return () => {
          observer.canceled = true;
          observer.disconnect();
        };
      }
      function addFilter(target, filter2) {
        let listener = listeners.get(target);
        if (!listener) {
          listener = {
            filters: /* @__PURE__ */ new Set(),
            remove: addObserver(
              target,
              (el) => listener.filters.forEach((f) => f(el))
            )
          };
          listeners.set(target, listener);
        }
        listener.filters.add(filter2);
      }
      function removeFilter(target, filter2) {
        const listener = listeners.get(target);
        if (!listener)
          return;
        listener.filters.delete(filter2);
        if (!listener.filters.size) {
          listener.remove();
          listeners.delete(target);
        }
      }
      function query(all2, selector, parent, includeParent) {
        const checkParent = includeParent && matches.call(parent, selector);
        if (all2) {
          const queryAll = parent.querySelectorAll(selector);
          return checkParent ? [parent, ...queryAll] : [...queryAll];
        }
        return checkParent ? parent : parent.querySelector(selector);
      }
      function getOne(selector, parent, timeout) {
        return new Promise((resolve) => {
          const node = query(false, selector, parent, false);
          if (node)
            return resolve(node);
          let timer;
          const filter2 = (el) => {
            const node2 = query(false, selector, el, true);
            if (node2) {
              removeFilter(parent, filter2);
              timer && clearTimeout(timer);
              resolve(node2);
            }
          };
          addFilter(parent, filter2);
          if (timeout > 0) {
            timer = setTimeout(() => {
              removeFilter(parent, filter2);
              resolve(null);
            }, timeout);
          }
        });
      }
      function get(selector, ...args) {
        let parent = typeof args[0] !== "number" && args.shift() || doc;
        const timeout = args[0] || 0;
        if (Array.isArray(selector)) {
          return Promise.all(
            selector.map((s) => getOne(s, parent, timeout))
          );
        }
        return getOne(selector, parent, timeout);
      }
      function each(selector, ...args) {
        let parent = typeof args[0] !== "function" && args.shift() || doc;
        const callback = args[0];
        const refs = /* @__PURE__ */ new WeakSet();
        for (const node of query(true, selector, parent, false)) {
          refs.add(node);
          if (callback(node, false) === false)
            return;
        }
        const filter2 = (el) => {
          for (const node of query(true, selector, el, true)) {
            const _el = node;
            if (refs.has(_el))
              break;
            refs.add(_el);
            if (callback(node, true) === false) {
              return removeFilter(parent, filter2);
            }
          }
        };
        addFilter(parent, filter2);
      }
      async function rm(selector, ...args) {
        if (Array.isArray(selector)) {
          await Promise.all(
            selector.map((s) => {
              get(s, ...args).then((e) => e.remove());
            })
          );
        } else {
          await get(selector, ...args).then((e) => e.remove());
        }
      }
      const elmGetter = exports("e", {
        get,
        each,
        rm
      });
      const rootVue = ref();
      const getRootVue = async () => {
        if (rootVue.value)
          return rootVue.value;
        let wrap = await elmGetter.get("#wrap");
        if (wrap.__vue__)
          rootVue.value = wrap.__vue__;
        else {
          throw new Error("未找到vue根组件");
        }
        return rootVue.value;
      };
      const useHookVueData = exports("h", (selectors, key, data) => {
        return () => {
          const jobVue = document.querySelector(selectors).__vue__;
          data.value = jobVue[key];
          let originalSet = jobVue.__lookupSetter__(key);
          Object.defineProperty(jobVue, key, {
            set(val) {
              data.value = val;
              originalSet.call(this, val);
            }
          });
        };
      });
      const useHookVueFn = exports("k", (selectors, key) => {
        return () => {
          const jobVue = document.querySelector(selectors).__vue__;
          return jobVue[key];
        };
      });
      const userInfo = ref();
      const storeInit = async () => {
        var _a;
        const v = await getRootVue();
        const store = (_a = v == null ? void 0 : v.$store) == null ? void 0 : _a.state;
        userInfo.value = store == null ? void 0 : store.userInfo;
        logger.debug("userInfo: ", userInfo.value);
      };
      const useStore = exports("q", () => {
        return {
          storeInit,
          userInfo
        };
      });
      const _hoisted_1$1 = { style: { "align-items": "center", "display": "flex" } };
      const _hoisted_2 = { style: { "margin-left": "8px" } };
      const confUserKey = "conf-user";
      const _sfc_main$2 = /* @__PURE__ */ defineComponent({
        __name: "user",
        props: {
          "modelValue": { type: Boolean, ...{ required: true } },
          "modelModifiers": {}
        },
        emits: ["update:modelValue"],
        setup(__props) {
          const { formData: formData2 } = useConfFormData();
          const { todayData: todayData2 } = useStatistics();
          const { userInfo: userInfo2 } = useStore();
          const show = useModel(__props, "modelValue");
          const data = reactive(_GM_getValue(confUserKey, {}));
          const tableData = computed(() => Object.values(data));
          logger.debug("账户数据", toRaw(data));
          const currentRow = ref();
          const handleCurrentChange = (val) => {
            currentRow.value = val;
          };
          async function create(flag = true) {
            var _a, _b, _c, _d, _e, _f, _g;
            try {
              const list = await new Promise((resolve, reject) => {
                _GM_cookie.list({}, (cookies2, error) => {
                  if (error) {
                    reject(error);
                  } else {
                    resolve(cookies2);
                  }
                });
              });
              let uid = (_a = userInfo2.value) == null ? void 0 : _a.userId;
              if (!uid) {
                return;
              }
              uid = String(uid);
              data[uid] = {
                uid,
                user: ((_b = userInfo2.value) == null ? void 0 : _b.showName) || ((_c = userInfo2.value) == null ? void 0 : _c.name) || "nil",
                avatar: ((_d = userInfo2.value) == null ? void 0 : _d.tinyAvatar) || ((_e = userInfo2.value) == null ? void 0 : _e.largeAvatar) || "",
                remark: "",
                gender: ((_f = userInfo2.value) == null ? void 0 : _f.gender) === 0 ? "man" : "woman",
                flag: ((_g = userInfo2.value) == null ? void 0 : _g.studentFlag) ? "student" : "staff",
                date: (/* @__PURE__ */ new Date()).toLocaleString(),
                cookie: JSON.stringify(list),
                form: toRaw(formData2),
                statistics: toRaw(todayData2)
              };
              _GM_setValue(confUserKey, data);
              await Promise.all(
                list.map((item) => _GM_cookie.delete({ name: item.name }))
              );
              if (flag) {
                ElMessage.success("创建成功,开始清空ck并刷新");
                window.location.reload();
              }
            } catch (e) {
              ElMessage.error("遇到错误,请重试," + e);
              throw new Error("err", { cause: e });
            }
          }
          async function change() {
            try {
              const data2 = currentRow.value;
              if (!data2) {
                ElMessage.error("错误,空状态");
                return;
              }
              currentRow.value = void 0;
              await create(false);
              if (data2.form)
                _GM_setValue(formDataKey, data2.form);
              if (data2.statistics)
                _GM_setValue(todayKey, data2.statistics);
              const ck = JSON.parse(data2.cookie);
              await Promise.all(ck.map((c) => _GM_cookie.set(c)));
              ElMessage.success("切换完成,即将刷新");
              window.location.reload();
            } catch (e) {
              logger.error("错误,切换失败", e);
              if (e.name !== "err" || !e.name)
                ElMessage.error("错误,切换失败");
            }
          }
          function del(d) {
            delete data[d.uid];
            logger.debug(data);
            _GM_setValue(confUserKey, toRaw(data));
            ElMessage.success("删除成功");
          }
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElDialog), {
              modelValue: show.value,
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => show.value = $event),
              title: "账户配置",
              width: "70%",
              "align-center": "",
              "destroy-on-close": "",
              "z-index": 20
            }, {
              footer: withCtx(() => [
                createElementVNode("div", null, [
                  createVNode(unref(ElButton), {
                    onClick: _cache[0] || (_cache[0] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("取消")
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElPopconfirm), {
                    title: "确认后将保存数据退出账户并自动刷新",
                    onConfirm: _cache[1] || (_cache[1] = () => create())
                  }, {
                    reference: withCtx(() => [
                      createVNode(unref(ElButton), { type: "primary" }, {
                        default: withCtx(() => [
                          createTextVNode("新建&登出")
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    onClick: change,
                    disabled: !currentRow.value
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" 切换 ")
                    ]),
                    _: 1
                  }, 8, ["disabled"])
                ])
              ]),
              default: withCtx(() => [
                createVNode(unref(ElAlert), {
                  title: "使用该功能将会明文存储cookie信息,可能包含隐私信息",
                  type: "warning",
                  style: { "margin": "6px 0" }
                }),
                createVNode(unref(ElAlert), {
                  title: "每个用户都有自己的相关配置但历史投递等信息将全局共享,如果切换后是未登陆状态可能ck不完整或过期再次登陆即可(不要删除,不然配置会丢失)",
                  type: "info",
                  style: { "margin": "6px 0" }
                }),
                createVNode(unref(ElTable), {
                  data: tableData.value,
                  style: { "width": "100%" },
                  "highlight-current-row": "",
                  "table-layout": "auto",
                  onCurrentChange: handleCurrentChange
                }, {
                  default: withCtx(() => [
                    createVNode(unref(ElTableColumn), {
                      type: "index",
                      width: "40"
                    }),
                    createVNode(unref(ElTableColumn), { label: "账户" }, {
                      default: withCtx((scope) => [
                        createElementVNode("div", _hoisted_1$1, [
                          createVNode(unref(ElAvatar), {
                            src: scope.row.avatar,
                            size: 30
                          }, null, 8, ["src"]),
                          createElementVNode("span", _hoisted_2, toDisplayString(scope.row.user), 1)
                        ])
                      ]),
                      _: 1
                    }),
                    createVNode(unref(ElTableColumn), {
                      label: "性别",
                      align: "center"
                    }, {
                      default: withCtx((scope) => [
                        createVNode(unref(ElTag), {
                          round: "",
                          effect: "dark",
                          style: { "border-style": "none" },
                          color: scope.row.gender === "man" ? "#9BC1FE" : "#FFBDEB"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(scope.row.gender === "man" ? "可爱男孩" : "温柔女孩"), 1)
                          ]),
                          _: 2
                        }, 1032, ["color"])
                      ]),
                      _: 1
                    }),
                    createVNode(unref(ElTableColumn), {
                      label: "身份",
                      align: "center"
                    }, {
                      default: withCtx((scope) => [
                        createVNode(unref(ElTag), {
                          effect: "dark",
                          round: "",
                          style: { "border-style": "none" },
                          type: scope.row.flag === "student" ? "success" : "warning"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(scope.row.flag === "student" ? "哈巴学生" : "无情社畜"), 1)
                          ]),
                          _: 2
                        }, 1032, ["type"])
                      ]),
                      _: 1
                    }),
                    createVNode(unref(ElTableColumn), {
                      prop: "date",
                      label: "上次登录"
                    }),
                    createVNode(unref(ElTableColumn), {
                      fixed: "right",
                      label: "操作"
                    }, {
                      default: withCtx((scope) => [
                        createVNode(unref(ElButton), {
                          link: "",
                          type: "primary",
                          size: "small",
                          disabled: ""
                        }, {
                          default: withCtx(() => [
                            createTextVNode("导出")
                          ]),
                          _: 1
                        }),
                        createVNode(unref(ElButton), {
                          link: "",
                          type: "primary",
                          size: "small",
                          onClick: () => del(scope.row)
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" 删除 ")
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["data"])
              ]),
              _: 1
            }, 8, ["modelValue"]);
          };
        }
      });
      const _hoisted_1 = /* @__PURE__ */ createElementVNode("span", null, "Log", -1);
      const _sfc_main$1 = /* @__PURE__ */ defineComponent({
        __name: "log",
        props: {
          "modelValue": { type: Boolean, ...{ required: true } },
          "modelModifiers": {}
        },
        emits: ["update:modelValue"],
        setup(__props) {
          const show = useModel(__props, "modelValue");
          return (_ctx, _cache) => {
            const _component_el_button = resolveComponent("el-button");
            return openBlock(), createBlock(unref(ElDialog), {
              modelValue: show.value,
              "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => show.value = $event),
              title: "日志查看",
              width: "500",
              "align-center": "",
              "destroy-on-close": "",
              "z-index": 20
            }, {
              footer: withCtx(() => [
                createElementVNode("div", null, [
                  createVNode(_component_el_button, {
                    onClick: _cache[0] || (_cache[0] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("Cancel")
                    ]),
                    _: 1
                  }),
                  createVNode(_component_el_button, {
                    type: "primary",
                    onClick: _cache[1] || (_cache[1] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("Confirm")
                    ]),
                    _: 1
                  })
                ])
              ]),
              default: withCtx(() => [
                _hoisted_1
              ]),
              _: 1
            }, 8, ["modelValue"]);
          };
        }
      });
      const _sfc_main = /* @__PURE__ */ defineComponent({
        __name: "App",
        setup(__props) {
          logger.info("BoosHelper挂载成功");
          ElMessage("BoosHelper挂载成功!");
          const { storeInit: storeInit2 } = useStore();
          const confBox = ref(false);
          const confs = {
            store: { name: "存储配置", component: _sfc_main$3 },
            user: { name: "账号配置", component: _sfc_main$2 },
            log: { name: "日志配置", component: _sfc_main$1 }
          };
          const confKey = ref("store");
          const dark = ref(_GM_getValue("theme-dark", false));
          function themeChange() {
            dark.value = !dark.value;
            if (dark.value) {
              ElMessage({
                message: "已切换到暗黑模式，如有样式没适配且严重影响使用，请反馈",
                duration: 5e3,
                showClose: true
              });
            }
            document.documentElement.classList.toggle("dark", dark.value);
            _GM_setValue("theme-dark", dark.value);
          }
          onMounted(async () => {
            await storeInit2();
          });
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock(Fragment, null, [
              createVNode(unref(ElDropdown), { trigger: "click" }, {
                dropdown: withCtx(() => [
                  createVNode(unref(ElDropdownMenu), null, {
                    default: withCtx(() => [
                      createVNode(unref(ElDropdownItem), { disabled: "" }, {
                        default: withCtx(() => [
                          createTextVNode("BossHelp配置项")
                        ]),
                        _: 1
                      }),
                      createVNode(unref(ElDropdownItem), {
                        divided: "",
                        disabled: ""
                      }),
                      (openBlock(), createElementBlock(Fragment, null, renderList(confs, (v, k) => {
                        return createVNode(unref(ElDropdownItem), {
                          key: k,
                          onClick: ($event) => {
                            confKey.value = k;
                            confBox.value = true;
                          }
                        }, {
                          default: withCtx(() => [
                            createTextVNode(toDisplayString(v.name), 1)
                          ]),
                          _: 2
                        }, 1032, ["onClick"]);
                      }), 64)),
                      createVNode(unref(ElDropdownItem), { onClick: themeChange }, {
                        default: withCtx(() => [
                          createTextVNode(" 暗黑模式（" + toDisplayString(dark.value ? "开" : "关") + "） ", 1)
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  })
                ]),
                default: withCtx(() => [
                  createVNode(unref(ElAvatar), {
                    size: 30,
                    src: "https://avatars.githubusercontent.com/u/68412205?v=4"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" H ")
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }),
              (openBlock(), createBlock(Teleport, { to: "body" }, [
                (openBlock(), createBlock(resolveDynamicComponent(confs[confKey.value].component), {
                  modelValue: confBox.value,
                  "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => confBox.value = $event)
                }, null, 8, ["modelValue"]))
              ]))
            ], 64);
          };
        }
      });
      function bind(fn, thisArg) {
        return function wrap() {
          return fn.apply(thisArg, arguments);
        };
      }
      const { toString } = Object.prototype;
      const { getPrototypeOf } = Object;
      const kindOf = /* @__PURE__ */ ((cache) => (thing) => {
        const str = toString.call(thing);
        return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
      })(/* @__PURE__ */ Object.create(null));
      const kindOfTest = (type) => {
        type = type.toLowerCase();
        return (thing) => kindOf(thing) === type;
      };
      const typeOfTest = (type) => (thing) => typeof thing === type;
      const { isArray } = Array;
      const isUndefined = typeOfTest("undefined");
      function isBuffer(val) {
        return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
      }
      const isArrayBuffer = kindOfTest("ArrayBuffer");
      function isArrayBufferView(val) {
        let result;
        if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
          result = ArrayBuffer.isView(val);
        } else {
          result = val && val.buffer && isArrayBuffer(val.buffer);
        }
        return result;
      }
      const isString = typeOfTest("string");
      const isFunction = typeOfTest("function");
      const isNumber = typeOfTest("number");
      const isObject = (thing) => thing !== null && typeof thing === "object";
      const isBoolean = (thing) => thing === true || thing === false;
      const isPlainObject = (val) => {
        if (kindOf(val) !== "object") {
          return false;
        }
        const prototype2 = getPrototypeOf(val);
        return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);
      };
      const isDate = kindOfTest("Date");
      const isFile = kindOfTest("File");
      const isBlob = kindOfTest("Blob");
      const isFileList = kindOfTest("FileList");
      const isStream = (val) => isObject(val) && isFunction(val.pipe);
      const isFormData = (thing) => {
        let kind;
        return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || // detect form-data instance
        kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
      };
      const isURLSearchParams = kindOfTest("URLSearchParams");
      const trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
      function forEach(obj, fn, { allOwnKeys = false } = {}) {
        if (obj === null || typeof obj === "undefined") {
          return;
        }
        let i;
        let l;
        if (typeof obj !== "object") {
          obj = [obj];
        }
        if (isArray(obj)) {
          for (i = 0, l = obj.length; i < l; i++) {
            fn.call(null, obj[i], i, obj);
          }
        } else {
          const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
          const len = keys.length;
          let key;
          for (i = 0; i < len; i++) {
            key = keys[i];
            fn.call(null, obj[key], key, obj);
          }
        }
      }
      function findKey(obj, key) {
        key = key.toLowerCase();
        const keys = Object.keys(obj);
        let i = keys.length;
        let _key;
        while (i-- > 0) {
          _key = keys[i];
          if (key === _key.toLowerCase()) {
            return _key;
          }
        }
        return null;
      }
      const _global = (() => {
        if (typeof globalThis !== "undefined")
          return globalThis;
        return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
      })();
      const isContextDefined = (context) => !isUndefined(context) && context !== _global;
      function merge() {
        const { caseless } = isContextDefined(this) && this || {};
        const result = {};
        const assignValue = (val, key) => {
          const targetKey = caseless && findKey(result, key) || key;
          if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
            result[targetKey] = merge(result[targetKey], val);
          } else if (isPlainObject(val)) {
            result[targetKey] = merge({}, val);
          } else if (isArray(val)) {
            result[targetKey] = val.slice();
          } else {
            result[targetKey] = val;
          }
        };
        for (let i = 0, l = arguments.length; i < l; i++) {
          arguments[i] && forEach(arguments[i], assignValue);
        }
        return result;
      }
      const extend = (a, b, thisArg, { allOwnKeys } = {}) => {
        forEach(b, (val, key) => {
          if (thisArg && isFunction(val)) {
            a[key] = bind(val, thisArg);
          } else {
            a[key] = val;
          }
        }, { allOwnKeys });
        return a;
      };
      const stripBOM = (content) => {
        if (content.charCodeAt(0) === 65279) {
          content = content.slice(1);
        }
        return content;
      };
      const inherits = (constructor, superConstructor, props, descriptors2) => {
        constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
        constructor.prototype.constructor = constructor;
        Object.defineProperty(constructor, "super", {
          value: superConstructor.prototype
        });
        props && Object.assign(constructor.prototype, props);
      };
      const toFlatObject = (sourceObj, destObj, filter2, propFilter) => {
        let props;
        let i;
        let prop;
        const merged = {};
        destObj = destObj || {};
        if (sourceObj == null)
          return destObj;
        do {
          props = Object.getOwnPropertyNames(sourceObj);
          i = props.length;
          while (i-- > 0) {
            prop = props[i];
            if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
              destObj[prop] = sourceObj[prop];
              merged[prop] = true;
            }
          }
          sourceObj = filter2 !== false && getPrototypeOf(sourceObj);
        } while (sourceObj && (!filter2 || filter2(sourceObj, destObj)) && sourceObj !== Object.prototype);
        return destObj;
      };
      const endsWith = (str, searchString, position) => {
        str = String(str);
        if (position === void 0 || position > str.length) {
          position = str.length;
        }
        position -= searchString.length;
        const lastIndex = str.indexOf(searchString, position);
        return lastIndex !== -1 && lastIndex === position;
      };
      const toArray = (thing) => {
        if (!thing)
          return null;
        if (isArray(thing))
          return thing;
        let i = thing.length;
        if (!isNumber(i))
          return null;
        const arr = new Array(i);
        while (i-- > 0) {
          arr[i] = thing[i];
        }
        return arr;
      };
      const isTypedArray = /* @__PURE__ */ ((TypedArray) => {
        return (thing) => {
          return TypedArray && thing instanceof TypedArray;
        };
      })(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
      const forEachEntry = (obj, fn) => {
        const generator = obj && obj[Symbol.iterator];
        const iterator = generator.call(obj);
        let result;
        while ((result = iterator.next()) && !result.done) {
          const pair = result.value;
          fn.call(obj, pair[0], pair[1]);
        }
      };
      const matchAll = (regExp, str) => {
        let matches2;
        const arr = [];
        while ((matches2 = regExp.exec(str)) !== null) {
          arr.push(matches2);
        }
        return arr;
      };
      const isHTMLForm = kindOfTest("HTMLFormElement");
      const toCamelCase = (str) => {
        return str.toLowerCase().replace(
          /[-_\s]([a-z\d])(\w*)/g,
          function replacer(m, p1, p2) {
            return p1.toUpperCase() + p2;
          }
        );
      };
      const hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
      const isRegExp = kindOfTest("RegExp");
      const reduceDescriptors = (obj, reducer) => {
        const descriptors2 = Object.getOwnPropertyDescriptors(obj);
        const reducedDescriptors = {};
        forEach(descriptors2, (descriptor, name) => {
          let ret;
          if ((ret = reducer(descriptor, name, obj)) !== false) {
            reducedDescriptors[name] = ret || descriptor;
          }
        });
        Object.defineProperties(obj, reducedDescriptors);
      };
      const freezeMethods = (obj) => {
        reduceDescriptors(obj, (descriptor, name) => {
          if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name) !== -1) {
            return false;
          }
          const value = obj[name];
          if (!isFunction(value))
            return;
          descriptor.enumerable = false;
          if ("writable" in descriptor) {
            descriptor.writable = false;
            return;
          }
          if (!descriptor.set) {
            descriptor.set = () => {
              throw Error("Can not rewrite read-only method '" + name + "'");
            };
          }
        });
      };
      const toObjectSet = (arrayOrString, delimiter) => {
        const obj = {};
        const define = (arr) => {
          arr.forEach((value) => {
            obj[value] = true;
          });
        };
        isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));
        return obj;
      };
      const noop = () => {
      };
      const toFiniteNumber = (value, defaultValue) => {
        value = +value;
        return Number.isFinite(value) ? value : defaultValue;
      };
      const ALPHA = "abcdefghijklmnopqrstuvwxyz";
      const DIGIT = "0123456789";
      const ALPHABET = {
        DIGIT,
        ALPHA,
        ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT
      };
      const generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT) => {
        let str = "";
        const { length } = alphabet;
        while (size--) {
          str += alphabet[Math.random() * length | 0];
        }
        return str;
      };
      function isSpecCompliantForm(thing) {
        return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === "FormData" && thing[Symbol.iterator]);
      }
      const toJSONObject = (obj) => {
        const stack = new Array(10);
        const visit = (source, i) => {
          if (isObject(source)) {
            if (stack.indexOf(source) >= 0) {
              return;
            }
            if (!("toJSON" in source)) {
              stack[i] = source;
              const target = isArray(source) ? [] : {};
              forEach(source, (value, key) => {
                const reducedValue = visit(value, i + 1);
                !isUndefined(reducedValue) && (target[key] = reducedValue);
              });
              stack[i] = void 0;
              return target;
            }
          }
          return source;
        };
        return visit(obj, 0);
      };
      const isAsyncFn = kindOfTest("AsyncFunction");
      const isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
      const utils$1 = {
        isArray,
        isArrayBuffer,
        isBuffer,
        isFormData,
        isArrayBufferView,
        isString,
        isNumber,
        isBoolean,
        isObject,
        isPlainObject,
        isUndefined,
        isDate,
        isFile,
        isBlob,
        isRegExp,
        isFunction,
        isStream,
        isURLSearchParams,
        isTypedArray,
        isFileList,
        forEach,
        merge,
        extend,
        trim,
        stripBOM,
        inherits,
        toFlatObject,
        kindOf,
        kindOfTest,
        endsWith,
        toArray,
        forEachEntry,
        matchAll,
        isHTMLForm,
        hasOwnProperty,
        hasOwnProp: hasOwnProperty,
        // an alias to avoid ESLint no-prototype-builtins detection
        reduceDescriptors,
        freezeMethods,
        toObjectSet,
        toCamelCase,
        noop,
        toFiniteNumber,
        findKey,
        global: _global,
        isContextDefined,
        ALPHABET,
        generateString,
        isSpecCompliantForm,
        toJSONObject,
        isAsyncFn,
        isThenable
      };
      function AxiosError(message, code, config, request, response) {
        Error.call(this);
        if (Error.captureStackTrace) {
          Error.captureStackTrace(this, this.constructor);
        } else {
          this.stack = new Error().stack;
        }
        this.message = message;
        this.name = "AxiosError";
        code && (this.code = code);
        config && (this.config = config);
        request && (this.request = request);
        response && (this.response = response);
      }
      utils$1.inherits(AxiosError, Error, {
        toJSON: function toJSON() {
          return {
            // Standard
            message: this.message,
            name: this.name,
            // Microsoft
            description: this.description,
            number: this.number,
            // Mozilla
            fileName: this.fileName,
            lineNumber: this.lineNumber,
            columnNumber: this.columnNumber,
            stack: this.stack,
            // Axios
            config: utils$1.toJSONObject(this.config),
            code: this.code,
            status: this.response && this.response.status ? this.response.status : null
          };
        }
      });
      const prototype$1 = AxiosError.prototype;
      const descriptors = {};
      [
        "ERR_BAD_OPTION_VALUE",
        "ERR_BAD_OPTION",
        "ECONNABORTED",
        "ETIMEDOUT",
        "ERR_NETWORK",
        "ERR_FR_TOO_MANY_REDIRECTS",
        "ERR_DEPRECATED",
        "ERR_BAD_RESPONSE",
        "ERR_BAD_REQUEST",
        "ERR_CANCELED",
        "ERR_NOT_SUPPORT",
        "ERR_INVALID_URL"
        // eslint-disable-next-line func-names
      ].forEach((code) => {
        descriptors[code] = { value: code };
      });
      Object.defineProperties(AxiosError, descriptors);
      Object.defineProperty(prototype$1, "isAxiosError", { value: true });
      AxiosError.from = (error, code, config, request, response, customProps) => {
        const axiosError = Object.create(prototype$1);
        utils$1.toFlatObject(error, axiosError, function filter2(obj) {
          return obj !== Error.prototype;
        }, (prop) => {
          return prop !== "isAxiosError";
        });
        AxiosError.call(axiosError, error.message, code, config, request, response);
        axiosError.cause = error;
        axiosError.name = error.name;
        customProps && Object.assign(axiosError, customProps);
        return axiosError;
      };
      const httpAdapter = null;
      function isVisitable(thing) {
        return utils$1.isPlainObject(thing) || utils$1.isArray(thing);
      }
      function removeBrackets(key) {
        return utils$1.endsWith(key, "[]") ? key.slice(0, -2) : key;
      }
      function renderKey(path, key, dots) {
        if (!path)
          return key;
        return path.concat(key).map(function each2(token, i) {
          token = removeBrackets(token);
          return !dots && i ? "[" + token + "]" : token;
        }).join(dots ? "." : "");
      }
      function isFlatArray(arr) {
        return utils$1.isArray(arr) && !arr.some(isVisitable);
      }
      const predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {
        return /^is[A-Z]/.test(prop);
      });
      function toFormData(obj, formData2, options) {
        if (!utils$1.isObject(obj)) {
          throw new TypeError("target must be an object");
        }
        formData2 = formData2 || new FormData();
        options = utils$1.toFlatObject(options, {
          metaTokens: true,
          dots: false,
          indexes: false
        }, false, function defined(option, source) {
          return !utils$1.isUndefined(source[option]);
        });
        const metaTokens = options.metaTokens;
        const visitor = options.visitor || defaultVisitor;
        const dots = options.dots;
        const indexes = options.indexes;
        const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
        const useBlob = _Blob && utils$1.isSpecCompliantForm(formData2);
        if (!utils$1.isFunction(visitor)) {
          throw new TypeError("visitor must be a function");
        }
        function convertValue(value) {
          if (value === null)
            return "";
          if (utils$1.isDate(value)) {
            return value.toISOString();
          }
          if (!useBlob && utils$1.isBlob(value)) {
            throw new AxiosError("Blob is not supported. Use a Buffer instead.");
          }
          if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {
            return useBlob && typeof Blob === "function" ? new Blob([value]) : Buffer.from(value);
          }
          return value;
        }
        function defaultVisitor(value, key, path) {
          let arr = value;
          if (value && !path && typeof value === "object") {
            if (utils$1.endsWith(key, "{}")) {
              key = metaTokens ? key : key.slice(0, -2);
              value = JSON.stringify(value);
            } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, "[]")) && (arr = utils$1.toArray(value))) {
              key = removeBrackets(key);
              arr.forEach(function each2(el, index) {
                !(utils$1.isUndefined(el) || el === null) && formData2.append(
                  // eslint-disable-next-line no-nested-ternary
                  indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + "[]",
                  convertValue(el)
                );
              });
              return false;
            }
          }
          if (isVisitable(value)) {
            return true;
          }
          formData2.append(renderKey(path, key, dots), convertValue(value));
          return false;
        }
        const stack = [];
        const exposedHelpers = Object.assign(predicates, {
          defaultVisitor,
          convertValue,
          isVisitable
        });
        function build(value, path) {
          if (utils$1.isUndefined(value))
            return;
          if (stack.indexOf(value) !== -1) {
            throw Error("Circular reference detected in " + path.join("."));
          }
          stack.push(value);
          utils$1.forEach(value, function each2(el, key) {
            const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(
              formData2,
              el,
              utils$1.isString(key) ? key.trim() : key,
              path,
              exposedHelpers
            );
            if (result === true) {
              build(el, path ? path.concat(key) : [key]);
            }
          });
          stack.pop();
        }
        if (!utils$1.isObject(obj)) {
          throw new TypeError("data must be an object");
        }
        build(obj);
        return formData2;
      }
      function encode$1(str) {
        const charMap = {
          "!": "%21",
          "'": "%27",
          "(": "%28",
          ")": "%29",
          "~": "%7E",
          "%20": "+",
          "%00": "\0"
        };
        return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
          return charMap[match];
        });
      }
      function AxiosURLSearchParams(params, options) {
        this._pairs = [];
        params && toFormData(params, this, options);
      }
      const prototype = AxiosURLSearchParams.prototype;
      prototype.append = function append(name, value) {
        this._pairs.push([name, value]);
      };
      prototype.toString = function toString2(encoder) {
        const _encode = encoder ? function(value) {
          return encoder.call(this, value, encode$1);
        } : encode$1;
        return this._pairs.map(function each2(pair) {
          return _encode(pair[0]) + "=" + _encode(pair[1]);
        }, "").join("&");
      };
      function encode(val) {
        return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
      }
      function buildURL(url, params, options) {
        if (!params) {
          return url;
        }
        const _encode = options && options.encode || encode;
        const serializeFn = options && options.serialize;
        let serializedParams;
        if (serializeFn) {
          serializedParams = serializeFn(params, options);
        } else {
          serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);
        }
        if (serializedParams) {
          const hashmarkIndex = url.indexOf("#");
          if (hashmarkIndex !== -1) {
            url = url.slice(0, hashmarkIndex);
          }
          url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
        }
        return url;
      }
      class InterceptorManager {
        constructor() {
          this.handlers = [];
        }
        /**
         * Add a new interceptor to the stack
         *
         * @param {Function} fulfilled The function to handle `then` for a `Promise`
         * @param {Function} rejected The function to handle `reject` for a `Promise`
         *
         * @return {Number} An ID used to remove interceptor later
         */
        use(fulfilled, rejected, options) {
          this.handlers.push({
            fulfilled,
            rejected,
            synchronous: options ? options.synchronous : false,
            runWhen: options ? options.runWhen : null
          });
          return this.handlers.length - 1;
        }
        /**
         * Remove an interceptor from the stack
         *
         * @param {Number} id The ID that was returned by `use`
         *
         * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
         */
        eject(id) {
          if (this.handlers[id]) {
            this.handlers[id] = null;
          }
        }
        /**
         * Clear all interceptors from the stack
         *
         * @returns {void}
         */
        clear() {
          if (this.handlers) {
            this.handlers = [];
          }
        }
        /**
         * Iterate over all the registered interceptors
         *
         * This method is particularly useful for skipping over any
         * interceptors that may have become `null` calling `eject`.
         *
         * @param {Function} fn The function to call for each interceptor
         *
         * @returns {void}
         */
        forEach(fn) {
          utils$1.forEach(this.handlers, function forEachHandler(h) {
            if (h !== null) {
              fn(h);
            }
          });
        }
      }
      const transitionalDefaults = {
        silentJSONParsing: true,
        forcedJSONParsing: true,
        clarifyTimeoutError: false
      };
      const URLSearchParams$1 = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams;
      const FormData$1 = typeof FormData !== "undefined" ? FormData : null;
      const Blob$1 = typeof Blob !== "undefined" ? Blob : null;
      const platform$1 = {
        isBrowser: true,
        classes: {
          URLSearchParams: URLSearchParams$1,
          FormData: FormData$1,
          Blob: Blob$1
        },
        protocols: ["http", "https", "file", "blob", "url", "data"]
      };
      const hasBrowserEnv = typeof window !== "undefined" && typeof document !== "undefined";
      const hasStandardBrowserEnv = ((product) => {
        return hasBrowserEnv && ["ReactNative", "NativeScript", "NS"].indexOf(product) < 0;
      })(typeof navigator !== "undefined" && navigator.product);
      const hasStandardBrowserWebWorkerEnv = (() => {
        return typeof WorkerGlobalScope !== "undefined" && // eslint-disable-next-line no-undef
        self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
      })();
      const utils = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
        __proto__: null,
        hasBrowserEnv,
        hasStandardBrowserEnv,
        hasStandardBrowserWebWorkerEnv
      }, Symbol.toStringTag, { value: "Module" }));
      const platform = {
        ...utils,
        ...platform$1
      };
      function toURLEncodedForm(data, options) {
        return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({
          visitor: function(value, key, path, helpers) {
            if (platform.isNode && utils$1.isBuffer(value)) {
              this.append(key, value.toString("base64"));
              return false;
            }
            return helpers.defaultVisitor.apply(this, arguments);
          }
        }, options));
      }
      function parsePropPath(name) {
        return utils$1.matchAll(/\w+|\[(\w*)]/g, name).map((match) => {
          return match[0] === "[]" ? "" : match[1] || match[0];
        });
      }
      function arrayToObject(arr) {
        const obj = {};
        const keys = Object.keys(arr);
        let i;
        const len = keys.length;
        let key;
        for (i = 0; i < len; i++) {
          key = keys[i];
          obj[key] = arr[key];
        }
        return obj;
      }
      function formDataToJSON(formData2) {
        function buildPath(path, value, target, index) {
          let name = path[index++];
          if (name === "__proto__")
            return true;
          const isNumericKey = Number.isFinite(+name);
          const isLast = index >= path.length;
          name = !name && utils$1.isArray(target) ? target.length : name;
          if (isLast) {
            if (utils$1.hasOwnProp(target, name)) {
              target[name] = [target[name], value];
            } else {
              target[name] = value;
            }
            return !isNumericKey;
          }
          if (!target[name] || !utils$1.isObject(target[name])) {
            target[name] = [];
          }
          const result = buildPath(path, value, target[name], index);
          if (result && utils$1.isArray(target[name])) {
            target[name] = arrayToObject(target[name]);
          }
          return !isNumericKey;
        }
        if (utils$1.isFormData(formData2) && utils$1.isFunction(formData2.entries)) {
          const obj = {};
          utils$1.forEachEntry(formData2, (name, value) => {
            buildPath(parsePropPath(name), value, obj, 0);
          });
          return obj;
        }
        return null;
      }
      function stringifySafely(rawValue, parser, encoder) {
        if (utils$1.isString(rawValue)) {
          try {
            (parser || JSON.parse)(rawValue);
            return utils$1.trim(rawValue);
          } catch (e) {
            if (e.name !== "SyntaxError") {
              throw e;
            }
          }
        }
        return (encoder || JSON.stringify)(rawValue);
      }
      const defaults = {
        transitional: transitionalDefaults,
        adapter: ["xhr", "http"],
        transformRequest: [function transformRequest(data, headers) {
          const contentType = headers.getContentType() || "";
          const hasJSONContentType = contentType.indexOf("application/json") > -1;
          const isObjectPayload = utils$1.isObject(data);
          if (isObjectPayload && utils$1.isHTMLForm(data)) {
            data = new FormData(data);
          }
          const isFormData2 = utils$1.isFormData(data);
          if (isFormData2) {
            return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;
          }
          if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data)) {
            return data;
          }
          if (utils$1.isArrayBufferView(data)) {
            return data.buffer;
          }
          if (utils$1.isURLSearchParams(data)) {
            headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
            return data.toString();
          }
          let isFileList2;
          if (isObjectPayload) {
            if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
              return toURLEncodedForm(data, this.formSerializer).toString();
            }
            if ((isFileList2 = utils$1.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
              const _FormData = this.env && this.env.FormData;
              return toFormData(
                isFileList2 ? { "files[]": data } : data,
                _FormData && new _FormData(),
                this.formSerializer
              );
            }
          }
          if (isObjectPayload || hasJSONContentType) {
            headers.setContentType("application/json", false);
            return stringifySafely(data);
          }
          return data;
        }],
        transformResponse: [function transformResponse(data) {
          const transitional2 = this.transitional || defaults.transitional;
          const forcedJSONParsing = transitional2 && transitional2.forcedJSONParsing;
          const JSONRequested = this.responseType === "json";
          if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
            const silentJSONParsing = transitional2 && transitional2.silentJSONParsing;
            const strictJSONParsing = !silentJSONParsing && JSONRequested;
            try {
              return JSON.parse(data);
            } catch (e) {
              if (strictJSONParsing) {
                if (e.name === "SyntaxError") {
                  throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);
                }
                throw e;
              }
            }
          }
          return data;
        }],
        /**
         * A timeout in milliseconds to abort a request. If set to 0 (default) a
         * timeout is not created.
         */
        timeout: 0,
        xsrfCookieName: "XSRF-TOKEN",
        xsrfHeaderName: "X-XSRF-TOKEN",
        maxContentLength: -1,
        maxBodyLength: -1,
        env: {
          FormData: platform.classes.FormData,
          Blob: platform.classes.Blob
        },
        validateStatus: function validateStatus(status) {
          return status >= 200 && status < 300;
        },
        headers: {
          common: {
            "Accept": "application/json, text/plain, */*",
            "Content-Type": void 0
          }
        }
      };
      utils$1.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
        defaults.headers[method] = {};
      });
      const defaults$1 = defaults;
      const ignoreDuplicateOf = utils$1.toObjectSet([
        "age",
        "authorization",
        "content-length",
        "content-type",
        "etag",
        "expires",
        "from",
        "host",
        "if-modified-since",
        "if-unmodified-since",
        "last-modified",
        "location",
        "max-forwards",
        "proxy-authorization",
        "referer",
        "retry-after",
        "user-agent"
      ]);
      const parseHeaders = (rawHeaders) => {
        const parsed = {};
        let key;
        let val;
        let i;
        rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
          i = line.indexOf(":");
          key = line.substring(0, i).trim().toLowerCase();
          val = line.substring(i + 1).trim();
          if (!key || parsed[key] && ignoreDuplicateOf[key]) {
            return;
          }
          if (key === "set-cookie") {
            if (parsed[key]) {
              parsed[key].push(val);
            } else {
              parsed[key] = [val];
            }
          } else {
            parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
          }
        });
        return parsed;
      };
      const $internals = Symbol("internals");
      function normalizeHeader(header) {
        return header && String(header).trim().toLowerCase();
      }
      function normalizeValue(value) {
        if (value === false || value == null) {
          return value;
        }
        return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);
      }
      function parseTokens(str) {
        const tokens = /* @__PURE__ */ Object.create(null);
        const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
        let match;
        while (match = tokensRE.exec(str)) {
          tokens[match[1]] = match[2];
        }
        return tokens;
      }
      const isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
      function matchHeaderValue(context, value, header, filter2, isHeaderNameFilter) {
        if (utils$1.isFunction(filter2)) {
          return filter2.call(this, value, header);
        }
        if (isHeaderNameFilter) {
          value = header;
        }
        if (!utils$1.isString(value))
          return;
        if (utils$1.isString(filter2)) {
          return value.indexOf(filter2) !== -1;
        }
        if (utils$1.isRegExp(filter2)) {
          return filter2.test(value);
        }
      }
      function formatHeader(header) {
        return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w, char, str) => {
          return char.toUpperCase() + str;
        });
      }
      function buildAccessors(obj, header) {
        const accessorName = utils$1.toCamelCase(" " + header);
        ["get", "set", "has"].forEach((methodName) => {
          Object.defineProperty(obj, methodName + accessorName, {
            value: function(arg1, arg2, arg3) {
              return this[methodName].call(this, header, arg1, arg2, arg3);
            },
            configurable: true
          });
        });
      }
      class AxiosHeaders {
        constructor(headers) {
          headers && this.set(headers);
        }
        set(header, valueOrRewrite, rewrite) {
          const self2 = this;
          function setHeader(_value, _header, _rewrite) {
            const lHeader = normalizeHeader(_header);
            if (!lHeader) {
              throw new Error("header name must be a non-empty string");
            }
            const key = utils$1.findKey(self2, lHeader);
            if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
              self2[key || _header] = normalizeValue(_value);
            }
          }
          const setHeaders = (headers, _rewrite) => utils$1.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
          if (utils$1.isPlainObject(header) || header instanceof this.constructor) {
            setHeaders(header, valueOrRewrite);
          } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
            setHeaders(parseHeaders(header), valueOrRewrite);
          } else {
            header != null && setHeader(valueOrRewrite, header, rewrite);
          }
          return this;
        }
        get(header, parser) {
          header = normalizeHeader(header);
          if (header) {
            const key = utils$1.findKey(this, header);
            if (key) {
              const value = this[key];
              if (!parser) {
                return value;
              }
              if (parser === true) {
                return parseTokens(value);
              }
              if (utils$1.isFunction(parser)) {
                return parser.call(this, value, key);
              }
              if (utils$1.isRegExp(parser)) {
                return parser.exec(value);
              }
              throw new TypeError("parser must be boolean|regexp|function");
            }
          }
        }
        has(header, matcher) {
          header = normalizeHeader(header);
          if (header) {
            const key = utils$1.findKey(this, header);
            return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
          }
          return false;
        }
        delete(header, matcher) {
          const self2 = this;
          let deleted = false;
          function deleteHeader(_header) {
            _header = normalizeHeader(_header);
            if (_header) {
              const key = utils$1.findKey(self2, _header);
              if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
                delete self2[key];
                deleted = true;
              }
            }
          }
          if (utils$1.isArray(header)) {
            header.forEach(deleteHeader);
          } else {
            deleteHeader(header);
          }
          return deleted;
        }
        clear(matcher) {
          const keys = Object.keys(this);
          let i = keys.length;
          let deleted = false;
          while (i--) {
            const key = keys[i];
            if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
              delete this[key];
              deleted = true;
            }
          }
          return deleted;
        }
        normalize(format) {
          const self2 = this;
          const headers = {};
          utils$1.forEach(this, (value, header) => {
            const key = utils$1.findKey(headers, header);
            if (key) {
              self2[key] = normalizeValue(value);
              delete self2[header];
              return;
            }
            const normalized = format ? formatHeader(header) : String(header).trim();
            if (normalized !== header) {
              delete self2[header];
            }
            self2[normalized] = normalizeValue(value);
            headers[normalized] = true;
          });
          return this;
        }
        concat(...targets) {
          return this.constructor.concat(this, ...targets);
        }
        toJSON(asStrings) {
          const obj = /* @__PURE__ */ Object.create(null);
          utils$1.forEach(this, (value, header) => {
            value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(", ") : value);
          });
          return obj;
        }
        [Symbol.iterator]() {
          return Object.entries(this.toJSON())[Symbol.iterator]();
        }
        toString() {
          return Object.entries(this.toJSON()).map(([header, value]) => header + ": " + value).join("\n");
        }
        get [Symbol.toStringTag]() {
          return "AxiosHeaders";
        }
        static from(thing) {
          return thing instanceof this ? thing : new this(thing);
        }
        static concat(first, ...targets) {
          const computed2 = new this(first);
          targets.forEach((target) => computed2.set(target));
          return computed2;
        }
        static accessor(header) {
          const internals = this[$internals] = this[$internals] = {
            accessors: {}
          };
          const accessors = internals.accessors;
          const prototype2 = this.prototype;
          function defineAccessor(_header) {
            const lHeader = normalizeHeader(_header);
            if (!accessors[lHeader]) {
              buildAccessors(prototype2, _header);
              accessors[lHeader] = true;
            }
          }
          utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
          return this;
        }
      }
      AxiosHeaders.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
      utils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key) => {
        let mapped = key[0].toUpperCase() + key.slice(1);
        return {
          get: () => value,
          set(headerValue) {
            this[mapped] = headerValue;
          }
        };
      });
      utils$1.freezeMethods(AxiosHeaders);
      const AxiosHeaders$1 = AxiosHeaders;
      function transformData(fns, response) {
        const config = this || defaults$1;
        const context = response || config;
        const headers = AxiosHeaders$1.from(context.headers);
        let data = context.data;
        utils$1.forEach(fns, function transform(fn) {
          data = fn.call(config, data, headers.normalize(), response ? response.status : void 0);
        });
        headers.normalize();
        return data;
      }
      function isCancel(value) {
        return !!(value && value.__CANCEL__);
      }
      function CanceledError(message, config, request) {
        AxiosError.call(this, message == null ? "canceled" : message, AxiosError.ERR_CANCELED, config, request);
        this.name = "CanceledError";
      }
      utils$1.inherits(CanceledError, AxiosError, {
        __CANCEL__: true
      });
      function settle(resolve, reject, response) {
        const validateStatus2 = response.config.validateStatus;
        if (!response.status || !validateStatus2 || validateStatus2(response.status)) {
          resolve(response);
        } else {
          reject(new AxiosError(
            "Request failed with status code " + response.status,
            [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
            response.config,
            response.request,
            response
          ));
        }
      }
      const cookies = platform.hasStandardBrowserEnv ? (
        // Standard browser envs support document.cookie
        {
          write(name, value, expires, path, domain, secure) {
            const cookie = [name + "=" + encodeURIComponent(value)];
            utils$1.isNumber(expires) && cookie.push("expires=" + new Date(expires).toGMTString());
            utils$1.isString(path) && cookie.push("path=" + path);
            utils$1.isString(domain) && cookie.push("domain=" + domain);
            secure === true && cookie.push("secure");
            document.cookie = cookie.join("; ");
          },
          read(name) {
            const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
            return match ? decodeURIComponent(match[3]) : null;
          },
          remove(name) {
            this.write(name, "", Date.now() - 864e5);
          }
        }
      ) : (
        // Non-standard browser env (web workers, react-native) lack needed support.
        {
          write() {
          },
          read() {
            return null;
          },
          remove() {
          }
        }
      );
      function isAbsoluteURL(url) {
        return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
      }
      function combineURLs(baseURL, relativeURL) {
        return relativeURL ? baseURL.replace(/\/?\/$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
      }
      function buildFullPath(baseURL, requestedURL) {
        if (baseURL && !isAbsoluteURL(requestedURL)) {
          return combineURLs(baseURL, requestedURL);
        }
        return requestedURL;
      }
      const isURLSameOrigin = platform.hasStandardBrowserEnv ? (
        // Standard browser envs have full support of the APIs needed to test
        // whether the request URL is of the same origin as current location.
        function standardBrowserEnv() {
          const msie = /(msie|trident)/i.test(navigator.userAgent);
          const urlParsingNode = document.createElement("a");
          let originURL;
          function resolveURL(url) {
            let href = url;
            if (msie) {
              urlParsingNode.setAttribute("href", href);
              href = urlParsingNode.href;
            }
            urlParsingNode.setAttribute("href", href);
            return {
              href: urlParsingNode.href,
              protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, "") : "",
              host: urlParsingNode.host,
              search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, "") : "",
              hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, "") : "",
              hostname: urlParsingNode.hostname,
              port: urlParsingNode.port,
              pathname: urlParsingNode.pathname.charAt(0) === "/" ? urlParsingNode.pathname : "/" + urlParsingNode.pathname
            };
          }
          originURL = resolveURL(window.location.href);
          return function isURLSameOrigin2(requestURL) {
            const parsed = utils$1.isString(requestURL) ? resolveURL(requestURL) : requestURL;
            return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
          };
        }()
      ) : (
        // Non standard browser envs (web workers, react-native) lack needed support.
        /* @__PURE__ */ function nonStandardBrowserEnv() {
          return function isURLSameOrigin2() {
            return true;
          };
        }()
      );
      function parseProtocol(url) {
        const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
        return match && match[1] || "";
      }
      function speedometer(samplesCount, min) {
        samplesCount = samplesCount || 10;
        const bytes = new Array(samplesCount);
        const timestamps = new Array(samplesCount);
        let head = 0;
        let tail = 0;
        let firstSampleTS;
        min = min !== void 0 ? min : 1e3;
        return function push(chunkLength) {
          const now = Date.now();
          const startedAt = timestamps[tail];
          if (!firstSampleTS) {
            firstSampleTS = now;
          }
          bytes[head] = chunkLength;
          timestamps[head] = now;
          let i = tail;
          let bytesCount = 0;
          while (i !== head) {
            bytesCount += bytes[i++];
            i = i % samplesCount;
          }
          head = (head + 1) % samplesCount;
          if (head === tail) {
            tail = (tail + 1) % samplesCount;
          }
          if (now - firstSampleTS < min) {
            return;
          }
          const passed = startedAt && now - startedAt;
          return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
        };
      }
      function progressEventReducer(listener, isDownloadStream) {
        let bytesNotified = 0;
        const _speedometer = speedometer(50, 250);
        return (e) => {
          const loaded = e.loaded;
          const total = e.lengthComputable ? e.total : void 0;
          const progressBytes = loaded - bytesNotified;
          const rate = _speedometer(progressBytes);
          const inRange = loaded <= total;
          bytesNotified = loaded;
          const data = {
            loaded,
            total,
            progress: total ? loaded / total : void 0,
            bytes: progressBytes,
            rate: rate ? rate : void 0,
            estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
            event: e
          };
          data[isDownloadStream ? "download" : "upload"] = true;
          listener(data);
        };
      }
      const isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
      const xhrAdapter = isXHRAdapterSupported && function(config) {
        return new Promise(function dispatchXhrRequest(resolve, reject) {
          let requestData = config.data;
          const requestHeaders = AxiosHeaders$1.from(config.headers).normalize();
          let { responseType, withXSRFToken } = config;
          let onCanceled;
          function done() {
            if (config.cancelToken) {
              config.cancelToken.unsubscribe(onCanceled);
            }
            if (config.signal) {
              config.signal.removeEventListener("abort", onCanceled);
            }
          }
          let contentType;
          if (utils$1.isFormData(requestData)) {
            if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {
              requestHeaders.setContentType(false);
            } else if ((contentType = requestHeaders.getContentType()) !== false) {
              const [type, ...tokens] = contentType ? contentType.split(";").map((token) => token.trim()).filter(Boolean) : [];
              requestHeaders.setContentType([type || "multipart/form-data", ...tokens].join("; "));
            }
          }
          let request = new XMLHttpRequest();
          if (config.auth) {
            const username = config.auth.username || "";
            const password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : "";
            requestHeaders.set("Authorization", "Basic " + btoa(username + ":" + password));
          }
          const fullPath = buildFullPath(config.baseURL, config.url);
          request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);
          request.timeout = config.timeout;
          function onloadend() {
            if (!request) {
              return;
            }
            const responseHeaders = AxiosHeaders$1.from(
              "getAllResponseHeaders" in request && request.getAllResponseHeaders()
            );
            const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
            const response = {
              data: responseData,
              status: request.status,
              statusText: request.statusText,
              headers: responseHeaders,
              config,
              request
            };
            settle(function _resolve(value) {
              resolve(value);
              done();
            }, function _reject(err) {
              reject(err);
              done();
            }, response);
            request = null;
          }
          if ("onloadend" in request) {
            request.onloadend = onloadend;
          } else {
            request.onreadystatechange = function handleLoad() {
              if (!request || request.readyState !== 4) {
                return;
              }
              if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
                return;
              }
              setTimeout(onloadend);
            };
          }
          request.onabort = function handleAbort() {
            if (!request) {
              return;
            }
            reject(new AxiosError("Request aborted", AxiosError.ECONNABORTED, config, request));
            request = null;
          };
          request.onerror = function handleError() {
            reject(new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request));
            request = null;
          };
          request.ontimeout = function handleTimeout() {
            let timeoutErrorMessage = config.timeout ? "timeout of " + config.timeout + "ms exceeded" : "timeout exceeded";
            const transitional2 = config.transitional || transitionalDefaults;
            if (config.timeoutErrorMessage) {
              timeoutErrorMessage = config.timeoutErrorMessage;
            }
            reject(new AxiosError(
              timeoutErrorMessage,
              transitional2.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,
              config,
              request
            ));
            request = null;
          };
          if (platform.hasStandardBrowserEnv) {
            withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(config));
            if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(fullPath)) {
              const xsrfValue = config.xsrfHeaderName && config.xsrfCookieName && cookies.read(config.xsrfCookieName);
              if (xsrfValue) {
                requestHeaders.set(config.xsrfHeaderName, xsrfValue);
              }
            }
          }
          requestData === void 0 && requestHeaders.setContentType(null);
          if ("setRequestHeader" in request) {
            utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
              request.setRequestHeader(key, val);
            });
          }
          if (!utils$1.isUndefined(config.withCredentials)) {
            request.withCredentials = !!config.withCredentials;
          }
          if (responseType && responseType !== "json") {
            request.responseType = config.responseType;
          }
          if (typeof config.onDownloadProgress === "function") {
            request.addEventListener("progress", progressEventReducer(config.onDownloadProgress, true));
          }
          if (typeof config.onUploadProgress === "function" && request.upload) {
            request.upload.addEventListener("progress", progressEventReducer(config.onUploadProgress));
          }
          if (config.cancelToken || config.signal) {
            onCanceled = (cancel) => {
              if (!request) {
                return;
              }
              reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);
              request.abort();
              request = null;
            };
            config.cancelToken && config.cancelToken.subscribe(onCanceled);
            if (config.signal) {
              config.signal.aborted ? onCanceled() : config.signal.addEventListener("abort", onCanceled);
            }
          }
          const protocol = parseProtocol(fullPath);
          if (protocol && platform.protocols.indexOf(protocol) === -1) {
            reject(new AxiosError("Unsupported protocol " + protocol + ":", AxiosError.ERR_BAD_REQUEST, config));
            return;
          }
          request.send(requestData || null);
        });
      };
      const knownAdapters = {
        http: httpAdapter,
        xhr: xhrAdapter
      };
      utils$1.forEach(knownAdapters, (fn, value) => {
        if (fn) {
          try {
            Object.defineProperty(fn, "name", { value });
          } catch (e) {
          }
          Object.defineProperty(fn, "adapterName", { value });
        }
      });
      const renderReason = (reason) => `- ${reason}`;
      const isResolvedHandle = (adapter) => utils$1.isFunction(adapter) || adapter === null || adapter === false;
      const adapters = {
        getAdapter: (adapters2) => {
          adapters2 = utils$1.isArray(adapters2) ? adapters2 : [adapters2];
          const { length } = adapters2;
          let nameOrAdapter;
          let adapter;
          const rejectedReasons = {};
          for (let i = 0; i < length; i++) {
            nameOrAdapter = adapters2[i];
            let id;
            adapter = nameOrAdapter;
            if (!isResolvedHandle(nameOrAdapter)) {
              adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
              if (adapter === void 0) {
                throw new AxiosError(`Unknown adapter '${id}'`);
              }
            }
            if (adapter) {
              break;
            }
            rejectedReasons[id || "#" + i] = adapter;
          }
          if (!adapter) {
            const reasons = Object.entries(rejectedReasons).map(
              ([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build")
            );
            let s = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
            throw new AxiosError(
              `There is no suitable adapter to dispatch the request ` + s,
              "ERR_NOT_SUPPORT"
            );
          }
          return adapter;
        },
        adapters: knownAdapters
      };
      function throwIfCancellationRequested(config) {
        if (config.cancelToken) {
          config.cancelToken.throwIfRequested();
        }
        if (config.signal && config.signal.aborted) {
          throw new CanceledError(null, config);
        }
      }
      function dispatchRequest(config) {
        throwIfCancellationRequested(config);
        config.headers = AxiosHeaders$1.from(config.headers);
        config.data = transformData.call(
          config,
          config.transformRequest
        );
        if (["post", "put", "patch"].indexOf(config.method) !== -1) {
          config.headers.setContentType("application/x-www-form-urlencoded", false);
        }
        const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);
        return adapter(config).then(function onAdapterResolution(response) {
          throwIfCancellationRequested(config);
          response.data = transformData.call(
            config,
            config.transformResponse,
            response
          );
          response.headers = AxiosHeaders$1.from(response.headers);
          return response;
        }, function onAdapterRejection(reason) {
          if (!isCancel(reason)) {
            throwIfCancellationRequested(config);
            if (reason && reason.response) {
              reason.response.data = transformData.call(
                config,
                config.transformResponse,
                reason.response
              );
              reason.response.headers = AxiosHeaders$1.from(reason.response.headers);
            }
          }
          return Promise.reject(reason);
        });
      }
      const headersToObject = (thing) => thing instanceof AxiosHeaders$1 ? { ...thing } : thing;
      function mergeConfig(config1, config2) {
        config2 = config2 || {};
        const config = {};
        function getMergedValue(target, source, caseless) {
          if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {
            return utils$1.merge.call({ caseless }, target, source);
          } else if (utils$1.isPlainObject(source)) {
            return utils$1.merge({}, source);
          } else if (utils$1.isArray(source)) {
            return source.slice();
          }
          return source;
        }
        function mergeDeepProperties(a, b, caseless) {
          if (!utils$1.isUndefined(b)) {
            return getMergedValue(a, b, caseless);
          } else if (!utils$1.isUndefined(a)) {
            return getMergedValue(void 0, a, caseless);
          }
        }
        function valueFromConfig2(a, b) {
          if (!utils$1.isUndefined(b)) {
            return getMergedValue(void 0, b);
          }
        }
        function defaultToConfig2(a, b) {
          if (!utils$1.isUndefined(b)) {
            return getMergedValue(void 0, b);
          } else if (!utils$1.isUndefined(a)) {
            return getMergedValue(void 0, a);
          }
        }
        function mergeDirectKeys(a, b, prop) {
          if (prop in config2) {
            return getMergedValue(a, b);
          } else if (prop in config1) {
            return getMergedValue(void 0, a);
          }
        }
        const mergeMap = {
          url: valueFromConfig2,
          method: valueFromConfig2,
          data: valueFromConfig2,
          baseURL: defaultToConfig2,
          transformRequest: defaultToConfig2,
          transformResponse: defaultToConfig2,
          paramsSerializer: defaultToConfig2,
          timeout: defaultToConfig2,
          timeoutMessage: defaultToConfig2,
          withCredentials: defaultToConfig2,
          withXSRFToken: defaultToConfig2,
          adapter: defaultToConfig2,
          responseType: defaultToConfig2,
          xsrfCookieName: defaultToConfig2,
          xsrfHeaderName: defaultToConfig2,
          onUploadProgress: defaultToConfig2,
          onDownloadProgress: defaultToConfig2,
          decompress: defaultToConfig2,
          maxContentLength: defaultToConfig2,
          maxBodyLength: defaultToConfig2,
          beforeRedirect: defaultToConfig2,
          transport: defaultToConfig2,
          httpAgent: defaultToConfig2,
          httpsAgent: defaultToConfig2,
          cancelToken: defaultToConfig2,
          socketPath: defaultToConfig2,
          responseEncoding: defaultToConfig2,
          validateStatus: mergeDirectKeys,
          headers: (a, b) => mergeDeepProperties(headersToObject(a), headersToObject(b), true)
        };
        utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
          const merge2 = mergeMap[prop] || mergeDeepProperties;
          const configValue = merge2(config1[prop], config2[prop], prop);
          utils$1.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
        });
        return config;
      }
      const VERSION = "1.6.8";
      const validators$1 = {};
      ["object", "boolean", "number", "function", "string", "symbol"].forEach((type, i) => {
        validators$1[type] = function validator2(thing) {
          return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
        };
      });
      const deprecatedWarnings = {};
      validators$1.transitional = function transitional(validator2, version, message) {
        function formatMessage(opt, desc) {
          return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
        }
        return (value, opt, opts) => {
          if (validator2 === false) {
            throw new AxiosError(
              formatMessage(opt, " has been removed" + (version ? " in " + version : "")),
              AxiosError.ERR_DEPRECATED
            );
          }
          if (version && !deprecatedWarnings[opt]) {
            deprecatedWarnings[opt] = true;
            console.warn(
              formatMessage(
                opt,
                " has been deprecated since v" + version + " and will be removed in the near future"
              )
            );
          }
          return validator2 ? validator2(value, opt, opts) : true;
        };
      };
      function assertOptions(options, schema, allowUnknown) {
        if (typeof options !== "object") {
          throw new AxiosError("options must be an object", AxiosError.ERR_BAD_OPTION_VALUE);
        }
        const keys = Object.keys(options);
        let i = keys.length;
        while (i-- > 0) {
          const opt = keys[i];
          const validator2 = schema[opt];
          if (validator2) {
            const value = options[opt];
            const result = value === void 0 || validator2(value, opt, options);
            if (result !== true) {
              throw new AxiosError("option " + opt + " must be " + result, AxiosError.ERR_BAD_OPTION_VALUE);
            }
            continue;
          }
          if (allowUnknown !== true) {
            throw new AxiosError("Unknown option " + opt, AxiosError.ERR_BAD_OPTION);
          }
        }
      }
      const validator = {
        assertOptions,
        validators: validators$1
      };
      const validators = validator.validators;
      class Axios {
        constructor(instanceConfig) {
          this.defaults = instanceConfig;
          this.interceptors = {
            request: new InterceptorManager(),
            response: new InterceptorManager()
          };
        }
        /**
         * Dispatch a request
         *
         * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
         * @param {?Object} config
         *
         * @returns {Promise} The Promise to be fulfilled
         */
        async request(configOrUrl, config) {
          try {
            return await this._request(configOrUrl, config);
          } catch (err) {
            if (err instanceof Error) {
              let dummy;
              Error.captureStackTrace ? Error.captureStackTrace(dummy = {}) : dummy = new Error();
              const stack = dummy.stack ? dummy.stack.replace(/^.+\n/, "") : "";
              if (!err.stack) {
                err.stack = stack;
              } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\n.+\n/, ""))) {
                err.stack += "\n" + stack;
              }
            }
            throw err;
          }
        }
        _request(configOrUrl, config) {
          if (typeof configOrUrl === "string") {
            config = config || {};
            config.url = configOrUrl;
          } else {
            config = configOrUrl || {};
          }
          config = mergeConfig(this.defaults, config);
          const { transitional: transitional2, paramsSerializer, headers } = config;
          if (transitional2 !== void 0) {
            validator.assertOptions(transitional2, {
              silentJSONParsing: validators.transitional(validators.boolean),
              forcedJSONParsing: validators.transitional(validators.boolean),
              clarifyTimeoutError: validators.transitional(validators.boolean)
            }, false);
          }
          if (paramsSerializer != null) {
            if (utils$1.isFunction(paramsSerializer)) {
              config.paramsSerializer = {
                serialize: paramsSerializer
              };
            } else {
              validator.assertOptions(paramsSerializer, {
                encode: validators.function,
                serialize: validators.function
              }, true);
            }
          }
          config.method = (config.method || this.defaults.method || "get").toLowerCase();
          let contextHeaders = headers && utils$1.merge(
            headers.common,
            headers[config.method]
          );
          headers && utils$1.forEach(
            ["delete", "get", "head", "post", "put", "patch", "common"],
            (method) => {
              delete headers[method];
            }
          );
          config.headers = AxiosHeaders$1.concat(contextHeaders, headers);
          const requestInterceptorChain = [];
          let synchronousRequestInterceptors = true;
          this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
            if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
              return;
            }
            synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
            requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
          });
          const responseInterceptorChain = [];
          this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
            responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
          });
          let promise;
          let i = 0;
          let len;
          if (!synchronousRequestInterceptors) {
            const chain = [dispatchRequest.bind(this), void 0];
            chain.unshift.apply(chain, requestInterceptorChain);
            chain.push.apply(chain, responseInterceptorChain);
            len = chain.length;
            promise = Promise.resolve(config);
            while (i < len) {
              promise = promise.then(chain[i++], chain[i++]);
            }
            return promise;
          }
          len = requestInterceptorChain.length;
          let newConfig = config;
          i = 0;
          while (i < len) {
            const onFulfilled = requestInterceptorChain[i++];
            const onRejected = requestInterceptorChain[i++];
            try {
              newConfig = onFulfilled(newConfig);
            } catch (error) {
              onRejected.call(this, error);
              break;
            }
          }
          try {
            promise = dispatchRequest.call(this, newConfig);
          } catch (error) {
            return Promise.reject(error);
          }
          i = 0;
          len = responseInterceptorChain.length;
          while (i < len) {
            promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);
          }
          return promise;
        }
        getUri(config) {
          config = mergeConfig(this.defaults, config);
          const fullPath = buildFullPath(config.baseURL, config.url);
          return buildURL(fullPath, config.params, config.paramsSerializer);
        }
      }
      utils$1.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
        Axios.prototype[method] = function(url, config) {
          return this.request(mergeConfig(config || {}, {
            method,
            url,
            data: (config || {}).data
          }));
        };
      });
      utils$1.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
        function generateHTTPMethod(isForm) {
          return function httpMethod(url, data, config) {
            return this.request(mergeConfig(config || {}, {
              method,
              headers: isForm ? {
                "Content-Type": "multipart/form-data"
              } : {},
              url,
              data
            }));
          };
        }
        Axios.prototype[method] = generateHTTPMethod();
        Axios.prototype[method + "Form"] = generateHTTPMethod(true);
      });
      const Axios$1 = Axios;
      class CancelToken {
        constructor(executor) {
          if (typeof executor !== "function") {
            throw new TypeError("executor must be a function.");
          }
          let resolvePromise;
          this.promise = new Promise(function promiseExecutor(resolve) {
            resolvePromise = resolve;
          });
          const token = this;
          this.promise.then((cancel) => {
            if (!token._listeners)
              return;
            let i = token._listeners.length;
            while (i-- > 0) {
              token._listeners[i](cancel);
            }
            token._listeners = null;
          });
          this.promise.then = (onfulfilled) => {
            let _resolve;
            const promise = new Promise((resolve) => {
              token.subscribe(resolve);
              _resolve = resolve;
            }).then(onfulfilled);
            promise.cancel = function reject() {
              token.unsubscribe(_resolve);
            };
            return promise;
          };
          executor(function cancel(message, config, request) {
            if (token.reason) {
              return;
            }
            token.reason = new CanceledError(message, config, request);
            resolvePromise(token.reason);
          });
        }
        /**
         * Throws a `CanceledError` if cancellation has been requested.
         */
        throwIfRequested() {
          if (this.reason) {
            throw this.reason;
          }
        }
        /**
         * Subscribe to the cancel signal
         */
        subscribe(listener) {
          if (this.reason) {
            listener(this.reason);
            return;
          }
          if (this._listeners) {
            this._listeners.push(listener);
          } else {
            this._listeners = [listener];
          }
        }
        /**
         * Unsubscribe from the cancel signal
         */
        unsubscribe(listener) {
          if (!this._listeners) {
            return;
          }
          const index = this._listeners.indexOf(listener);
          if (index !== -1) {
            this._listeners.splice(index, 1);
          }
        }
        /**
         * Returns an object that contains a new `CancelToken` and a function that, when called,
         * cancels the `CancelToken`.
         */
        static source() {
          let cancel;
          const token = new CancelToken(function executor(c) {
            cancel = c;
          });
          return {
            token,
            cancel
          };
        }
      }
      const CancelToken$1 = CancelToken;
      function spread(callback) {
        return function wrap(arr) {
          return callback.apply(null, arr);
        };
      }
      function isAxiosError(payload) {
        return utils$1.isObject(payload) && payload.isAxiosError === true;
      }
      const HttpStatusCode = {
        Continue: 100,
        SwitchingProtocols: 101,
        Processing: 102,
        EarlyHints: 103,
        Ok: 200,
        Created: 201,
        Accepted: 202,
        NonAuthoritativeInformation: 203,
        NoContent: 204,
        ResetContent: 205,
        PartialContent: 206,
        MultiStatus: 207,
        AlreadyReported: 208,
        ImUsed: 226,
        MultipleChoices: 300,
        MovedPermanently: 301,
        Found: 302,
        SeeOther: 303,
        NotModified: 304,
        UseProxy: 305,
        Unused: 306,
        TemporaryRedirect: 307,
        PermanentRedirect: 308,
        BadRequest: 400,
        Unauthorized: 401,
        PaymentRequired: 402,
        Forbidden: 403,
        NotFound: 404,
        MethodNotAllowed: 405,
        NotAcceptable: 406,
        ProxyAuthenticationRequired: 407,
        RequestTimeout: 408,
        Conflict: 409,
        Gone: 410,
        LengthRequired: 411,
        PreconditionFailed: 412,
        PayloadTooLarge: 413,
        UriTooLong: 414,
        UnsupportedMediaType: 415,
        RangeNotSatisfiable: 416,
        ExpectationFailed: 417,
        ImATeapot: 418,
        MisdirectedRequest: 421,
        UnprocessableEntity: 422,
        Locked: 423,
        FailedDependency: 424,
        TooEarly: 425,
        UpgradeRequired: 426,
        PreconditionRequired: 428,
        TooManyRequests: 429,
        RequestHeaderFieldsTooLarge: 431,
        UnavailableForLegalReasons: 451,
        InternalServerError: 500,
        NotImplemented: 501,
        BadGateway: 502,
        ServiceUnavailable: 503,
        GatewayTimeout: 504,
        HttpVersionNotSupported: 505,
        VariantAlsoNegotiates: 506,
        InsufficientStorage: 507,
        LoopDetected: 508,
        NotExtended: 510,
        NetworkAuthenticationRequired: 511
      };
      Object.entries(HttpStatusCode).forEach(([key, value]) => {
        HttpStatusCode[value] = key;
      });
      const HttpStatusCode$1 = HttpStatusCode;
      function createInstance(defaultConfig) {
        const context = new Axios$1(defaultConfig);
        const instance = bind(Axios$1.prototype.request, context);
        utils$1.extend(instance, Axios$1.prototype, context, { allOwnKeys: true });
        utils$1.extend(instance, context, null, { allOwnKeys: true });
        instance.create = function create(instanceConfig) {
          return createInstance(mergeConfig(defaultConfig, instanceConfig));
        };
        return instance;
      }
      const axios = exports("m", createInstance(defaults$1));
      axios.Axios = Axios$1;
      axios.CanceledError = CanceledError;
      axios.CancelToken = CancelToken$1;
      axios.isCancel = isCancel;
      axios.VERSION = VERSION;
      axios.toFormData = toFormData;
      axios.AxiosError = AxiosError;
      axios.Cancel = axios.CanceledError;
      axios.all = function all(promises) {
        return Promise.all(promises);
      };
      axios.spread = spread;
      axios.isAxiosError = isAxiosError;
      axios.mergeConfig = mergeConfig;
      axios.AxiosHeaders = AxiosHeaders$1;
      axios.formToJSON = (thing) => formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);
      axios.getAdapter = adapters.getAdapter;
      axios.HttpStatusCode = HttpStatusCode$1;
      axios.default = axios;
      logger.debug("初始化");
      async function main(router) {
        let module$1 = {
          run() {
            logger.info("BoosHelper加载成功");
            logger.warn("当前页面无对应hook脚本", router.path);
          }
        };
        switch (router.path) {
          case "/web/geek/job":
            module$1 = await __vitePreload(() => module.import('./index-Dz7efrfh-CSpQtLsm.js'), void 0 );
            break;
        }
        module$1.run();
        const helper = document.querySelector("#boos-helper");
        if (!helper) {
          const app = createApp(_sfc_main);
          const appEl = document.createElement("div");
          appEl.id = "boos-helper";
          document.body.append(appEl);
          app.mount(appEl);
        }
      }
      async function start() {
        document.documentElement.classList.toggle(
          "dark",
          _GM_getValue("theme-dark", false)
        );
        const v = await getRootVue();
        v.$router.afterHooks.push(main);
        main(v.$route);
        let axiosLoad;
        axios.interceptors.request.use(
          function(config) {
            if (config.timeout) {
              axiosLoad = loader({ ms: config.timeout, color: "#F79E63" });
            }
            return config;
          },
          function(error) {
            if (axiosLoad)
              axiosLoad();
            return Promise.reject(error);
          }
        );
        axios.interceptors.response.use(
          function(response) {
            if (axiosLoad)
              axiosLoad();
            return response;
          },
          function(error) {
            if (axiosLoad)
              axiosLoad();
            return Promise.reject(error);
          }
        );
      }
      logger.debug("开始运行");
      start();

    })
  };
}));

System.register("./index-Dz7efrfh-CSpQtLsm.js", ['./__monkey.entry-BeSQLw94.js', 'vue', 'element-plus', 'protobufjs'], (function (exports, module) {
  'use strict';
  var _GM_getValue, logger, elmGetter, formInfoData, toValue, noop$1, isObject$1, tryOnScopeDispose, useConfFormData, useStatistics, delay, notification, isClient, useHookVueData, deepmerge, useHookVueFn, axios, _unsafeWindow, _GM_setValue, useStore, ref, toRaw, markRaw, createApp, defineComponent, computed, onMounted, openBlock, createElementBlock, Fragment, createElementVNode, createTextVNode, unref, toDisplayString, createCommentVNode, normalizeStyle, createVNode, withCtx, withModifiers, createBlock, Teleport, watch, resolveDirective, mergeProps, withDirectives, renderList, mergeModels, useModel$1, renderSlot, h, watchEffect, ElTooltip, ElTabs, ElTabPane, ElCheckbox, ElLink, ElImage, ElSpace, ElButton, ElDialog, ElSelectV2, ElAlert, ElForm, ElInput, ElFormItem, ElSelect, ElOption, ElAutoResizer, ElTableV2, ElRow, ElCol, ElStatistic, ElDropdown, ElDropdownMenu, ElDropdownItem, ElIcon, ElButtonGroup, ElProgress, ElPopconfirm, ElText, ElTable, ElTableColumn, ElRadioGroup, ElRadioButton, ElSlider, ElMessage, ElTag, ElSwitch, protobuf;
  return {
    setters: [module => {
      _GM_getValue = module._;
      logger = module.l;
      elmGetter = module.e;
      formInfoData = module.f;
      toValue = module.t;
      noop$1 = module.n;
      isObject$1 = module.i;
      tryOnScopeDispose = module.a;
      useConfFormData = module.u;
      useStatistics = module.b;
      delay = module.d;
      notification = module.c;
      isClient = module.g;
      useHookVueData = module.h;
      deepmerge = module.j;
      useHookVueFn = module.k;
      axios = module.m;
      _unsafeWindow = module.o;
      _GM_setValue = module.p;
      useStore = module.q;
    }, module => {
      ref = module.ref;
      toRaw = module.toRaw;
      markRaw = module.markRaw;
      createApp = module.createApp;
      defineComponent = module.defineComponent;
      computed = module.computed;
      onMounted = module.onMounted;
      openBlock = module.openBlock;
      createElementBlock = module.createElementBlock;
      Fragment = module.Fragment;
      createElementVNode = module.createElementVNode;
      createTextVNode = module.createTextVNode;
      unref = module.unref;
      toDisplayString = module.toDisplayString;
      createCommentVNode = module.createCommentVNode;
      normalizeStyle = module.normalizeStyle;
      createVNode = module.createVNode;
      withCtx = module.withCtx;
      withModifiers = module.withModifiers;
      createBlock = module.createBlock;
      Teleport = module.Teleport;
      watch = module.watch;
      resolveDirective = module.resolveDirective;
      mergeProps = module.mergeProps;
      withDirectives = module.withDirectives;
      renderList = module.renderList;
      mergeModels = module.mergeModels;
      useModel$1 = module.useModel;
      renderSlot = module.renderSlot;
      h = module.h;
      watchEffect = module.watchEffect;
    }, module => {
      ElTooltip = module.ElTooltip;
      ElTabs = module.ElTabs;
      ElTabPane = module.ElTabPane;
      ElCheckbox = module.ElCheckbox;
      ElLink = module.ElLink;
      ElImage = module.ElImage;
      ElSpace = module.ElSpace;
      ElButton = module.ElButton;
      ElDialog = module.ElDialog;
      ElSelectV2 = module.ElSelectV2;
      ElAlert = module.ElAlert;
      ElForm = module.ElForm;
      ElInput = module.ElInput;
      ElFormItem = module.ElFormItem;
      ElSelect = module.ElSelect;
      ElOption = module.ElOption;
      ElAutoResizer = module.ElAutoResizer;
      ElTableV2 = module.ElTableV2;
      ElRow = module.ElRow;
      ElCol = module.ElCol;
      ElStatistic = module.ElStatistic;
      ElDropdown = module.ElDropdown;
      ElDropdownMenu = module.ElDropdownMenu;
      ElDropdownItem = module.ElDropdownItem;
      ElIcon = module.ElIcon;
      ElButtonGroup = module.ElButtonGroup;
      ElProgress = module.ElProgress;
      ElPopconfirm = module.ElPopconfirm;
      ElText = module.ElText;
      ElTable = module.ElTable;
      ElTableColumn = module.ElTableColumn;
      ElRadioGroup = module.ElRadioGroup;
      ElRadioButton = module.ElRadioButton;
      ElSlider = module.ElSlider;
      ElMessage = module.ElMessage;
      ElTag = module.ElTag;
      ElSwitch = module.ElSwitch;
    }, module => {
      protobuf = module.default;
    }],
    execute: (function () {

      exports("run", run);

      var __defProp = Object.defineProperty;
      var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
      var __publicField = (obj, key, value) => {
        __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
        return value;
      };
      function unrefElement(elRef) {
        var _a;
        const plain = toValue(elRef);
        return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;
      }
      const defaultWindow = isClient ? window : void 0;
      function useEventListener(...args) {
        let target;
        let events2;
        let listeners;
        let options;
        if (typeof args[0] === "string" || Array.isArray(args[0])) {
          [events2, listeners, options] = args;
          target = defaultWindow;
        } else {
          [target, events2, listeners, options] = args;
        }
        if (!target)
          return noop$1;
        if (!Array.isArray(events2))
          events2 = [events2];
        if (!Array.isArray(listeners))
          listeners = [listeners];
        const cleanups = [];
        const cleanup = () => {
          cleanups.forEach((fn) => fn());
          cleanups.length = 0;
        };
        const register = (el, event, listener, options2) => {
          el.addEventListener(event, listener, options2);
          return () => el.removeEventListener(event, listener, options2);
        };
        const stopWatch = watch(
          () => [unrefElement(target), toValue(options)],
          ([el, options2]) => {
            cleanup();
            if (!el)
              return;
            const optionsClone = isObject$1(options2) ? { ...options2 } : options2;
            cleanups.push(
              ...events2.flatMap((event) => {
                return listeners.map((listener) => register(el, event, listener, optionsClone));
              })
            );
          },
          { immediate: true, flush: "post" }
        );
        const stop = () => {
          stopWatch();
          cleanup();
        };
        tryOnScopeDispose(stop);
        return stop;
      }
      const UseMouseBuiltinExtractors = {
        page: (event) => [event.pageX, event.pageY],
        client: (event) => [event.clientX, event.clientY],
        screen: (event) => [event.screenX, event.screenY],
        movement: (event) => event instanceof Touch ? null : [event.movementX, event.movementY]
      };
      function useMouse(options = {}) {
        const {
          type = "page",
          touch = true,
          resetOnTouchEnds = false,
          initialValue = { x: 0, y: 0 },
          window: window2 = defaultWindow,
          target = window2,
          scroll = true,
          eventFilter
        } = options;
        let _prevMouseEvent = null;
        const x = ref(initialValue.x);
        const y = ref(initialValue.y);
        const sourceType = ref(null);
        const extractor = typeof type === "function" ? type : UseMouseBuiltinExtractors[type];
        const mouseHandler = (event) => {
          const result = extractor(event);
          _prevMouseEvent = event;
          if (result) {
            [x.value, y.value] = result;
            sourceType.value = "mouse";
          }
        };
        const touchHandler = (event) => {
          if (event.touches.length > 0) {
            const result = extractor(event.touches[0]);
            if (result) {
              [x.value, y.value] = result;
              sourceType.value = "touch";
            }
          }
        };
        const scrollHandler = () => {
          if (!_prevMouseEvent || !window2)
            return;
          const pos = extractor(_prevMouseEvent);
          if (_prevMouseEvent instanceof MouseEvent && pos) {
            x.value = pos[0] + window2.scrollX;
            y.value = pos[1] + window2.scrollY;
          }
        };
        const reset = () => {
          x.value = initialValue.x;
          y.value = initialValue.y;
        };
        const mouseHandlerWrapper = eventFilter ? (event) => eventFilter(() => mouseHandler(event), {}) : (event) => mouseHandler(event);
        const touchHandlerWrapper = eventFilter ? (event) => eventFilter(() => touchHandler(event), {}) : (event) => touchHandler(event);
        const scrollHandlerWrapper = eventFilter ? () => eventFilter(() => scrollHandler(), {}) : () => scrollHandler();
        if (target) {
          const listenerOptions = { passive: true };
          useEventListener(target, ["mousemove", "dragover"], mouseHandlerWrapper, listenerOptions);
          if (touch && type !== "movement") {
            useEventListener(target, ["touchstart", "touchmove"], touchHandlerWrapper, listenerOptions);
            if (resetOnTouchEnds)
              useEventListener(target, "touchend", reset, listenerOptions);
          }
          if (scroll && type === "page")
            useEventListener(window2, "scroll", scrollHandlerWrapper, { passive: true });
        }
        return {
          x,
          y,
          sourceType
        };
      }
      function useMouseInElement(target, options = {}) {
        const {
          handleOutside = true,
          window: window2 = defaultWindow
        } = options;
        const type = options.type || "page";
        const { x, y, sourceType } = useMouse(options);
        const targetRef = ref(target != null ? target : window2 == null ? void 0 : window2.document.body);
        const elementX = ref(0);
        const elementY = ref(0);
        const elementPositionX = ref(0);
        const elementPositionY = ref(0);
        const elementHeight = ref(0);
        const elementWidth = ref(0);
        const isOutside = ref(true);
        let stop = () => {
        };
        if (window2) {
          stop = watch(
            [targetRef, x, y],
            () => {
              const el = unrefElement(targetRef);
              if (!el)
                return;
              const {
                left,
                top,
                width,
                height
              } = el.getBoundingClientRect();
              elementPositionX.value = left + (type === "page" ? window2.pageXOffset : 0);
              elementPositionY.value = top + (type === "page" ? window2.pageYOffset : 0);
              elementHeight.value = height;
              elementWidth.value = width;
              const elX = x.value - elementPositionX.value;
              const elY = y.value - elementPositionY.value;
              isOutside.value = width === 0 || height === 0 || elX < 0 || elY < 0 || elX > width || elY > height;
              if (handleOutside || !isOutside.value) {
                elementX.value = elX;
                elementY.value = elY;
              }
            },
            { immediate: true }
          );
          useEventListener(document, "mouseleave", () => {
            isOutside.value = true;
          });
        }
        return {
          x,
          y,
          sourceType,
          elementX,
          elementY,
          elementPositionX,
          elementPositionY,
          elementHeight,
          elementWidth,
          isOutside,
          stop
        };
      }
      function useMap(initialValue) {
        const initialMap = initialValue ? new Map(initialValue) : /* @__PURE__ */ new Map();
        const state = ref(initialMap);
        const actions = {
          set: (key, value) => {
            state.value.set(key, value);
          },
          get: (key) => {
            return state.value.get(key);
          },
          remove: (key) => {
            state.value.delete(key);
          },
          has: (key) => state.value.has(key),
          clear: () => state.value.clear(),
          setAll: (newMap) => {
            state.value = new Map(newMap);
          },
          reset: () => state.value = initialMap
        };
        return {
          state,
          actions: markRaw(actions)
        };
      }
      const jobList = ref([]);
      const jobMap = useMap();
      const init = useHookVueData("#wrap .page-job-wrapper", "jobList", jobList);
      const useJobList = () => {
        return {
          jobList,
          jobMap,
          initJobList: init
        };
      };
      const columns = [
        {
          key: "title",
          title: "标题",
          dataKey: "title",
          width: 200
        },
        {
          key: "state",
          title: "状态",
          width: 150,
          align: "center",
          cellRenderer: ({ rowData }) => h(ElTag, { type: rowData.state ?? "primary" }, () => rowData.state_name)
        },
        {
          key: "message",
          title: "信息",
          dataKey: "message",
          width: 360,
          minWidth: 360,
          align: "left"
        }
      ];
      const dataOld = ref([]);
      const data = ref([
        {
          title: "嘿嘿嘿",
          state: "info",
          state_name: "消息",
          message: "目前没有投递日志啦"
        },
        {
          title: "啦啦啦",
          state: "success",
          state_name: "消息",
          message: "要查看其他日志请点击右上角的悬浮按钮"
        }
      ]);
      const useLog = () => {
        const add = (title, err, logdata, msg) => {
          const state = !err ? "success" : err.state;
          const message = msg ?? (err ? err.message : void 0);
          data.value.push({
            title,
            state,
            state_name: (err == null ? void 0 : err.name) ?? "投递成功",
            message,
            data: logdata
          });
        };
        const info = (title, message) => {
          data.value.push({
            title,
            state: "info",
            state_name: "消息",
            message,
            data: void 0
          });
        };
        const clear = () => {
          dataOld.value = [];
          data.value = [];
        };
        const reset = () => {
          dataOld.value = data.value;
          data.value = [];
        };
        const Row = ({ cells, rowData }) => {
          return cells;
        };
        Row.inheritAttrs = false;
        return {
          columns,
          data,
          dataOld,
          clear,
          reset,
          add,
          info,
          Row
        };
      };
      const deliverLock = ref(false);
      const deliverStop$1 = ref(false);
      const useCommon = () => {
        return {
          deliverLock,
          deliverStop: deliverStop$1
        };
      };
      const errMap = /* @__PURE__ */ new Map();
      function createCustomError(name, state = "warning") {
        var _a;
        errMap.set(name, true);
        return _a = class extends Error {
          constructor(message) {
            super(message);
            __publicField(this, "state");
            this.name = name;
            this.state = state;
            Object.setPrototypeOf(this, _a.prototype);
          }
        }, __publicField(_a, "message"), _a;
      }
      createCustomError("重复沟通");
      const JobTitleError = createCustomError("岗位名筛选");
      const CompanyNameError = createCustomError("公司名筛选");
      const SalaryError = createCustomError("薪资筛选");
      const CompanySizeError = createCustomError("公司规模筛选");
      const JobDescriptionError = createCustomError("工作内容筛选");
      const AIFilteringError = createCustomError("AI筛选");
      const ActivityError = createCustomError("活跃度过滤");
      const UnknownError = createCustomError("未知错误", "danger");
      const PublishError = createCustomError("投递出错", "danger");
      const GreetError = createCustomError("打招呼出错", "danger");
      function requestCard(params) {
        return axios.get("https://www.zhipin.com/wapi/zpgeek/job/card.json", {
          params,
          timeout: 5e3
        });
      }
      async function sendPublishReq(data2, errorMsg, retries = 3) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
        if (retries === 0) {
          throw new PublishError(errorMsg || "重试多次失败");
        }
        const url = "https://www.zhipin.com/wapi/zpgeek/friend/add.json";
        let params;
        params = {
          securityId: data2.securityId,
          jobId: data2.encryptJobId,
          lid: data2.lid
        };
        const token = (_b = (_a = _unsafeWindow) == null ? void 0 : _a._PAGE) == null ? void 0 : _b.zp_token;
        if (!token) {
          ElMessage.error("没有获取到token,请刷新重试");
          throw new PublishError("没有获取到token");
        }
        try {
          const res = await axios({
            url,
            params,
            method: "POST",
            headers: { Zp_token: token }
          });
          if (res.data.code === 1 && ((_f = (_e = (_d = (_c = res.data) == null ? void 0 : _c.zpData) == null ? void 0 : _d.bizData) == null ? void 0 : _e.chatRemindDialog) == null ? void 0 : _f.content)) {
            throw new PublishError(
              (_j = (_i = (_h = (_g = res.data) == null ? void 0 : _g.zpData) == null ? void 0 : _h.bizData) == null ? void 0 : _i.chatRemindDialog) == null ? void 0 : _j.content
            );
          }
          if (res.data.code !== 0) {
            throw new PublishError("状态错误:" + res.data.message);
          }
          return res.data;
        } catch (e) {
          if (e instanceof PublishError) {
            throw e;
          }
          return sendPublishReq(data2, e.message, retries - 1);
        }
      }
      async function requestBossData(card, errorMsg, retries = 3) {
        var _a, _b;
        if (retries === 0) {
          throw new GreetError(errorMsg || "重试多次失败");
        }
        const url = "https://www.zhipin.com/wapi/zpchat/geek/getBossData";
        const token = (_b = (_a = _unsafeWindow) == null ? void 0 : _a._PAGE) == null ? void 0 : _b.zp_token;
        if (!token) {
          ElMessage.error("没有获取到token,请刷新重试");
          throw new GreetError("没有获取到token");
        }
        try {
          const data2 = new FormData();
          data2.append("bossId", card.encryptUserId);
          data2.append("securityId", card.securityId);
          data2.append("bossSrc", "0");
          const res = await axios({
            url,
            data: data2,
            method: "POST",
            headers: { Zp_token: token }
          });
          if (res.data.code !== 0 && res.data.message !== "非好友关系") {
            throw new GreetError("状态错误:" + res.data.message);
          }
          if (res.data.code !== 0)
            return requestBossData(card, "非好友关系", retries - 1);
          return res.data.zpData;
        } catch (e) {
          if (e instanceof GreetError) {
            throw e;
          }
          return requestBossData(card, e.message, retries - 1);
        }
      }
      var Root = protobuf.Root, Type = protobuf.Type, Field = protobuf.Field;
      const root = new Root().define("cn.techwolf.boss.chat").add(
        new Type("TechwolfUser").add(new Field("uid", 1, "int64")).add(new Field("name", 2, "string", "optional")).add(new Field("source", 7, "int32", "optional"))
      ).add(
        new Type("TechwolfMessageBody").add(new Field("type", 1, "int32")).add(new Field("templateId", 2, "int32", "optional")).add(new Field("headTitle", 11, "string")).add(new Field("text", 3, "string"))
      ).add(
        new Type("TechwolfMessage").add(new Field("from", 1, "TechwolfUser")).add(new Field("to", 2, "TechwolfUser")).add(new Field("type", 3, "int32")).add(new Field("mid", 4, "int64", "optional")).add(new Field("time", 5, "int64", "optional")).add(new Field("body", 6, "TechwolfMessageBody")).add(new Field("cmid", 11, "int64", "optional"))
      ).add(
        new Type("TechwolfChatProtocol").add(new Field("type", 1, "int32")).add(new Field("messages", 3, "TechwolfMessage", "repeated"))
      );
      const AwesomeMessage = root.lookupType("TechwolfChatProtocol");
      class Message {
        constructor({
          form_uid,
          to_uid,
          to_name,
          content
        }) {
          __publicField(this, "msg");
          __publicField(this, "hex");
          const r = (/* @__PURE__ */ new Date()).getTime();
          const d = r + 68256432452609;
          const data2 = {
            messages: [
              {
                from: {
                  uid: form_uid,
                  source: 0
                },
                to: {
                  uid: to_uid,
                  name: to_name,
                  source: 0
                },
                type: 1,
                mid: d.toString(),
                time: r.toString(),
                body: {
                  type: 1,
                  templateId: 1,
                  text: content
                },
                cmid: d.toString()
              }
            ],
            type: 1
          };
          this.msg = AwesomeMessage.encode(data2).finish().slice();
          this.hex = [...this.msg].map((b) => b.toString(16).padStart(2, "0")).join("");
        }
        toArrayBuffer() {
          return this.msg.buffer.slice(0, this.msg.byteLength);
        }
        send() {
          _unsafeWindow.ChatWebsocket.send(this);
        }
      }
      var mitem$1 = { exports: {} };
      (function(module) {
        (function() {
          const miTem2 = {
            name: "miTem",
            version: "1.0.8"
          };
          const templateSettings = {
            statement: /\{%([\s\S]+?)%\}/g,
            expression: /\{\{([\s\S]+?)\}\}/g,
            filter_param: /([\s\S]+?)(\(([^)]*)\))$/
          };
          miTem2.partials = {};
          miTem2.registerPartial = (name, partial) => {
            miTem2.partials[name] = typeof partial === "string" ? miTem2.compile(partial) : partial;
          };
          const statements = {
            partial: (...args) => `o+=m.partials['${args[1]}'].apply(null, [${typeof args[2] !== "undefined" ? `c.${args[2]}` : "c"}]);`,
            if: (...args) => `if(c.${args[1]}){`,
            else: (...args) => `}else ${args[1] === "if" ? statements.if("", args[2]) : "{"}`,
            endif: () => "}",
            endfor: () => "}c=c.loop.parent;",
            for: (...args) => {
              const code = `if (typeof c.${args[3]}=== 'undefined') return '';
      var t={loop:{parent:c,length:c.${args[3]}.length}};c=t;var i=0;
      if(typeof c.loop.parent.${args[3]}.length === 'undefined')
      {c.loop.length=m.objSize(c.loop.parent.${args[3]})}
      for(${args[1]} in c.loop.parent.${args[3]}){
      if (!c.loop.parent.${args[3]}.hasOwnProperty(${args[1]}))continue;
      c.${args[1]}=c.loop.parent.${args[3]}[${args[1]}];
      c.loop.last=(i===c.loop.length-1);
      c.loop.first=(i===0);
      c.loop.key=${args[1]};
      c.loop.index0=i; c.loop.index=i+1;i++;`;
              return code;
            }
          };
          miTem2.variable = function(val) {
            this.val = val;
          };
          miTem2.variable.prototype.applyFilter = function(filterName, filterParameters) {
            let ret;
            if (typeof miTem2.filters[filterName] !== "undefined") {
              ret = miTem2.filters[filterName].apply(this.val, filterParameters);
            } else if (typeof this.val[filterName] === "undefined") {
              throw new Error(`Filter ${filterName} is not defined`);
            } else {
              ret = this.val[filterName].apply(this.val, filterParameters);
            }
            this.val = ret;
            return this;
          };
          miTem2.variable.prototype.toString = function() {
            return this.val;
          };
          miTem2.objSize = (obj) => {
            const keys = Object.keys(obj);
            return keys.length;
          };
          miTem2.retoreDefaultSettings = function() {
            miTem2.settings = {
              stopOnError: false
            };
          };
          miTem2.retoreDefaultSettings();
          miTem2.filters = {
            default(value) {
              return typeof this === "undefined" ? value : this;
            },
            abs() {
              return Math.abs(this);
            },
            capitalize() {
              return this.charAt(0).toUpperCase() + this.slice(1);
            },
            nl2br() {
              return this.replace(/\n/gi, "<br />");
            },
            title() {
              return this.split(" ").map((val) => val.charAt(0).toUpperCase() + val.slice(1).toLowerCase()).join(" ");
            }
          };
          if (module.exports) {
            module.exports = miTem2;
          } else {
            window.miTem = miTem2;
          }
          miTem2.processFilters = (expression) => {
            const lexemes = expression.trim().split("|");
            let variable = `(new m.variable(c.${lexemes[0]}))`;
            const filters = lexemes.slice(1);
            let filterRegexLexemes;
            filters.forEach((filter) => {
              filterRegexLexemes = templateSettings.filter_param.exec(filter.trim()) || ["", filter.trim(), "", ""];
              const parameters = filterRegexLexemes[3].split(",");
              variable += `.applyFilter('${filterRegexLexemes[1]}', [${parameters.join(",")}])`;
            });
            variable += ".toString()";
            return variable;
          };
          miTem2.compile = (tmpl) => {
            let returnFunctionStr = "var c=d;var m=this.miTem;var o='";
            const strings = tmpl.split("\n");
            let newLine = "";
            let compiled = true;
            let lineNumber;
            let lineStr;
            const statementReplaceFn = function(...args) {
              const lexemes = args[1].trim().split(" ");
              let retStr = "';";
              if (typeof statements[lexemes[0]] === "undefined") {
                console.error(`Line: ${lineNumber}; Error in ${args[0]}; Unknown tag '${lexemes[0]}'`);
                compiled = false;
              } else
                retStr += statements[lexemes[0]].apply(null, lexemes);
              retStr += "o+='";
              return retStr;
            };
            const expressionReplaceFn = function(...args) {
              const key = args[1];
              let calculatedValue = miTem2.processFilters(key.replace(/\\'/gi, "'"));
              calculatedValue = `(function(){var s=this,t;s.m=m;try{return ${calculatedValue}}catch(e){console.error('Line: ${parseInt(lineNumber, 10) + 1}; Error in ${args[0]}');`;
              if (miTem2.settings.stopOnError)
                calculatedValue += "throw e;";
              calculatedValue += "}})()";
              return `'+${calculatedValue}+'`;
            };
            strings.forEach((line, i) => {
              lineNumber = i;
              lineStr = line;
              returnFunctionStr += newLine;
              const currentLine = lineStr.replace(/'/gi, "\\'");
              returnFunctionStr += currentLine.replace(templateSettings.statement, statementReplaceFn).replace(templateSettings.expression, expressionReplaceFn);
              newLine = `'+"\\n"+'`;
            });
            returnFunctionStr += "'; return o;";
            if (compiled) {
              return (data2) => {
                let returnFunction;
                try {
                  returnFunction = new Function("d", returnFunctionStr);
                } catch (e) {
                  console.error(returnFunctionStr);
                  console.error(e);
                }
                const scope = {};
                scope.miTem = miTem2;
                return returnFunction.apply(scope, [data2]);
              };
            }
            return () => "";
          };
        })();
      })(mitem$1);
      var mitemExports = mitem$1.exports;
      const miTem = mitemExports;
      var mitem = {
        miTem
      };
      const confModelKey = "conf-model";
      const modelData$1 = ref(_GM_getValue(confModelKey, []));
      logger.debug("ai模型数据", toRaw(modelData$1.value));
      async function requestGpt$1(model, message, { timeout = 12e4, json = false }) {
        var _a, _b, _c;
        let ans;
        for (const m of model) {
          try {
            switch (m.data.mode) {
              case "ChatGPT": {
                const res = await axios.post(
                  m.data.url + "/v1/chat/completions",
                  {
                    messages: [
                      {
                        content: message,
                        role: "user"
                      }
                    ],
                    model: m.data.model,
                    temperature: m.data.temperature || 0.35,
                    // TODO: 部分模型不支持json格式，需要判断
                    response_format: false ? { type: "json_object" } : void 0
                  },
                  {
                    headers: {
                      Authorization: `Bearer ${m.data.apiKey}`,
                      "Content-Type": "application/json"
                    },
                    timeout
                  }
                );
                ans = (_c = (_b = (_a = res.data) == null ? void 0 : _a.choices[0]) == null ? void 0 : _b.message) == null ? void 0 : _c.content;
                break;
              }
              case "自定义":
              case "仅记录": {
                const template = mitem.miTem.compile(m.data.data);
                const msg = template({
                  message: JSON.stringify(message).replace(/^(\s|")+|(\s|")+$/g, ""),
                  raw: JSON.stringify(message)
                });
                const req = await axios.post(m.data.url, JSON.parse(msg), {
                  headers: m.data.header ? JSON.parse(m.data.header) : void 0,
                  timeout
                });
                if (m.data.mode === "自定义") {
                  const reqTemplate = mitem.miTem.compile(`{{${m.data.req}}}`);
                  return reqTemplate(req);
                }
                break;
              }
            }
          } catch (e) {
            logger.error("GPT请求错误", e);
          }
        }
        return ans;
      }
      function save() {
        _GM_setValue(confModelKey, toRaw(modelData$1.value));
        ElMessage.success("保存成功");
      }
      const useModel = () => {
        return {
          modelData: modelData$1,
          save,
          requestGpt: requestGpt$1
        };
      };
      const { modelData, requestGpt } = useModel();
      const { formData: formData$1 } = useConfFormData();
      const { todayData: todayData$1 } = useStatistics();
      const { userInfo } = useStore();
      const jobTitle = (h2) => h2.push(async ({ data: data2 }, ctx) => {
        try {
          const text = data2.jobName;
          if (!text)
            throw new JobTitleError("岗位名为空");
          for (const x of formData$1.jobTitle.value) {
            if (text.includes(x)) {
              if (formData$1.jobTitle.include) {
                return;
              }
              throw new JobTitleError(`岗位名含有排除关键词 [${x}]`);
            }
          }
          if (formData$1.jobTitle.include) {
            throw new JobTitleError("岗位名不包含关键词");
          }
        } catch (e) {
          todayData$1.jobTitle++;
          throw new JobTitleError(e.message);
        }
      });
      const company = (h2) => h2.push(async ({ data: data2 }, ctx) => {
        try {
          const text = data2.brandName;
          if (!text)
            throw new CompanyNameError("公司名为空");
          for (const x of formData$1.company.value) {
            if (text.includes(x)) {
              if (formData$1.company.include) {
                return;
              }
              throw new CompanyNameError(`公司名含有排除关键词 [${x}]`);
            }
          }
          if (formData$1.company.include) {
            throw new CompanyNameError("公司名不包含关键词");
          }
        } catch (e) {
          todayData$1.company++;
          throw new CompanyNameError(e.message);
        }
      });
      const salaryRange = (h2) => h2.push(async ({ data: data2 }, ctx) => {
        try {
          const text = data2.salaryDesc;
          const [v, err] = rangeMatch(text, formData$1.salaryRange.value);
          if (!v)
            throw new SalaryError(
              `不匹配的薪资范围 [${err}],预期: ${formData$1.salaryRange.value}`
            );
        } catch (e) {
          todayData$1.salaryRange++;
          throw new SalaryError(e.message);
        }
      });
      const companySizeRange = (h2) => h2.push(async ({ data: data2 }, ctx) => {
        try {
          const text = data2.brandScaleName;
          const [v, err] = rangeMatch(text, formData$1.companySizeRange.value);
          if (!v)
            throw new CompanySizeError(
              `不匹配的公司规模 [${err}], 预期: ${formData$1.companySizeRange.value}`
            );
        } catch (e) {
          todayData$1.companySizeRange++;
          throw new CompanySizeError(e.message);
        }
      });
      const jobContent = (h2) => h2.push(async ({}, { card }) => {
        try {
          const content = card == null ? void 0 : card.postDescription;
          for (const x of formData$1.jobContent.value) {
            if (!x) {
              continue;
            }
            let re = new RegExp(
              "(?<!(不|无).{0,5})" + x + "(?!系统|软件|工具|服务)"
            );
            if (content && re.test(content)) {
              if (formData$1.jobContent.include) {
                return;
              }
              throw new JobDescriptionError(`工作内容含有排除关键词 [${x}]`);
            }
          }
          if (formData$1.jobContent.include) {
            throw new JobDescriptionError("工作内容中不包含关键词");
          }
        } catch (e) {
          todayData$1.jobContent++;
          throw new JobDescriptionError(e.message);
        }
      });
      const aiFiltering = (h2) => {
        const template = mitem.miTem.compile(`我现在需要求职，让你根据我的需要对岗位进行评分，方便我筛选岗位。
我的要求是:
${formData$1.aiFiltering.word}
>>>下面是岗位相关信息:
岗位描述:{{ card.postDescription}}
薪酬:{{card.salaryDesc}}
经验要求:{{card.experienceName}},学历要求:{{card.degreeName}}
相关标签:{{card.jobLabels}},公司福利：{{data.welfareList}}
>>>>>>>>>>我需要你输出Json格式的字符串，符合以下的定义
interface aiFiltering {
  rating: number; // 分数，0-100分，低于60的我会筛选掉
  negative: string[] | string; // 扣分项，可以是一句话为什么扣分，也可以是数组代表多个扣分项
  positive: string[] | string; // 加分项，可以是一句话为什么加分，也可以是数组代表多个加分项
}`);
        let m = formData$1.aiFiltering.model || [];
        m = Array.isArray(m) ? m : [m];
        const model = modelData.value.filter((v) => m.includes(v.key));
        h2.push(async ({}, ctx) => {
          try {
            const msg = template({ data: ctx, card: ctx.card });
            if (!model || model.length === 0) {
              ElMessage.warning("没有找到AI筛选的模型");
              return;
            }
            ctx.aiFilteringQ = msg;
            const gptMsg = await requestGpt(model, msg, { json: true });
            if (!gptMsg) {
              return;
            }
            ctx.aiFilteringAraw = gptMsg;
            const data2 = JSON.parse(gptMsg);
            ctx.aiFilteringAjson = data2;
            const mg = `分数${data2.rating}
消极：${data2.negative}
积极：${data2.positive}`;
            ctx.aiFilteringAtext = msg;
            if (data2.rating < 60) {
              throw new AIFilteringError(mg);
            }
          } catch (e) {
            todayData$1.jobContent++;
            throw new AIFilteringError(e.message);
          }
        });
      };
      const activityFilter = (h2) => h2.push(async ({}, { card }) => {
        try {
          const activeText = card == null ? void 0 : card.activeTimeDesc;
          if (!activeText || activeText.includes("月") || activeText.includes("年"))
            throw new ActivityError(`不活跃,当前活跃度 [${activeText}]`);
        } catch (e) {
          todayData$1.activityFilter++;
          throw new ActivityError(e.message);
        }
      });
      const customGreeting = (h2) => {
        var _a, _b, _c, _d, _e;
        const template = mitem.miTem.compile(formData$1.customGreeting.value);
        const uid = ((_a = userInfo.value) == null ? void 0 : _a.userId) || ((_c = (_b = _unsafeWindow) == null ? void 0 : _b._PAGE) == null ? void 0 : _c.uid) || ((_e = (_d = _unsafeWindow) == null ? void 0 : _d._PAGE) == null ? void 0 : _e.userId);
        if (!uid) {
          ElMessage.error("没有获取到uid,请刷新重试");
          throw new GreetError("没有获取到uid");
        }
        h2.push(async (args, ctx) => {
          try {
            const boosData = await requestBossData(ctx.card);
            let msg = formData$1.customGreeting.value;
            if (formData$1.greetingVariable.value && ctx.card) {
              msg = template({ card: ctx.card });
            }
            ctx.message = msg;
            const buf = new Message({
              form_uid: uid.toString(),
              to_uid: boosData.data.bossId.toString(),
              to_name: boosData.data.encryptBossId,
              // encryptUserId
              content: msg
            });
            buf.send();
          } catch (e) {
            throw new GreetError(e == null ? void 0 : e.message);
          }
        });
      };
      const aiGreeting = (h2) => {
        var _a, _b, _c, _d, _e;
        const template = mitem.miTem.compile(formData$1.aiGreeting.word);
        let m = formData$1.aiGreeting.model || [];
        m = Array.isArray(m) ? m : [m];
        const model = modelData.value.filter((v) => m.includes(v.key));
        const uid = ((_a = userInfo.value) == null ? void 0 : _a.userId) || ((_c = (_b = _unsafeWindow) == null ? void 0 : _b._PAGE) == null ? void 0 : _c.uid) || ((_e = (_d = _unsafeWindow) == null ? void 0 : _d._PAGE) == null ? void 0 : _e.userId);
        if (!uid) {
          ElMessage.error("没有获取到uid,请刷新重试");
          throw new GreetError("没有获取到uid");
        }
        h2.push(async (args, ctx) => {
          try {
            const boosData = await requestBossData(ctx.card);
            const msg = template({ data: ctx, card: ctx.card });
            if (!model || model.length === 0) {
              ElMessage.warning("没有找到招呼语的模型");
              return;
            }
            ctx.aiGreetingQ = msg;
            const gptMsg = await requestGpt(model, msg, {});
            if (!gptMsg) {
              return;
            }
            ctx.message = gptMsg;
            ctx.aiGreetingA = gptMsg;
            const buf = new Message({
              form_uid: uid.toString(),
              to_uid: boosData.data.bossId.toString(),
              to_name: boosData.data.encryptBossId,
              // encryptUserId
              content: gptMsg
              // !!! 重大失误
            });
            buf.send();
          } catch (e) {
            throw new GreetError(e == null ? void 0 : e.message);
          }
        });
      };
      const record = async (ctx) => {
        let m = formData$1.record.model || [];
        m = Array.isArray(m) ? m : [m];
        const model = modelData.value.filter((v) => m.includes(v.key));
        await requestGpt(model, ctx, {});
      };
      const { formData } = useConfFormData();
      function createHandle() {
        const handles = [];
        const handlesRes = [];
        const handlesAfter = [];
        if (formData.jobTitle.enable)
          jobTitle(handles);
        if (formData.company.enable)
          company(handles);
        if (formData.salaryRange.enable)
          salaryRange(handles);
        if (formData.companySizeRange.enable)
          companySizeRange(handles);
        if (formData.jobContent.enable)
          jobContent(handlesRes);
        if (formData.aiFiltering.enable)
          aiFiltering(handlesRes);
        if (formData.activityFilter.value)
          activityFilter(handlesRes);
        if (formData.customGreeting.enable && !formData.aiGreeting.enable)
          customGreeting(handlesAfter);
        if (formData.aiGreeting.enable)
          aiGreeting(handlesAfter);
        return {
          before: async (args, ctx) => {
            try {
              await Promise.all(handles.map((handle) => handle(args, ctx)));
              if (handlesRes.length > 0) {
                const res = await requestCard({
                  lid: args.data.lid,
                  securityId: args.data.securityId
                });
                if (res.data.code == 0) {
                  ctx.card = res.data.zpData.jobCard;
                  await Promise.all(handlesRes.map((handle) => handle(args, ctx)));
                } else {
                  throw new UnknownError("请求响应错误:" + res.data.message);
                }
              }
            } catch (e) {
              if (errMap.has(e.name)) {
                throw e;
              }
              throw new UnknownError("预期外:" + e.message);
            }
          },
          after: async (args, ctx) => {
            if (handlesAfter.length === 0)
              return;
            try {
              if (!ctx.card) {
                const res = await requestCard({
                  lid: args.data.lid,
                  securityId: args.data.securityId
                });
                if (res.data.code == 0) {
                  ctx.card = res.data.zpData.jobCard;
                } else {
                  throw new UnknownError("请求响应错误:" + res.data.message);
                }
              }
              await Promise.all(handlesAfter.map((handle) => handle(args, ctx)));
            } catch (e) {
              if (errMap.has(e.name)) {
                throw e;
              }
              throw new UnknownError("预期外:" + e.message);
            }
          },
          record: (ctx) => {
            if (formData.record.enable)
              return record(ctx);
            return Promise.resolve();
          }
        };
      }
      const { todayData } = useStatistics();
      const { deliverStop } = useCommon();
      const total = ref(0);
      const current = ref(0);
      const log = useLog();
      async function jobListHandle(jobList2, jobMap2) {
        var _a;
        log.info("获取岗位", `本次获取到 ${jobList2.length} 个`);
        total.value = jobList2.length;
        const h2 = createHandle();
        jobList2.forEach((v) => {
          if (!jobMap2.has(v.encryptJobId))
            jobMap2.set(v.encryptJobId, {
              state: "wait",
              msg: "等待中"
            });
        });
        for (const [index, data2] of jobList2.entries()) {
          current.value = index;
          if (deliverStop.value) {
            log.info("暂停投递", `剩余 ${jobList2.length - index} 个未处理`);
            return;
          }
          if (((_a = jobMap2.get(data2.encryptJobId)) == null ? void 0 : _a.state) !== "wait")
            continue;
          try {
            jobMap2.set(data2.encryptJobId, {
              state: "running",
              msg: "处理中"
            });
            const ctx = JSON.parse(JSON.stringify(data2));
            try {
              await h2.before({ data: data2 }, ctx);
              await sendPublishReq(data2);
              await h2.after({ data: data2 }, ctx);
              log.add(data2.jobName, null, ctx, ctx.message);
              todayData.success++;
              jobMap2.set(data2.encryptJobId, {
                state: "success",
                msg: "投递成功"
              });
              logger.warn("成功", ctx);
              ctx.state = "成功";
            } catch (e) {
              jobMap2.set(data2.encryptJobId, {
                state: e.state === "warning" ? "warn" : "error",
                msg: e.name || "没有消息"
              });
              log.add(data2.jobName, e, ctx);
              logger.warn("过滤", ctx);
              ctx.state = "过滤";
              ctx.err = e.message || "";
            } finally {
              await h2.record(ctx);
            }
          } catch (e) {
            jobMap2.set(data2.encryptJobId, {
              state: "error",
              msg: "未知报错"
            });
            logger.error("未知报错", e, data2);
          } finally {
            todayData.total++;
            await delay(2e3);
          }
        }
      }
      const useDeliver = () => {
        return {
          createHandle,
          jobListHandle,
          total,
          current
        };
      };
      const _hoisted_1$7 = { style: { "order": "-1" } };
      const _hoisted_2$4 = { class: "card-tag" };
      const _hoisted_3$4 = { class: "card-title" };
      const _hoisted_4$1 = { class: "card-salary" };
      const _hoisted_5 = { class: "card-footer" };
      const _hoisted_6 = { class: "author-row" };
      const _hoisted_7 = ["src"];
      const _hoisted_8 = { class: "company-name" };
      const _hoisted_9 = { class: "card-status" };
      const _sfc_main$d = /* @__PURE__ */ defineComponent({
        __name: "card",
        setup(__props) {
          const {
            jobList: jobList2,
            jobMap: { actions: jobMap2 }
          } = useJobList();
          const { current: current2 } = useDeliver();
          const jobListRef = ref();
          const autoScroll = ref(true);
          const cards = ref();
          function scroll(e) {
            e.preventDefault();
            if (!cards.value) {
              return;
            }
            let left = -e.wheelDelta || e.deltaY / 2;
            cards.value.scrollLeft = cards.value.scrollLeft + left;
            autoScroll.value = false;
          }
          watchEffect(() => {
            const d = jobListRef.value;
            if (autoScroll.value && d && d.length > current2.value) {
              d[current2.value].scrollIntoView({
                behavior: "smooth",
                block: "nearest",
                inline: "center"
              });
            }
          });
          function stateColor(state) {
            switch (state) {
              case "wait":
                return "#CECECE";
              case "error":
                return "#e74c3c";
              case "warn":
                return "#f39c12";
              case "success":
                return "#2ecc71";
              case "running":
                return "#98F5F9";
            }
            return "#CECECE";
          }
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock("div", _hoisted_1$7, [
              createElementVNode("div", {
                ref_key: "cards",
                ref: cards,
                onWheel: withModifiers(scroll, ["stop"]),
                class: "card-grid"
              }, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(jobList2), (v) => {
                  var _a, _b;
                  return openBlock(), createElementBlock("div", {
                    ref_for: true,
                    ref_key: "jobListRef",
                    ref: jobListRef,
                    class: "card",
                    style: normalizeStyle({
                      "--state-color": stateColor((_a = unref(jobMap2).get(v.encryptJobId)) == null ? void 0 : _a.state),
                      "--state-show": unref(jobMap2).has(v.encryptJobId) ? "block" : "none"
                    })
                  }, [
                    createElementVNode("div", _hoisted_2$4, toDisplayString(v.brandIndustry) + "," + toDisplayString(v.jobDegree) + "," + toDisplayString(v.brandScaleName), 1),
                    createElementVNode("h3", _hoisted_3$4, toDisplayString(v.jobName), 1),
                    createElementVNode("h3", _hoisted_4$1, toDisplayString(v.salaryDesc), 1),
                    createElementVNode("div", null, [
                      createVNode(unref(ElSpace), {
                        size: 3,
                        spacer: "|",
                        wrap: ""
                      }, {
                        default: withCtx(() => [
                          (openBlock(true), createElementBlock(Fragment, null, renderList(v.skills, (tag) => {
                            return openBlock(), createBlock(unref(ElTag), {
                              size: "small",
                              effect: "plain",
                              type: "warning"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(tag), 1)
                              ]),
                              _: 2
                            }, 1024);
                          }), 256))
                        ]),
                        _: 2
                      }, 1024),
                      createVNode(unref(ElSpace), {
                        size: 3,
                        wrap: ""
                      }, {
                        default: withCtx(() => [
                          (openBlock(true), createElementBlock(Fragment, null, renderList(v.jobLabels, (tag) => {
                            return openBlock(), createBlock(unref(ElTag), {
                              size: "small",
                              effect: "plain",
                              type: "success"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(tag), 1)
                              ]),
                              _: 2
                            }, 1024);
                          }), 256))
                        ]),
                        _: 2
                      }, 1024)
                    ]),
                    createElementVNode("div", _hoisted_5, toDisplayString(v.welfareList.join(",")), 1),
                    createElementVNode("div", _hoisted_6, [
                      createElementVNode("img", {
                        alt: "",
                        class: "avatar",
                        height: "80",
                        src: v.brandLogo,
                        width: "80"
                      }, null, 8, _hoisted_7),
                      createElementVNode("div", null, [
                        createElementVNode("span", _hoisted_8, toDisplayString(v.brandName), 1),
                        createElementVNode("h4", null, toDisplayString(v.cityName) + "/" + toDisplayString(v.areaDistrict) + "/" + toDisplayString(v.businessDistrict), 1)
                      ])
                    ]),
                    createElementVNode("div", _hoisted_9, toDisplayString(((_b = unref(jobMap2).get(v.encryptJobId)) == null ? void 0 : _b.msg) || "无内容"), 1)
                  ], 4);
                }), 256))
              ], 544),
              createVNode(unref(ElSwitch), {
                modelValue: autoScroll.value,
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => autoScroll.value = $event),
                "inline-prompt": "",
                "active-text": "自动滚动",
                "inactive-text": "自动滚动"
              }, null, 8, ["modelValue"])
            ]);
          };
        }
      });
      const _export_sfc = (sfc, props) => {
        const target = sfc.__vccOpts || sfc;
        for (const [key, val] of props) {
          target[key] = val;
        }
        return target;
      };
      const cardVue = /* @__PURE__ */ _export_sfc(_sfc_main$d, [["__scopeId", "data-v-d2ec6d12"]]);
      const _sfc_main$c = /* @__PURE__ */ defineComponent({
        __name: "chat",
        setup(__props) {
          return (_ctx, _cache) => {
            return null;
          };
        }
      });
      const _hoisted_1$6 = {
        class: "hp-about",
        help: "谢谢你的关心"
      };
      const _hoisted_2$3 = { help: "梦想就是全职开源" };
      const _hoisted_3$3 = { help: "更应该感谢这些人" };
      const _hoisted_4 = {
        class: "hp-about",
        help: "如果对你有帮助一定要Star呀!",
        style: { "margin-left": "20px" }
      };
      const _sfc_main$b = /* @__PURE__ */ defineComponent({
        __name: "about",
        setup(__props) {
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock(Fragment, null, [
              createElementVNode("div", _hoisted_1$6, [
                createElementVNode("div", _hoisted_2$3, [
                  createTextVNode(" 作者:　 "),
                  createVNode(unref(ElLink), {
                    href: "https://github.com/Ocyss",
                    target: "_blank"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" Ocyss_04 ")
                    ]),
                    _: 1
                  })
                ]),
                createElementVNode("div", _hoisted_3$3, [
                  createTextVNode(" 鸣谢:　 "),
                  createVNode(unref(ElLink), {
                    href: "https://github.com/yangfeng20",
                    target: "_blank"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" yangfeng20 ")
                    ]),
                    _: 1
                  })
                ])
              ]),
              createElementVNode("div", _hoisted_4, [
                createElementVNode("div", null, [
                  createVNode(unref(ElLink), {
                    href: "https://github.com/Ocyss",
                    target: "_blank",
                    type: "danger"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" Boss-Helper [Boos直聘助手] ")
                    ]),
                    _: 1
                  })
                ]),
                createElementVNode("div", null, [
                  createVNode(unref(ElLink), {
                    href: "https://greasyfork.org/zh-CN/scripts/468125-boss-batch-push-boss%E7%9B%B4%E8%81%98%E6%89%B9%E9%87%8F%E6%8A%95%E7%AE%80%E5%8E%86",
                    target: "_blank",
                    type: "danger"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" Boss Batch Push [Boss直聘批量投简历] ")
                    ]),
                    _: 1
                  })
                ])
              ]),
              createVNode(unref(ElImage), {
                help: "可能并没什么用,只是不让页面空荡荡",
                style: { "width": "200px", "height": "200px" },
                src: "https://img2.imgtp.com/2024/03/16/Jipx1nKP.png",
                fit: "cover",
                loading: "lazy"
              })
            ], 64);
          };
        }
      });
      const _sfc_main$a = /* @__PURE__ */ defineComponent({
        __name: "config",
        props: /* @__PURE__ */ mergeModels({
          data: {}
        }, {
          "modelValue": { type: Boolean, ...{ required: true } },
          "modelModifiers": {}
        }),
        emits: ["update:modelValue"],
        setup(__props) {
          const { formData: formData2, confSaving, defaultFormData } = useConfFormData();
          const { modelData: modelData2 } = useModel();
          const props = __props;
          const show = useModel$1(__props, "modelValue");
          const val = ref(formData2[props.data].word);
          const m = formData2[props.data].model || [];
          const model = ref(Array.isArray(m) ? m : [m]);
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElDialog), {
              modelValue: show.value,
              "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => show.value = $event),
              title: unref(formInfoData)[_ctx.data].label,
              width: "70%",
              "align-center": "",
              "destroy-on-close": "",
              "z-index": 20
            }, {
              footer: withCtx(() => [
                createElementVNode("div", null, [
                  createVNode(unref(ElButton), {
                    onClick: _cache[2] || (_cache[2] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("取消")
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElPopconfirm), {
                    title: "恢复默认但不保存～",
                    onConfirm: _cache[3] || (_cache[3] = ($event) => val.value = unref(defaultFormData)[_ctx.data].word)
                  }, {
                    reference: withCtx(() => [
                      createVNode(unref(ElButton), { type: "info" }, {
                        default: withCtx(() => [
                          createTextVNode("默认")
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    onClick: _cache[4] || (_cache[4] = () => {
                      unref(formData2)[_ctx.data].model = model.value;
                      unref(formData2)[_ctx.data].word = val.value;
                      unref(confSaving)();
                      show.value = false;
                    })
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" 保存 ")
                    ]),
                    _: 1
                  })
                ])
              ]),
              default: withCtx(() => [
                createVNode(unref(ElSelectV2), {
                  modelValue: model.value,
                  "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => model.value = $event),
                  options: unref(modelData2),
                  props: { label: "name", value: "key" },
                  placeholder: "选择模型",
                  multiple: "",
                  style: { "width": "45%", "margin-bottom": "8px" }
                }, null, 8, ["modelValue", "options"]),
                createVNode(unref(ElText), null, {
                  default: withCtx(() => [
                    createTextVNode(" 此处使用 "),
                    createVNode(unref(ElLink), {
                      type: "primary",
                      href: "https://www.npmjs.com/package/mitem",
                      target: "_blank"
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" 模板引擎 mitem ")
                      ]),
                      _: 1
                    }),
                    createTextVNode(" 来渲染模板。当配置多个模型的时候将依次调用，使用第一个响应. ")
                  ]),
                  _: 1
                }),
                createVNode(unref(ElInput), {
                  modelValue: val.value,
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => val.value = $event),
                  style: { "width": "100%" },
                  autosize: { minRows: 10, maxRows: 18 },
                  type: "textarea",
                  placeholder: "如果无内容或错误内容请直接恢复默认，示例会随脚本更新"
                }, null, 8, ["modelValue"]),
                _ctx.data === "aiFiltering" ? (openBlock(), createBlock(unref(ElAlert), {
                  key: 0,
                  title: "自带尾巴",
                  type: "info",
                  description: `>>>下面是岗位相关信息:
      岗位描述:{{ card.postDescription}}
      薪酬:{{card.salaryDesc}}
      经验要求:{{card.experienceName}},学历要求:{{card.degreeName}}
      相关标签:{{card.jobLabels}},公司福利：{{data.welfareList}}
      >>>>>>>>>>我需要你输出Json格式的字符串，符合以下的定义
      interface aiFiltering {
      rating: number; // 分数，0-100分，低于60的我会筛选掉
      negative: string[] | string; // 扣分项，可以是一句话为什么扣分，也可以是数组代表多个扣分项
      positive: string[] | string; // 加分项，可以是一句话为什么加分，也可以是数组代表多个加分项
      }`
                }, null, 8, ["description"])) : createCommentVNode("", true)
              ]),
              _: 1
            }, 8, ["modelValue", "title"]);
          };
        }
      });
      const _hoisted_1$5 = { key: 0 };
      const _hoisted_2$2 = { key: 1 };
      const _hoisted_3$2 = { key: 0 };
      const _sfc_main$9 = /* @__PURE__ */ defineComponent({
        __name: "model",
        props: {
          "modelValue": { type: Boolean, ...{ required: true } },
          "modelModifiers": {}
        },
        emits: ["update:modelValue"],
        setup(__props) {
          const show = useModel$1(__props, "modelValue");
          const { modelData: modelData2, save: save2 } = useModel();
          let createData = ref({
            key: "",
            name: "",
            default: false,
            data: {
              mode: "ChatGPT",
              url: "",
              model: "gpt-3.5-turbo",
              apiKey: "",
              temperature: 0.35
            }
          });
          const createBoxShow = ref(false);
          function create() {
            createData.value = {
              key: "",
              name: "",
              default: false,
              data: {
                mode: "ChatGPT",
                url: "",
                model: "gpt-3.5-turbo",
                apiKey: "",
                temperature: 0.35
              }
            };
            createBoxShow.value = true;
          }
          function saveModel() {
            const d = toRaw(createData.value);
            if (d.key) {
              const old = modelData2.value.find((v) => v.key == d.key);
              if (old) {
                deepmerge(old, d, { clone: false });
              } else {
                d.key = (/* @__PURE__ */ new Date()).getTime().toString();
                modelData2.value.push(d);
              }
            } else {
              d.key = (/* @__PURE__ */ new Date()).getTime().toString();
              modelData2.value.push(d);
            }
            createBoxShow.value = false;
          }
          function changeMode(mode) {
            switch (mode) {
              case "ChatGPT":
                deepmerge(
                  createData.value.data,
                  {
                    mode,
                    url: "",
                    model: "gpt-3.5-turbo",
                    apiKey: ""
                  },
                  { clone: false }
                );
                break;
              case "自定义":
              case "仅记录":
                deepmerge(
                  createData.value.data,
                  {
                    mode,
                    url: "",
                    header: "",
                    data: ""
                  },
                  { clone: false }
                );
                break;
            }
          }
          function del(d) {
            modelData2.value = modelData2.value.filter((v) => d.key !== v.key);
            ElMessage.success("删除成功");
          }
          function copy(d) {
            d = JSON.parse(JSON.stringify(d));
            d.key = (/* @__PURE__ */ new Date()).getTime().toString();
            d.name = d.name + " 副本";
            modelData2.value.push(d);
            ElMessage.success("复制成功");
          }
          function edit(d) {
            createData.value = toRaw(d);
            createBoxShow.value = true;
          }
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElDialog), {
              modelValue: show.value,
              "onUpdate:modelValue": _cache[12] || (_cache[12] = ($event) => show.value = $event),
              title: "Ai模型配置",
              width: "70%",
              "align-center": "",
              "destroy-on-close": "",
              "z-index": 20
            }, {
              footer: withCtx(() => [
                createElementVNode("div", null, [
                  createVNode(unref(ElButton), {
                    onClick: _cache[11] || (_cache[11] = ($event) => show.value = false)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("取消")
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    onClick: create
                  }, {
                    default: withCtx(() => [
                      createTextVNode("新建")
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    onClick: unref(save2)
                  }, {
                    default: withCtx(() => [
                      createTextVNode("保存")
                    ]),
                    _: 1
                  }, 8, ["onClick"])
                ])
              ]),
              default: withCtx(() => [
                createVNode(unref(ElTable), {
                  data: unref(modelData2),
                  style: { "width": "100%" },
                  "table-layout": "auto"
                }, {
                  default: withCtx(() => [
                    createVNode(unref(ElTableColumn), {
                      prop: "name",
                      label: "名称"
                    }),
                    createVNode(unref(ElTableColumn), {
                      prop: "data.mode",
                      label: "类型"
                    }),
                    createVNode(unref(ElTableColumn), {
                      prop: "data.url",
                      label: "url"
                    }),
                    createVNode(unref(ElTableColumn), { label: "管理" }, {
                      default: withCtx((scope) => [
                        createVNode(unref(ElButton), {
                          link: "",
                          type: "primary",
                          size: "small",
                          onClick: () => del(scope.row)
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" 删除 ")
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode(unref(ElButton), {
                          link: "",
                          type: "primary",
                          size: "small",
                          onClick: () => copy(scope.row)
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" 复制 ")
                          ]),
                          _: 2
                        }, 1032, ["onClick"]),
                        createVNode(unref(ElButton), {
                          link: "",
                          type: "primary",
                          size: "small",
                          onClick: () => edit(scope.row)
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" 编辑 ")
                          ]),
                          _: 2
                        }, 1032, ["onClick"])
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["data"]),
                createVNode(unref(ElDialog), {
                  modelValue: createBoxShow.value,
                  "onUpdate:modelValue": _cache[10] || (_cache[10] = ($event) => createBoxShow.value = $event),
                  width: "70%",
                  title: "新建模型",
                  "append-to-body": ""
                }, {
                  footer: withCtx(() => [
                    createElementVNode("div", null, [
                      createVNode(unref(ElButton), {
                        onClick: _cache[9] || (_cache[9] = ($event) => createBoxShow.value = false)
                      }, {
                        default: withCtx(() => [
                          createTextVNode("取消")
                        ]),
                        _: 1
                      }),
                      createVNode(unref(ElButton), {
                        type: "primary",
                        onClick: saveModel
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(unref(createData).key ? "保存" : "创建"), 1)
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  default: withCtx(() => [
                    createVNode(unref(ElForm), {
                      "label-width": "auto",
                      "label-position": "top"
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(ElFormItem), { label: "名称:" }, {
                          default: withCtx(() => [
                            createVNode(unref(ElInput), {
                              modelValue: unref(createData).name,
                              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => unref(createData).name = $event)
                            }, null, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        createVNode(unref(ElFormItem), { label: "类别:" }, {
                          default: withCtx(() => [
                            createVNode(unref(ElRadioGroup), {
                              "model-value": unref(createData).data.mode,
                              size: "large",
                              "onUpdate:modelValue": changeMode
                            }, {
                              default: withCtx(() => [
                                createVNode(unref(ElRadioButton), {
                                  label: "ChatGPT",
                                  value: "ChatGPT"
                                }),
                                createVNode(unref(ElRadioButton), {
                                  label: "自定义",
                                  value: "自定义"
                                }),
                                createVNode(unref(ElRadioButton), {
                                  label: "仅记录",
                                  value: "仅记录"
                                })
                              ]),
                              _: 1
                            }, 8, ["model-value", "onUpdate:modelValue"])
                          ]),
                          _: 1
                        }),
                        unref(createData).data.mode === "ChatGPT" ? (openBlock(), createElementBlock("div", _hoisted_1$5, [
                          createVNode(unref(ElFormItem), { label: "host:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                modelValue: unref(createData).data.url,
                                "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => unref(createData).data.url = $event),
                                placeholder: "https://api.openai.com"
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElFormItem), { label: "model:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                modelValue: unref(createData).data.model,
                                "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => unref(createData).data.model = $event)
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElFormItem), { label: "apiKey:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                modelValue: unref(createData).data.apiKey,
                                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => unref(createData).data.apiKey = $event)
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElAlert), {
                            title: "高级配置，不懂勿改",
                            type: "warning",
                            "show-icon": ""
                          }),
                          createVNode(unref(ElFormItem), { label: "temperature:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElSlider), {
                                modelValue: unref(createData).data.temperature,
                                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => unref(createData).data.temperature = $event),
                                min: 0,
                                max: 1,
                                step: 0.01,
                                "show-input": "",
                                marks: { [0.2]: "严谨", [0.8]: "想象" }
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          })
                        ])) : unref(createData).data.mode === "自定义" || unref(createData).data.mode === "仅记录" ? (openBlock(), createElementBlock("div", _hoisted_2$2, [
                          createVNode(unref(ElFormItem), { label: "请求方式:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElSelect), {
                                "model-value": "post",
                                disabled: "",
                                style: { "width": "240px" }
                              }, {
                                default: withCtx(() => [
                                  createVNode(unref(ElOption), {
                                    label: "POST",
                                    value: "post"
                                  })
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElFormItem), { label: "url:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                modelValue: unref(createData).data.url,
                                "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => unref(createData).data.url = $event),
                                placeholder: "请输入请求地址"
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElFormItem), { label: "header:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                autosize: "",
                                type: "textarea",
                                modelValue: unref(createData).data.header,
                                "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => unref(createData).data.header = $event),
                                placeholder: "使用Json格式"
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          createVNode(unref(ElAlert), {
                            title: "Json格式，其中{{message}}为发给GPT的消息,需要引号包裹，{{raw}}为原始数据，不需要引号包裹通常作为json值对象，在记录模式下很使用",
                            type: "info",
                            "show-icon": ""
                          }),
                          createVNode(unref(ElFormItem), { label: "data:" }, {
                            default: withCtx(() => [
                              createVNode(unref(ElInput), {
                                autosize: "",
                                type: "textarea",
                                modelValue: unref(createData).data.data,
                                "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => unref(createData).data.data = $event)
                              }, null, 8, ["modelValue"])
                            ]),
                            _: 1
                          }),
                          unref(createData).data.mode === "自定义" ? (openBlock(), createElementBlock("div", _hoisted_3$2, [
                            createVNode(unref(ElAlert), {
                              title: "响应内容提取，返回体将作为模板引擎的数据传入，数据一般在data.data，最终产物将作为gpt响应发给boos或筛选判断。（发送给Boos需要字符串，筛选需要json格式）",
                              type: "info",
                              "show-icon": ""
                            }),
                            createVNode(unref(ElFormItem), { label: "req:" }, {
                              default: withCtx(() => [
                                createVNode(unref(ElInput), {
                                  modelValue: unref(createData).data.req,
                                  "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => unref(createData).data.req = $event),
                                  placeholder: "示例：data.message[0]"
                                }, null, 8, ["modelValue"])
                              ]),
                              _: 1
                            })
                          ])) : createCommentVNode("", true)
                        ])) : createCommentVNode("", true)
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }, 8, ["modelValue"])
              ]),
              _: 1
            }, 8, ["modelValue"]);
          };
        }
      });
      const _sfc_main$8 = {};
      const _hoisted_1$4 = {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: "0 0 24 24"
      };
      const _hoisted_2$1 = /* @__PURE__ */ createElementVNode("g", { fill: "none" }, [
        /* @__PURE__ */ createElementVNode("path", {
          d: "M12.01 2.25c.735.008 1.466.093 2.182.253a.75.75 0 0 1 .582.649l.17 1.527a1.384 1.384 0 0 0 1.928 1.116l1.4-.615a.75.75 0 0 1 .85.174a9.793 9.793 0 0 1 2.204 3.792a.75.75 0 0 1-.271.825l-1.242.916a1.38 1.38 0 0 0 .001 2.226l1.243.915a.75.75 0 0 1 .271.826a9.798 9.798 0 0 1-2.203 3.792a.75.75 0 0 1-.849.175l-1.406-.617a1.38 1.38 0 0 0-1.927 1.114l-.169 1.526a.75.75 0 0 1-.571.647a9.518 9.518 0 0 1-4.406 0a.75.75 0 0 1-.572-.647l-.17-1.524a1.382 1.382 0 0 0-1.924-1.11l-1.407.616a.75.75 0 0 1-.849-.175a9.798 9.798 0 0 1-2.203-3.796a.75.75 0 0 1 .271-.826l1.244-.916a1.38 1.38 0 0 0 0-2.226l-1.243-.914a.75.75 0 0 1-.272-.826a9.793 9.793 0 0 1 2.205-3.792a.75.75 0 0 1 .849-.174l1.4.615a1.387 1.387 0 0 0 1.93-1.118l.17-1.526a.75.75 0 0 1 .583-.65c.718-.159 1.45-.243 2.202-.252zM13.576 8h-3.272l3.105 4l-2.773 3.514a.3.3 0 0 0 .236.486h2.704a.3.3 0 0 0 .237-.116l2.778-3.577a.5.5 0 0 0 0-.614l-2.778-3.577A.3.3 0 0 0 13.575 8zm-3.898.803l-2.264 2.889a.5.5 0 0 0-.052.535l.052.081l2.002 2.556l.05.05a.3.3 0 0 0 .372 0l.049-.049l1.336-1.68a.3.3 0 0 0 .043-.301l-.042-.07l-.638-.814l.78-.996l-1.688-2.201z",
          fill: "currentColor"
        })
      ], -1);
      const _hoisted_3$1 = [
        _hoisted_2$1
      ];
      function _sfc_render(_ctx, _cache) {
        return openBlock(), createElementBlock("svg", _hoisted_1$4, _hoisted_3$1);
      }
      const settingsVue = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render]]);
      const _sfc_main$7 = /* @__PURE__ */ defineComponent({
        __name: "formAi",
        props: {
          label: {},
          lock: { type: Boolean },
          help: {},
          disabled: { type: Boolean },
          data: {}
        },
        emits: ["change", "show"],
        setup(__props, { emit: __emit }) {
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElButtonGroup), {
              type: _ctx.data.enable ? "success" : "danger",
              help: _ctx.help
            }, {
              default: withCtx(() => [
                createVNode(unref(ElButton), {
                  disabled: _ctx.lock || _ctx.disabled,
                  onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$emit("change", _ctx.data))
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(_ctx.label), 1)
                  ]),
                  _: 1
                }, 8, ["disabled"]),
                createVNode(unref(ElButton), {
                  icon: settingsVue,
                  disabled: _ctx.disabled,
                  onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("show"))
                }, null, 8, ["disabled"])
              ]),
              _: 1
            }, 8, ["type", "help"]);
          };
        }
      });
      const _hoisted_1$3 = { style: { "margin-top": "15px" } };
      const _sfc_main$6 = /* @__PURE__ */ defineComponent({
        __name: "ai",
        setup(__props) {
          const { formData: formData2, confSaving } = useConfFormData();
          const { deliverLock: deliverLock2 } = useCommon();
          const useModelData = useModel();
          const modelData2 = useModelData.modelData.value.filter(
            (m2) => m2.data.mode === "仅记录"
          );
          const aiBoxShow = ref(false);
          const aiConfBoxShow = ref(false);
          const aiBox = ref(
            "aiGreeting"
          );
          function change(v) {
            v.enable = !v.enable;
            confSaving();
          }
          const m = formData2.record.model || [];
          const recordModel = ref(Array.isArray(m) ? m : [m]);
          return (_ctx, _cache) => {
            const _directive_key = resolveDirective("key");
            return openBlock(), createElementBlock(Fragment, null, [
              createVNode(unref(ElSpace), {
                wrap: "",
                fill: "",
                "fill-ratio": 32,
                style: { "width": "100%" }
              }, {
                default: withCtx(() => [
                  createVNode(_sfc_main$7, mergeProps(unref(formInfoData).aiGreeting, {
                    data: unref(formData2).aiGreeting,
                    lock: unref(deliverLock2),
                    onShow: _cache[0] || (_cache[0] = ($event) => {
                      aiBox.value = "aiGreeting";
                      aiBoxShow.value = true;
                    }),
                    onChange: change
                  }), null, 16, ["data", "lock"]),
                  createVNode(_sfc_main$7, mergeProps(unref(formInfoData).aiFiltering, {
                    data: unref(formData2).aiFiltering,
                    lock: unref(deliverLock2),
                    onShow: _cache[1] || (_cache[1] = ($event) => {
                      aiBox.value = "aiFiltering";
                      aiBoxShow.value = true;
                    }),
                    onChange: change
                  }), null, 16, ["data", "lock"]),
                  createVNode(_sfc_main$7, mergeProps(unref(formInfoData).aiReply, {
                    data: unref(formData2).aiReply,
                    onShow: _cache[2] || (_cache[2] = ($event) => {
                      aiBox.value = "aiReply";
                      aiBoxShow.value = true;
                    }),
                    onChange: change,
                    disabled: ""
                  }), null, 16, ["data"]),
                  createVNode(_sfc_main$7, mergeProps(unref(formInfoData).record, {
                    data: unref(formData2).record,
                    onShow: _cache[3] || (_cache[3] = ($event) => {
                      aiBox.value = "record";
                      aiBoxShow.value = true;
                    }),
                    onChange: change
                  }), null, 16, ["data"])
                ]),
                _: 1
              }),
              createElementVNode("div", _hoisted_1$3, [
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "有那么多功能，当然要分等级了，不然岂不是浪费了这么多的模型（主要缺钱）",
                  onClick: _cache[4] || (_cache[4] = ($event) => aiConfBoxShow.value = true)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 模型配置 ")
                  ]),
                  _: 1
                })
              ]),
              (openBlock(), createBlock(Teleport, { to: "body" }, [
                createVNode(_sfc_main$9, {
                  modelValue: aiConfBoxShow.value,
                  "onUpdate:modelValue": _cache[5] || (_cache[5] = ($event) => aiConfBoxShow.value = $event)
                }, null, 8, ["modelValue"]),
                aiBoxShow.value && aiBox.value !== "record" ? withDirectives((openBlock(), createBlock(_sfc_main$a, {
                  key: 0,
                  data: aiBox.value,
                  modelValue: aiBoxShow.value,
                  "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => aiBoxShow.value = $event)
                }, null, 8, ["data", "modelValue"])), [
                  [_directive_key, aiBox.value]
                ]) : createCommentVNode("", true),
                aiBoxShow.value && aiBox.value === "record" ? (openBlock(), createBlock(unref(ElDialog), {
                  key: 1,
                  modelValue: aiBoxShow.value,
                  "onUpdate:modelValue": _cache[10] || (_cache[10] = ($event) => aiBoxShow.value = $event),
                  title: unref(formInfoData).record.label,
                  width: "70%",
                  "align-center": "",
                  "destroy-on-close": "",
                  "z-index": 20
                }, {
                  footer: withCtx(() => [
                    createElementVNode("div", null, [
                      createVNode(unref(ElButton), {
                        onClick: _cache[8] || (_cache[8] = ($event) => aiBoxShow.value = false)
                      }, {
                        default: withCtx(() => [
                          createTextVNode("取消")
                        ]),
                        _: 1
                      }),
                      createVNode(unref(ElButton), {
                        type: "primary",
                        onClick: _cache[9] || (_cache[9] = () => {
                          unref(formData2).record.model = recordModel.value;
                          unref(confSaving)();
                          aiBoxShow.value = false;
                        })
                      }, {
                        default: withCtx(() => [
                          createTextVNode(" 保存 ")
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  default: withCtx(() => [
                    createVNode(unref(ElSelectV2), {
                      modelValue: recordModel.value,
                      "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => recordModel.value = $event),
                      options: unref(modelData2),
                      props: { label: "name", value: "key" },
                      placeholder: "选择模型",
                      multiple: "",
                      style: { "width": "45%", "margin-bottom": "8px" }
                    }, null, 8, ["modelValue", "options"]),
                    createVNode(unref(ElAlert), {
                      title: "数据是宝贵的，每一次投递完都会去请求一次，不要问为什么在AI类别里面为啥是AI同款模型",
                      type: "warning",
                      "show-icon": "",
                      closable: false
                    })
                  ]),
                  _: 1
                }, 8, ["modelValue", "title"])) : createCommentVNode("", true)
              ]))
            ], 64);
          };
        }
      });
      const _sfc_main$5 = /* @__PURE__ */ defineComponent({
        __name: "formItem",
        props: /* @__PURE__ */ mergeModels({
          label: {},
          help: {},
          disabled: { type: Boolean }
        }, {
          "include": { type: Boolean, ...{
            default: void 0
          } },
          "includeModifiers": {},
          "enable": { type: Boolean, ...{ required: true } },
          "enableModifiers": {}
        }),
        emits: ["update:include", "update:enable"],
        setup(__props) {
          const include = useModel$1(__props, "include");
          const enable = useModel$1(__props, "enable");
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElFormItem), { help: _ctx.help }, {
              label: withCtx(() => [
                createVNode(unref(ElCheckbox), {
                  modelValue: enable.value,
                  "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => enable.value = $event),
                  label: _ctx.label,
                  size: "small"
                }, null, 8, ["modelValue", "label"]),
                include.value !== void 0 ? (openBlock(), createBlock(unref(ElLink), {
                  key: 0,
                  onClick: _cache[1] || (_cache[1] = withModifiers(($event) => include.value = !include.value, ["stop"])),
                  type: include.value ? "primary" : "warning",
                  size: "small",
                  disabled: _ctx.disabled
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(include.value ? "包含" : "排除"), 1)
                  ]),
                  _: 1
                }, 8, ["type", "disabled"])) : createCommentVNode("", true)
              ]),
              default: withCtx(() => [
                renderSlot(_ctx.$slots, "default")
              ]),
              _: 3
            }, 8, ["help"]);
          };
        }
      });
      const _sfc_main$4 = /* @__PURE__ */ defineComponent({
        __name: "formSelect",
        props: {
          "value": { required: true },
          "valueModifiers": {},
          "options": { required: true },
          "optionsModifiers": {}
        },
        emits: ["update:value", "update:options"],
        setup(__props) {
          const value = useModel$1(__props, "value");
          const options = useModel$1(__props, "options");
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElSelect), {
              modelValue: value.value,
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => value.value = $event),
              multiple: "",
              filterable: "",
              "allow-create": "",
              "default-first-option": "",
              "reserve-keyword": false,
              style: { "width": "240px" },
              placeholder: ""
            }, {
              default: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(options.value, (item) => {
                  return openBlock(), createBlock(unref(ElOption), {
                    key: item,
                    label: item,
                    value: item
                  }, null, 8, ["label", "value"]);
                }), 128))
              ]),
              _: 1
            }, 8, ["modelValue"]);
          };
        }
      });
      const _hoisted_1$2 = { style: { "margin-top": "15px" } };
      const _sfc_main$3 = /* @__PURE__ */ defineComponent({
        __name: "config",
        setup(__props) {
          const { formData: formData2, confDelete, confExport, confImport, confReload, confSaving } = useConfFormData();
          const { deliverLock: deliverLock2 } = useCommon();
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock(Fragment, null, [
              createVNode(unref(ElForm), {
                ref: "formRef",
                inline: "",
                "label-position": "left",
                "label-width": "auto",
                model: unref(formData2),
                disabled: unref(deliverLock2)
              }, {
                default: withCtx(() => [
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).company, {
                    enable: unref(formData2).company.enable,
                    "onUpdate:enable": _cache[2] || (_cache[2] = ($event) => unref(formData2).company.enable = $event),
                    include: unref(formData2).company.include,
                    "onUpdate:include": _cache[3] || (_cache[3] = ($event) => unref(formData2).company.include = $event),
                    disabled: unref(deliverLock2)
                  }), {
                    default: withCtx(() => [
                      createVNode(_sfc_main$4, {
                        value: unref(formData2).company.value,
                        "onUpdate:value": _cache[0] || (_cache[0] = ($event) => unref(formData2).company.value = $event),
                        options: unref(formData2).company.options,
                        "onUpdate:options": _cache[1] || (_cache[1] = ($event) => unref(formData2).company.options = $event)
                      }, null, 8, ["value", "options"])
                    ]),
                    _: 1
                  }, 16, ["enable", "include", "disabled"]),
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).jobTitle, {
                    enable: unref(formData2).jobTitle.enable,
                    "onUpdate:enable": _cache[6] || (_cache[6] = ($event) => unref(formData2).jobTitle.enable = $event),
                    include: unref(formData2).jobTitle.include,
                    "onUpdate:include": _cache[7] || (_cache[7] = ($event) => unref(formData2).jobTitle.include = $event),
                    disabled: unref(deliverLock2)
                  }), {
                    default: withCtx(() => [
                      createVNode(_sfc_main$4, {
                        value: unref(formData2).jobTitle.value,
                        "onUpdate:value": _cache[4] || (_cache[4] = ($event) => unref(formData2).jobTitle.value = $event),
                        options: unref(formData2).jobTitle.options,
                        "onUpdate:options": _cache[5] || (_cache[5] = ($event) => unref(formData2).jobTitle.options = $event)
                      }, null, 8, ["value", "options"])
                    ]),
                    _: 1
                  }, 16, ["enable", "include", "disabled"]),
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).jobContent, {
                    enable: unref(formData2).jobContent.enable,
                    "onUpdate:enable": _cache[10] || (_cache[10] = ($event) => unref(formData2).jobContent.enable = $event),
                    include: unref(formData2).jobContent.include,
                    "onUpdate:include": _cache[11] || (_cache[11] = ($event) => unref(formData2).jobContent.include = $event),
                    disabled: unref(deliverLock2)
                  }), {
                    default: withCtx(() => [
                      createVNode(_sfc_main$4, {
                        value: unref(formData2).jobContent.value,
                        "onUpdate:value": _cache[8] || (_cache[8] = ($event) => unref(formData2).jobContent.value = $event),
                        options: unref(formData2).jobContent.options,
                        "onUpdate:options": _cache[9] || (_cache[9] = ($event) => unref(formData2).jobContent.options = $event)
                      }, null, 8, ["value", "options"])
                    ]),
                    _: 1
                  }, 16, ["enable", "include", "disabled"]),
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).salaryRange, {
                    enable: unref(formData2).salaryRange.enable,
                    "onUpdate:enable": _cache[13] || (_cache[13] = ($event) => unref(formData2).salaryRange.enable = $event)
                  }), {
                    default: withCtx(() => [
                      createVNode(unref(ElInput), {
                        modelValue: unref(formData2).salaryRange.value,
                        "onUpdate:modelValue": _cache[12] || (_cache[12] = ($event) => unref(formData2).salaryRange.value = $event),
                        style: { "width": "240px" }
                      }, null, 8, ["modelValue"])
                    ]),
                    _: 1
                  }, 16, ["enable"]),
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).companySizeRange, {
                    enable: unref(formData2).companySizeRange.enable,
                    "onUpdate:enable": _cache[15] || (_cache[15] = ($event) => unref(formData2).companySizeRange.enable = $event)
                  }), {
                    default: withCtx(() => [
                      createVNode(unref(ElInput), {
                        modelValue: unref(formData2).companySizeRange.value,
                        "onUpdate:modelValue": _cache[14] || (_cache[14] = ($event) => unref(formData2).companySizeRange.value = $event),
                        modelModifiers: { lazy: true },
                        style: { "width": "240px" }
                      }, null, 8, ["modelValue"])
                    ]),
                    _: 1
                  }, 16, ["enable"]),
                  createVNode(_sfc_main$5, mergeProps(unref(formInfoData).customGreeting, {
                    enable: unref(formData2).customGreeting.enable,
                    "onUpdate:enable": _cache[17] || (_cache[17] = ($event) => unref(formData2).customGreeting.enable = $event)
                  }), {
                    default: withCtx(() => [
                      createVNode(unref(ElInput), {
                        modelValue: unref(formData2).customGreeting.value,
                        "onUpdate:modelValue": _cache[16] || (_cache[16] = ($event) => unref(formData2).customGreeting.value = $event),
                        modelModifiers: { lazy: true },
                        style: { "width": "240px" }
                      }, null, 8, ["modelValue"])
                    ]),
                    _: 1
                  }, 16, ["enable"]),
                  createVNode(unref(ElFormItem), { label: "处理动画" }, {
                    default: withCtx(() => [
                      createVNode(unref(ElSelect), {
                        onVModel: unref(formData2).animation,
                        clearable: "",
                        style: { "width": "240px" },
                        placeholder: "无动画"
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(ElOption), {
                            label: "边框",
                            value: "frame"
                          }),
                          createVNode(unref(ElOption), {
                            label: "卡片",
                            value: "card"
                          }),
                          createVNode(unref(ElOption), {
                            label: "一起",
                            value: "together"
                          })
                        ]),
                        _: 1
                      }, 8, ["onVModel"])
                    ]),
                    _: 1
                  }),
                  createElementVNode("div", null, [
                    createVNode(unref(ElCheckbox), mergeProps(unref(formInfoData).greetingVariable, {
                      modelValue: unref(formData2).greetingVariable.value,
                      "onUpdate:modelValue": _cache[18] || (_cache[18] = ($event) => unref(formData2).greetingVariable.value = $event),
                      border: ""
                    }), null, 16, ["modelValue"]),
                    createVNode(unref(ElCheckbox), mergeProps(unref(formInfoData).activityFilter, {
                      modelValue: unref(formData2).activityFilter.value,
                      "onUpdate:modelValue": _cache[19] || (_cache[19] = ($event) => unref(formData2).activityFilter.value = $event),
                      border: ""
                    }), null, 16, ["modelValue"]),
                    createVNode(unref(ElCheckbox), mergeProps(unref(formInfoData).notification, {
                      modelValue: unref(formData2).notification.value,
                      "onUpdate:modelValue": _cache[20] || (_cache[20] = ($event) => unref(formData2).notification.value = $event),
                      border: ""
                    }), null, 16, ["modelValue"])
                  ])
                ]),
                _: 1
              }, 8, ["model", "disabled"]),
              createElementVNode("div", _hoisted_1$2, [
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "保存配置，用于后续直接使用当前配置。",
                  onClick: unref(confSaving)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 保存配置 ")
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "重新加载本地配置",
                  onClick: unref(confReload)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 重载配置 ")
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "互联网就是要分享",
                  onClick: unref(confExport)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 导出配置 ")
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "互联网就是要分享",
                  onClick: unref(confImport)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 导入配置 ")
                  ]),
                  _: 1
                }, 8, ["onClick"]),
                createVNode(unref(ElButton), {
                  type: "primary",
                  help: "清空配置,不会帮你保存,可以重载恢复",
                  onClick: unref(confDelete)
                }, {
                  default: withCtx(() => [
                    createTextVNode(" 删除配置 ")
                  ]),
                  _: 1
                }, 8, ["onClick"])
              ])
            ], 64);
          };
        }
      });
      const _sfc_main$2 = /* @__PURE__ */ defineComponent({
        __name: "logs",
        setup(__props) {
          const tableRef = ref();
          const { data: data2, columns: columns2, Row } = useLog();
          return (_ctx, _cache) => {
            return openBlock(), createBlock(unref(ElAutoResizer), { disableHeight: true }, {
              default: withCtx(({ width }) => [
                createVNode(unref(ElTableV2), {
                  ref_key: "tableRef",
                  ref: tableRef,
                  columns: unref(columns2),
                  data: unref(data2),
                  height: 360,
                  width
                }, null, 8, ["columns", "data", "width"])
              ]),
              _: 1
            });
          };
        }
      });
      const page = ref({ page: 1, pageSize: 30 });
      const pageChange = ref((v) => {
      });
      const initPage = useHookVueData("#wrap .page-job-wrapper", "pageVo", page);
      const initChange = useHookVueFn("#wrap .page-job-wrapper", "pageChangeAction");
      const next = () => {
        if (page.value.page >= 10)
          return;
        pageChange.value(page.value.page + 1);
      };
      const prev = () => {
        if (page.value.page <= 1)
          return;
        pageChange.value(page.value.page - 1);
      };
      const usePager = () => {
        return {
          page,
          pageChange,
          next,
          prev,
          initPager: () => {
            initPage();
            pageChange.value = initChange();
          }
        };
      };
      const _hoisted_1$1 = { class: "el-dropdown-link" };
      const _hoisted_2 = /* @__PURE__ */ createElementVNode("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 1024 1024"
      }, [
        /* @__PURE__ */ createElementVNode("path", {
          fill: "currentColor",
          d: "M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"
        })
      ], -1);
      const _hoisted_3 = { style: { "display": "flex" } };
      const _sfc_main$1 = /* @__PURE__ */ defineComponent({
        __name: "statistics",
        setup(__props) {
          const log2 = useLog();
          const { todayData: todayData2, statisticsData } = useStatistics();
          const { deliverLock: deliverLock2, deliverStop: deliverStop2 } = useCommon();
          const { jobListHandle: jobListHandle2 } = useDeliver();
          const { jobList: jobList2, jobMap: jobMap2 } = useJobList();
          const { next: next2, page: page2, prev: prev2 } = usePager();
          const { formData: formData2 } = useConfFormData();
          const statisticCycle = ref(1);
          const statisticCycleData = [
            {
              label: "近三日投递",
              help: "愿你每一次投递都能得到回应",
              date: 3
            },
            {
              label: "本周投递",
              help: "愿你早日找到心满意足的工作",
              date: 7
            },
            {
              label: "本月投递",
              help: "愿你在面试中得到满意的结果",
              date: 30
            },
            {
              label: "历史投递",
              help: "愿你能早九晚五还双休带五险",
              date: -1
            }
          ];
          const cycle = computed(() => {
            const date = statisticCycleData[statisticCycle.value].date;
            let ans = 0;
            for (var i = 0; (date == -1 || i < date - 1) && i < statisticsData.length; i++) {
              ans += statisticsData[i].success;
            }
            return ans;
          });
          async function startBatch() {
            log2.reset();
            deliverLock2.value = true;
            try {
              logger.debug("start batch", page2);
              while (page2.value.page <= 10 && !deliverStop2.value) {
                await delay(1e4);
                await jobListHandle2(jobList2.value, jobMap2.actions);
                if (deliverStop2.value) {
                  break;
                }
                await delay(12e4);
                next2();
                jobMap2.actions.clear();
              }
            } catch (e) {
              logger.error("获取失败", e);
              ElMessage.error("获取失败!");
            } finally {
              logger.debug(log2.data);
              ElMessage.info("投递结束");
              if (formData2.notification.value) {
                notification("投递结束");
              }
              deliverLock2.value = false;
              deliverStop2.value = false;
            }
          }
          function stopBatch() {
            deliverStop2.value = true;
          }
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock(Fragment, null, [
              createVNode(unref(ElRow), { gutter: 20 }, {
                default: withCtx(() => [
                  createVNode(unref(ElCol), { span: 5 }, {
                    default: withCtx(() => [
                      createVNode(unref(ElStatistic), {
                        help: "统计当天脚本扫描过的所有岗位",
                        value: unref(todayData2).total,
                        title: "岗位总数：",
                        suffix: "份"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElCol), { span: 5 }, {
                    default: withCtx(() => [
                      createVNode(unref(ElStatistic), {
                        help: "统计当天岗位过滤的比例,被过滤/总数",
                        value: (unref(todayData2).total - unref(todayData2).success) / unref(todayData2).total * 100,
                        title: "过滤比例：",
                        suffix: "%"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElCol), { span: 5 }, {
                    default: withCtx(() => [
                      createVNode(unref(ElStatistic), {
                        help: "统计当天岗位中已沟通的比例,已沟通/总数",
                        value: unref(todayData2).repeat / unref(todayData2).total * 100,
                        title: "沟通比例：",
                        suffix: "%"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElCol), { span: 5 }, {
                    default: withCtx(() => [
                      createVNode(unref(ElStatistic), {
                        help: "统计当天岗位中的活跃情况,不活跃/总数",
                        value: unref(todayData2).activityFilter / unref(todayData2).total * 100,
                        title: "活跃比例：",
                        suffix: "%"
                      }, null, 8, ["value"])
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElCol), { span: 4 }, {
                    default: withCtx(() => [
                      createVNode(unref(ElStatistic), {
                        help: statisticCycleData[statisticCycle.value].help,
                        value: cycle.value + unref(todayData2).success,
                        suffix: "份"
                      }, {
                        title: withCtx(() => [
                          createVNode(unref(ElDropdown), {
                            trigger: "click",
                            onCommand: _cache[0] || (_cache[0] = (arg) => {
                              statisticCycle.value = arg;
                            })
                          }, {
                            dropdown: withCtx(() => [
                              createVNode(unref(ElDropdownMenu), null, {
                                default: withCtx(() => [
                                  (openBlock(), createElementBlock(Fragment, null, renderList(statisticCycleData, (item, index) => {
                                    return createVNode(unref(ElDropdownItem), { command: index }, {
                                      default: withCtx(() => [
                                        createTextVNode(toDisplayString(item.label), 1)
                                      ]),
                                      _: 2
                                    }, 1032, ["command"]);
                                  }), 64))
                                ]),
                                _: 1
                              })
                            ]),
                            default: withCtx(() => [
                              createElementVNode("span", _hoisted_1$1, [
                                createTextVNode(toDisplayString(statisticCycleData[statisticCycle.value].label) + ": ", 1),
                                createVNode(unref(ElIcon), { class: "el-icon--right" }, {
                                  default: withCtx(() => [
                                    _hoisted_2
                                  ]),
                                  _: 1
                                })
                              ])
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }, 8, ["help", "value"])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }),
              createElementVNode("div", _hoisted_3, [
                createVNode(unref(ElButtonGroup), { style: { "margin": "10px 30px 0 0" } }, {
                  default: withCtx(() => [
                    createVNode(unref(ElButton), {
                      type: "primary",
                      help: "点击开始就会开始投递",
                      loading: unref(deliverLock2),
                      onClick: startBatch
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" 开始 ")
                      ]),
                      _: 1
                    }, 8, ["loading"]),
                    unref(deliverLock2) && !unref(deliverStop2) ? (openBlock(), createBlock(unref(ElButton), {
                      key: 0,
                      type: "warning",
                      help: "暂停后应该能继续",
                      onClick: stopBatch
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" 暂停 ")
                      ]),
                      _: 1
                    })) : createCommentVNode("", true),
                    unref(deliverLock2) && !unref(deliverStop2) ? (openBlock(), createBlock(unref(ElButton), {
                      key: 1,
                      type: "danger",
                      help: "停止后应该不能继续",
                      onClick: stopBatch
                    }, {
                      default: withCtx(() => [
                        createTextVNode(" 停止 ")
                      ]),
                      _: 1
                    })) : createCommentVNode("", true)
                  ]),
                  _: 1
                }),
                createVNode(unref(ElProgress), {
                  help: "我会统计当天脚本投递的数量,boos直聘限制100,该记录并不准确倒车的噢",
                  style: { "flex": "1" },
                  percentage: unref(todayData2).success
                }, null, 8, ["percentage"])
              ])
            ], 64);
          };
        }
      });
      const _hoisted_1 = { key: 0 };
      const _sfc_main = /* @__PURE__ */ defineComponent({
        __name: "ui",
        setup(__props) {
          const { initPager } = usePager();
          const { initJobList } = useJobList();
          const { x, y } = useMouse({ type: "client" });
          const { total: total2, current: current2 } = useDeliver();
          const helpVisible = ref(false);
          const searchRef = ref();
          const tabsRef = ref();
          const helpContent = ref("鼠标移到对应元素查看提示");
          const helpElWidth = ref(400);
          const { isOutside } = useMouseInElement(tabsRef);
          const triggerRef = computed(() => {
            return {
              getBoundingClientRect() {
                return DOMRect.fromRect({
                  width: 0,
                  height: 0,
                  x: x.value,
                  y: y.value
                });
              }
            };
          });
          const boxStyles = computed(() => {
            if (helpVisible.value && !isOutside.value) {
              const element = document.elementFromPoint(x.value, y.value);
              const el = findHelp(element);
              if (el) {
                const bounding = el.getBoundingClientRect();
                helpElWidth.value = bounding.width;
                return {
                  width: `${bounding.width}px`,
                  height: `${bounding.height}px`,
                  left: `${bounding.left}px`,
                  top: `${bounding.top}px`,
                  display: "block",
                  backgroundColor: "#3eaf7c33",
                  transition: "all 0.08s linear"
                };
              }
            }
            return {
              display: "none"
            };
          });
          function findHelp(dom) {
            if (!dom)
              return;
            const help = dom.getAttribute("help");
            if (help) {
              helpContent.value = help;
              return dom;
            }
            return findHelp(dom.parentElement);
          }
          onMounted(() => {
            initJobList();
            initPager();
            elmGetter.get([
              ".job-search-wrapper .job-search-box.clearfix",
              ".job-search-wrapper .search-condition-wrapper.clearfix"
            ]).then(([searchEl, conditionEl]) => {
              searchRef.value.$el.appendChild(searchEl);
              searchRef.value.$el.appendChild(conditionEl);
              elmGetter.rm(".job-search-scan", searchEl);
            });
          });
          return (_ctx, _cache) => {
            return openBlock(), createElementBlock(Fragment, null, [
              createElementVNode("h2", null, [
                createTextVNode(" Boos-Helper "),
                unref(total2) > 0 ? (openBlock(), createElementBlock("span", _hoisted_1, toDisplayString(unref(current2) + 1) + "/" + toDisplayString(unref(total2)), 1)) : createCommentVNode("", true)
              ]),
              createElementVNode("div", {
                style: normalizeStyle([{ "z-index": "999", "position": "fixed", "pointer-events": "none", "border-width": "1px" }, boxStyles.value])
              }, null, 4),
              createVNode(unref(ElTooltip), {
                visible: helpVisible.value && !unref(isOutside),
                "virtual-ref": triggerRef.value
              }, {
                content: withCtx(() => [
                  createElementVNode("div", {
                    style: normalizeStyle(`width: auto;max-width:${helpElWidth.value}px;font-size:17px;`)
                  }, toDisplayString(helpContent.value), 5)
                ]),
                _: 1
              }, 8, ["visible", "virtual-ref"]),
              createVNode(unref(ElTabs), {
                ref_key: "tabsRef",
                ref: tabsRef,
                help: "鼠标移到对应元素查看提示"
              }, {
                default: withCtx(() => [
                  createVNode(unref(ElTabPane), {
                    label: "统计",
                    help: "失败是成功她妈"
                  }, {
                    default: withCtx(() => [
                      createVNode(_sfc_main$1)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElTabPane), {
                    label: "筛选",
                    ref_key: "searchRef",
                    ref: searchRef,
                    help: "可能真的需要?帮助?"
                  }, null, 512),
                  createVNode(unref(ElTabPane), {
                    label: "配置",
                    help: "建议全文背诵"
                  }, {
                    default: withCtx(() => [
                      createVNode(_sfc_main$3)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElTabPane), {
                    label: "AI",
                    help: "AI时代，脚本怎么能落伍!"
                  }, {
                    default: withCtx(() => [
                      createVNode(_sfc_main$6)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElTabPane), {
                    label: "日志",
                    help: "反正你也不看"
                  }, {
                    default: withCtx(() => [
                      createVNode(_sfc_main$2)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElTabPane), {
                    label: "关于",
                    class: "hp-about-box",
                    help: "项目是写不完美的,但总要去追求完美"
                  }, {
                    default: withCtx(() => [
                      createVNode(_sfc_main$b)
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElTabPane), null, {
                    label: withCtx(() => [
                      createVNode(unref(ElCheckbox), {
                        modelValue: helpVisible.value,
                        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => helpVisible.value = $event),
                        label: "帮助",
                        size: "large",
                        onClick: _cache[1] || (_cache[1] = withModifiers(() => {
                        }, ["stop"]))
                      }, null, 8, ["modelValue"])
                    ]),
                    _: 1
                  })
                ]),
                _: 1
              }, 512),
              (openBlock(), createBlock(Teleport, { to: ".page-job-inner .page-job-content" }, [
                createVNode(cardVue)
              ])),
              (openBlock(), createBlock(Teleport, { to: ".page-job-wrapper" }, [
                createVNode(_sfc_main$c)
              ]))
            ], 64);
          };
        }
      });
      async function mountVue() {
        const jobSearchWrapper = await elmGetter.get(".job-search-wrapper");
        if (document.querySelector("#boos-helper-job")) {
          return;
        }
        const app = createApp(_sfc_main);
        const jobEl = document.createElement("div");
        jobEl.id = "boos-helper-job";
        jobSearchWrapper.insertBefore(jobEl, jobSearchWrapper.firstElementChild);
        jobSearchWrapper.setAttribute("help", "出界了哇!");
        app.mount(jobEl);
      }
      function removeAd() {
        elmGetter.rm(".job-list-wrapper .subscribe-weixin-wrapper");
        elmGetter.rm(".job-side-wrapper");
        elmGetter.rm(".side-bar-box");
        elmGetter.rm(".go-login-btn");
      }
      async function run() {
        logger.info("加载/web/geek/job页面Hook");
        removeAd();
        mountVue();
      }

    })
  };
}));

System.import("./__entry.js", "./");