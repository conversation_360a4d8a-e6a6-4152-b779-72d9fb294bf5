import requests
import rpa as r
import datetime
import elastic_search.esUtil as esUtil
from bs4 import BeautifulSoup
import os
import shutil
from qbittorrentapi import Client

class QBitTorrentHelper:
    api_url = 'http://jim-nas.internal.site:8085'
    username = 'admin'
    password = '19950102'
    client = Client(host='http://jim-nas.internal.site:8085',username=username, password=password)


    def addJob(self, torrent):
        save_path = '/volume2/MyHDD/rss-game'
        result = self.client.torrents_add(urls=torrent, save_path=save_path, category="game")

    def getInfo(self):
        result = self.client.torrents_info()
        return result

    def changeDownloadPathToMonth(self, path="/volume2/MyHDD/rss-game-m/"):
        today = datetime.date.today().strftime("%Y-%m") + "/"
        new_location = os.path.join(path, today)
        cat = self.client.torrent_categories
        cat.edit_category('game', save_path=new_location)

    def changePathToMonth(self, task_hash, file_name, path="/volume2/MyHDD/rss-game-m2/", path2 = "Volumes/MyHDD/rss-game-m2"):
        today = datetime.date.today().strftime("%Y-%m") + "/"
        new_location = os.path.join(path, today)
        new_location2 = os.path.join(path2, today)

        smbpath = 'smb://************/MySSD'
        os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
        smbpath = 'smb://************/MyHDD'
        os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
        os.makedirs(new_location2, exist_ok=True)
        try:
            self.client.torrents_set_location(torrent_hashes=task_hash, location=new_location)
        except Exception as e:
            print(e)

def rename_subfolders(folder_path, suffix, new_suffix):
    # 获取指定文件夹下的所有子文件夹
    subfolders = [f.path for f in os.scandir(folder_path) if f.is_dir()]

    for subfolder in subfolders:
        # 检查子文件夹名称是否以指定的后缀结尾
        if subfolder.endswith(suffix):
            # 构建新的子文件夹名称
            new_subfolder_name = subfolder[:-len(suffix)] + new_suffix
            new_subfolder_path = os.path.join(folder_path, new_subfolder_name)

            # 重命名子文件夹
            os.rename(subfolder, new_subfolder_path)
def move_files_to_parent_folder(folder_path):
    # 获取指定文件夹下的所有子文件夹
    subfolders = [f.path for f in os.scandir(folder_path) if f.is_dir()]

    for subfolder in subfolders:
        # 遍历子文件夹中的所有文件
        for file_name in os.listdir(subfolder):
            file_path = os.path.join(subfolder, file_name)
            # 移动文件到父文件夹中
            shutil.move(file_path, folder_path)

        # 删除子文件夹
        os.rmdir(subfolder)


if __name__ == '__main__':
    # torrent = "magnet:?xt=urn:btih:bd66760caafc012865a5e8a35ad8e1d81bdefdcf&dn=White.Night.Dream.rar&tr=udp://tracker.torrent.eu.org:451/announce&tr=udp://exodus.desync.com:6969/announce&tr=udp://tracker.moeking.me:6969/announce&tr=udp://tracker.opentrackr.org:1337/announce&tr=udp://open.stealth.si:80/announce&tr=udp://tracker.theoks.net:6969/announce&tr=udp://movies.zsw.ca:6969/announce&tr=udp://tracker.tiny-vps.com:6969/announce&tr=udp://tracker-udp.gbitt.info:80/announce&tr=http://tracker.gbitt.info:80/announce&tr=https://tracker.gbitt.info:443/announce&tr=http://tracker.ccp.ovh:6969/announce&tr=udp://tracker.ccp.ovh:6969/announce&tr=udp://tracker.dler.com:6969/announce&tr=http://tracker.bt4g.com:2095/announce"
    q = QBitTorrentHelper()
    # result = q.getInfo()
    # torrent_list = result.data
    # torrent_list = list(filter(lambda x: x.get("category") == 'game' and x.get("progress") == 1, torrent_list))
    # for tor in torrent_list:
    #     q.changePathToMonth(tor.get("hash"), tor.get("name"))
    # q.addJob(torrent)
    q.changeDownloadPathToMonth()
    # smbpath = 'smb://************/MySSD'
    # os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
    # smbpath = 'smb://************/MyHDD'
    # os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
    # rename_subfolders('/Volumes/MyHDD/rss-game-m/2024-04/',".rar","")
    # move_files_to_parent_folder('/Volumes/MyHDD/rss-game-m/2024-04/')
