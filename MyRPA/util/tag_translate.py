import requests
import rpa as r
import datetime
import elastic_search.esUtil as esUtil
from bs4 import BeautifulSoup
from qbittorrentapi import Client
from translate import Translator
from MyRPA.util.MongoDBHelper import MongoDBHelper

class TagTranslator:
    translator = Translator(to_lang="zh", email="<EMAIL>")
    helper = MongoDBHelper()
    def translate_text(self, text):
        if text == None or len(text) == 0:
            return
        translation = self.translator.translate(text)
        return translation

    def getTranslation(self, text):
        if text is None:
            return ""
        text = text.strip()
        tag = self.helper.my_find_data('my_game_tag', {'tag': text,})
        if tag is not None and len(tag) > 0:
            return tag[0]["trans"]

        trans = self.translate_text(text)
        self.helper.my_insert_data('my_game_tag', {'tag': text, 'trans': trans})
        return trans


if __name__ == '__main__':
    tag = TagTranslator()
    texts = 'Simulation, RPG, Visual Novel, Anime, Management, Strategy,RPG'.split(",")
    for text in texts:
        trans = tag.getTranslation(text)
        print(trans)
