import requests
import rpa as r
import datetime
import elastic_search.esUtil as esUtil
from bs4 import BeautifulSoup
from qbittorrentapi import Client
from wordpress import api, API
import json
from wordpressHelper.nginx__file_struct import NginxProcess
from urllib.parse import urlparse


class WordPressUtil2:
    # wpapi = API(
    #     url="http://192.168.0.33:6980",
    #     consumer_key='',
    #     consumer_secret='',
    #     api="wp-json",
    #     version='wp/v2',
    #     wp_user="jiml",
    #     wp_pass="Litiantong1995;",
    #     basic_auth=True,
    #     user_auth=True,
    # )

    wpapi = API(
        url="http://jim-nas.internal.site:6980/",
        consumer_key='',
        consumer_secret='',
        api="wp-json",
        version='wp/v2',
        wp_user="jiml",
        wp_pass="19950102",
        basic_auth=True,
        user_auth=True,
    )
    n = NginxProcess()

    def write_article(self, title, content, cats=[], tags=[]):
        category_ids = []
        for category in cats:
            category_id = self.get_or_create_category(category)
            if category_id:
                category_ids.append(category_id)

        # 获取或创建标签的 ID
        tag_ids = []
        for tag in tags:
            tag_id = self.get_or_create_tag(tag)
            if tag_id:
                tag_ids.append(tag_id)
        return {
            "title": title,
            "content": content,
            # "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # "author": 4,
            "format": "standard",
            "status": "publish",
            "tags": tag_ids,
            "categories": category_ids
        }

    def get_or_create_category(self, category_name):
        # 检查分类是否已存在
        categories = self.wpapi.get('categories', params={'search': category_name}).json()
        if categories:
            return categories[0]['id']

        # 创建新的分类
        new_category = {
            'name': category_name
        }
        response = self.wpapi.post('categories', data=new_category)
        if response.status_code == 201:
            print(f"新分类 '{category_name}' 创建成功！")
            return response.json()['id']
        else:
            print(f"新分类 '{category_name}' 创建失败。")
            return None

    # 获取或创建标签的 ID
    def get_or_create_tag(self, tag_name):
        # 检查标签是否已存在
        tags = self.wpapi.get('tags', params={'search': tag_name}).json()
        if tags:
            return tags[0]['id']

        # 创建新的标签
        new_tag = {
            'name': tag_name
        }
        response = self.wpapi.post('tags', data=new_tag)
        if response.status_code == 201:
            print(f"新标签 '{tag_name}' 创建成功！")
            return response.json()['id']
        else:
            print(f"新标签 '{tag_name}' 创建失败。")
            return None

    def read(self):
        post = self.wpapi.get("posts")
        j = json.loads(post.text)
        # print(j)

    def post(self, article):
        if article is None:
            return
        res = self.wpapi.post("posts", article)
        return json.loads(res.content).get("id")

    def new_media(self, img_url):
        img_filename = img_url.split("/")[-1]

        response = requests.get(img_url)

        files = {'file': response.content}
        headers = {'Content-Disposition': 'attachment; filename="file.jpg"'}
        response = self.wpapi.post("media",data={},files=files, headers={
            "Content-Disposition": f'attachment; filename="{img_filename}"',
            'Content-Type': 'image/jpeg'
        })

        print(response)

    def sync_wordpress_article(self, url, process_full=False, cats=[]):
        n = NginxProcess()
        a = n.loadFolders(url, not process_full)
        if not process_full:
            a = list(filter(lambda s: s["isCurrentDay"] is not False, a))
        i=0
        for aa in a:
            re = n.processFolder(aa["href"], aa["content"])
            cats2 = []
            cats2.extend(cats)
            cats2.extend(self.add_cat_by_keyword(re["title"]))
            body = w.write_article(re["img_urls"], re["title"], cats=cats2)
            w.post(body)
            print(f"Done:{i}/ {re['title']}")
            i +=1



if __name__ == '__main__':
    w = WordPressUtil2()
    body = w.write_article("Title22", "Content22",tags=["myTag1","myTag2"],cats=["myCat1","myCat2"])
    w.post(body)

    # a = n.loadImageUrls("http://192.168.0.33:2345/Photos/girldreamy/An%20Ko%20%E6%9D%8F%E5%AD%90%20Vol.003%20%E5%86%85%E8%B4%AD%E6%97%A0%E6%B0%B4%E5%8D%B0%20%E4%BE%8B%E8%A1%8C%E4%BD%93%E6%A3%80%20A/")
