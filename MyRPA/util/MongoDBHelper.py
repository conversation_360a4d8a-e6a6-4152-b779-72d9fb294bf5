from pymongo import MongoClient
import re

class MongoDBHelper:
    def __init__(self, host='localhost', port=27017, db_name='mydatabase'):
        self.host = host
        self.port = port
        self.db_name = db_name
        self.client = None
        self.db = None

    def connect(self):
        self.client = MongoClient(host=self.host, port=self.port)
        self.db = self.client[self.db_name]

    def insert_data(self, collection_name, data):
        collection = self.db[collection_name]
        collection.insert_one(data)

    def find_data(self, collection_name, query):
        collection = self.db[collection_name]
        result = collection.find(query)
        return result

    def count_data(self, collection_name, query):
        collection = self.db[collection_name]
        count = collection.count_documents(query)
        return count

    def find_regex(self, collection_name, query):
        collection = self.db[collection_name]
        query = {"name": {"$regex": re.compile(query, re.IGNORECASE)}}
        cursor = collection.find(query)
        return cursor

    def update_data(self, collection_name, query, update_data):
        collection = self.db[collection_name]
        collection.update_many(query, {'$set': update_data})
        print("Data updated successfully.")

    def delete_data(self, collection_name, query):
        collection = self.db[collection_name]
        collection.delete_many(query)
        print("Data deleted successfully.")

    def close(self):
        self.client.close()

    def my_insert_data(self,collection_name, data):
        self.connect()
        self.insert_data(collection_name, data)
        self.close()

    async def save_to_mongo(self, data):
        """保存数据到 MongoDB"""
        await self.client.my_collection.insert_one(data)  # 替换为你的集合名称

    def my_update_data(self, collection_name, query, update_data):
        self.connect()
        collection = self.db[collection_name]
        collection.update_many(query, {'$set': update_data})
        self.close()

    def my_find_data(self, collection_name, data):
        self.connect()
        ret = self.find_data(collection_name, data)
        if ret is not None:
            ret = list(ret)
        self.close()
        return ret
