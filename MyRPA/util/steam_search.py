import requests
from fuzzywuzzy import fuzz

class SteamSearchUtil:

    # 替换为您自己的Steam Web API密钥
    api_key = 'EBB866779FB3DEFC35B563380C53B960'
    game_data = []

    def initData(self):

        # 创建API请求的URL
        url = f'http://api.steampowered.com/ISteamApps/GetAppList/v0002/?key={self.api_key}'

        try:
            # 发送API请求并获取响应数据
            response = requests.get(url)
            data = response.json()

            # 获取游戏列表
            app_list = data['applist']['apps']
            self.game_data = app_list
        except requests.exceptions.RequestException as e:
            # 请求发生错误
            print(f"请求发生错误: {e}")


    def search_steam_game(self,game_name,recursive=True):

        if len(self.game_data)==0:
            self.initData()

        if game_name is None:
            return
        if game_name.rfind('-') != -1:
            game_name = game_name[:game_name.rfind('-')]
        if game_name.rfind(' v') != -1:
            game_name = game_name[:game_name.rfind(' v')]

        # 在游戏列表中搜索匹配的游戏

        highest_similarity = -1
        best_match = None
        for app in self.game_data:
            if app['name'] not in game_name:
                continue
            similarity = fuzz.ratio(game_name.lower(), app['name'].lower())
            if similarity > highest_similarity:
                best_match = app
                highest_similarity = similarity

        if highest_similarity < 70:
            # if game_name.rfind(' ') != -1 and recursive:
            #     game_name = game_name[:game_name.rfind(' ')]
            #     return self.search_steam_game(game_name, False)
            return
        print(f"游戏名称: {best_match['name']}")
        print(f"App ID: {best_match['appid']}")

        return self.get_game_details(best_match['appid'])


    def get_game_details(self, app_id):

        # 创建API请求的URL
        url = f'http://store.steampowered.com/api/appdetails?appids={app_id}&key={self.api_key}&cc=cn&l=chinese'
        url = f'http://store.steampowered.com/api/appdetails?appids={app_id}'

        try:
            # 发送API请求并获取响应数据
            response = requests.get(url,headers={
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"  # 指定所需的语言
            })
            data = response.json()

            # 提取游戏详情
            game_data = data[str(app_id)]
            if game_data['success']:
                game_details = game_data['data']
                min_req = None
                recm_req = None
                try:
                    min_req = game_details.get('pc_requirements',{}).get('minimum')
                    recm_req = game_details.get('pc_requirements',{}).get('recommended')
                except:
                    pass
                return {
                    "steam_name": game_details.get("name"),
                    "steam_appid": game_details.get("steam_appid"),
                    'steam_detailed_description': game_details.get('detailed_description'),
                    'steam_supported_languages': game_details.get('supported_languages'),
                    'steam_header_image': game_details.get('header_image'),
                    'steam_pc_requirements_minimum': min_req,
                    'steam_pc_requirements_recommended': recm_req,
                    'steam_developers': ", ".join(game_details.get('developers',[])),
                    'steam_publishers': ", ".join(game_details.get('publishers',[])),
                    'steam_price_overview': game_details.get('price_overview'),
                    'steam_screenshots': list(map(lambda x: x.get("path_full"),game_details.get('screenshots',[]))), # url list
                    'steam_movies': list(map(lambda x: x.get("mp4", {}).get("max"),game_details.get('movies',[]))), # url list
                    'steam_genres': list(map(lambda x: x.get("description"),game_details.get('genres',[]))), # string list,
                    'steam_categories': list(map(lambda x: x.get("description"),game_details.get('categories',[]))), # string list, dont use this
                    'steam_background': game_details.get('background'), # string list, dont use this
                }
            else:
                print("未找到游戏详情")

        except Exception as e:
            # 请求发生错误
            print(f"请求发生错误: {e}")

if __name__ == '__main__':
    # 调用函数进行游戏搜索
    # search_steam_game('Parry Nightmare')
    s = SteamSearchUtil()
    info = s.search_steam_game("Pepper Grinder")
    print(info)
    s.search_steam_game("Rebirth of Issac")
    print(info)
    # get_game_details(2076580)