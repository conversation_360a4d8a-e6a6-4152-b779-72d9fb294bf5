import datetime

import rpa as r
from bs4 import BeautifulSoup
import pytz
import requests

from MyRPA.util.MongoDBHelper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from MyRPA.util.qbitTorrentHelper import QBitTorrentHelper
from MyRPA.util.steam_search import SteamSearchUtil

utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)

class DailyGame:

    def init(self):
        pass

    def single_save(self, game_list):
        m = MongoDBHelper()
        m.connect()
        for game in game_list:
            m.insert_data('my_game', game)
        m.close()
        download_game = list(filter(lambda x: x["can_download"], game_list))
        if len(download_game) > 0:
            q = QBitTorrentHelper()
            for game in game_list:
                q.addJob(game["torrent"])


    def batch_save(self, game_list):
        m = MongoDBHelper()
        m.connect()
        for game in game_list:
            m.insert_data('my_game', game)
        m.close()
        download_game = list(filter(lambda x: x["can_download"], game_list))
        if len(download_game) > 0:
            q = QBitTorrentHelper()
            for game in game_list:
                q.addJob(game["torrent"])



    def getData(self, pageNo):
        r.init()
        r.url(f"https://pcgamestorrents.com/page/{pageNo}")
        r.wait(2)
        result = r.read("page")
        soup = BeautifulSoup(result, features="html.parser")
        articles = soup.findAll("article")
        hrefs = list(map(lambda x: x.find("h2").find("a")["href"], list(articles)))
        game_list = []
        outdate = False
        c = MongoDBHelper()

        for href in hrefs:
            db_link = c.my_find_data('my_game', {"link": href})
            if db_link is not None and len(db_link) > 0:
                continue

            r.url(href)
            r.wait()
            name = r.read('//article/h1')
            content = r.read('//article/div[2]/p[5]')
            release_date = r.read('//article/div[2]/p[10]')
            genre = r.read('//article/div[2]/p[11]')
            release_size = r.read('//article/div[2]/p[16]')
            cracked_by = r.read('//article/div[2]/p[15]')
            can_download = self.canDownload(release_size)

            soup2 = BeautifulSoup(r.read('page'), features="html.parser")
            imgs = soup2.findAll("img",{"class": "igg-image-content"})
            img_links = list(map(lambda x: x.attrs["src"], imgs))
            torrent = ''
            r.click('//article/div[2]/p[18]/a')
            r.wait(10)
            r.click('//*[@id="nut"]')
            torrent = r.read('/html/body/input')
            game = {
                'name': name,
                'content': content,
                'release_date': release_date,
                'genre': genre,
                'release_size': release_size,
                'cracked_by': cracked_by,
                'link': href,
                'torrent': torrent,
                'can_download': can_download,
                'process_date': datetime.datetime.now().strftime("%Y-%m-%d"),
                'img_link': img_links
            }
            game_list.append(game)
            self.single_save([game])

        r.close()
        return game_list

    def canDownload(self, size):
        sizeNum = size[13:-2].strip()
        if size.endswith('MB'):
            return True
        if size.endswith('GB'):
            try:
                return float(sizeNum) < 5
            except:
                return False

if __name__ == '__main__':
    d = DailyGame()
    data = []
    for pageNo in range(3):
        data = d.getData(pageNo)
        d.batch_save(data)
