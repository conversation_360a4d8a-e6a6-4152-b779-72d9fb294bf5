import rpa as r
import datetime
import elastic_search.esUtil as esUtil
from bs4 import BeautifulSoup


class RPA:

    def init(self):
        pass

    def bing(self):
        r.init(True)
        r.url('https://www.bing.com')
        text = r.read("page")
        print(text)
        r.close()

    def game(self):
        r.init(True)
        r.url('')
        r.close()


    def read(self):
        now = datetime.datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        r.init(True)
        r.url(
            'https://www.zhipin.com/web/geek/job-recommend?city=101010100&salary=406&experience=105,106,107&industry=100015&scale=303,304,305,306&jobType=1901')
        r.wait()
        count = r.count('//*[@class="job-card-box"]')
        r.click('//*[@id="wrap"]/div[2]/div[1]/div/div[1]/a[3]')
        list = []
        i = 0
        while r.present(f'//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[{i + 1}]'):
            r.click(f'//*[@id="wrap"]/div[2]/div[2]/div/div/div[1]/ul/li[{i + 1}]')
            i += 1
            if i > 50:
                break
            r.wait(2)
            job_name = r.read('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[1]/div[1]/div/span[1]')
            sub_title = r.read('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[1]/div[1]/ul')
            job_desc = r.read('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/p')
            job_desc = job_desc[job_desc.rfind('}') + 1:]
            author = r.read('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/div/h2')
            company = r.read('//*[@id="wrap"]/div[2]/div[2]/div/div/div[2]/div/div[2]/div[1]/div[2]')

            soup = BeautifulSoup(r.read("page"), features="html.parser")
            link = soup.find("a", {"class": "more-job-btn"})
            job_link = link['href']

            if esUtil.exists('search-job-zh', 'job_link', job_link):
                continue

            item = {
                "_index": 'search-job-zh',
                "_source": {
                    "job_name": job_name,
                    "sub_title": sub_title,
                    "date": now,
                    "author": author,
                    "company": company,
                    "job_link": job_link,
                    "job_desc": job_desc
                }
            }
            print(item)
            esUtil.write_es_single(item["_source"])
            list.append(item)
        r.close()

