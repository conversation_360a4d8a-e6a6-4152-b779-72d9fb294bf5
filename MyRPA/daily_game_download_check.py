import datetime

import rpa as r
from bs4 import BeautifulSoup
import pytz
import requests

from MyRPA.util.MongoDBHelper import MongoDBHelper
from MyRPA.util.qbitTorrentHelper import QBitTorrentHelper

utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)

class DailyGameChecker:

    def run(self):
        q = QBitTorrentHelper()
        result = q.getInfo()
        torrent_list = result.data
        torrent_list = list(filter(lambda x: x.get("category")=='game', torrent_list))
        torrent_dict = {}
        for item in torrent_list:
            torrent_dict[item['magnet_uri'].split("&tr=udp")[0]] = item

        helper = MongoDBHelper()
        game_list = helper.my_find_data('my_game', {
            'torrent': {'$ne': ''},
            'position': None
        })
        for game in game_list:
            magnet_uri = game["torrent"].split("&tr=udp")[0]
            if magnet_uri in torrent_dict:
                data = torrent_dict.get(magnet_uri)
                helper.my_update_data('my_game', {"_id": game.get("_id")}, {"position": data.get("content_path")})
                print(f"update position of {game.get('name')}: {data.get('content_path')}")


if __name__ == '__main__':
    d = DailyGameChecker()
    d.run()

