# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

import rpa as r
from bs4 import BeautifulSoup
import os
import datetime
import random


now = datetime.datetime.now().strftime('%Y-%m-%d')


def read_eh():
    # r.keyboard('[ctrl][shift]n')
    # r.clipboard('e-hentai.org/popular')
    # r.keyboard('[ctrl]v')
    r.url('http://e-hentai.org/popular')
    r.wait(5)
    page = BeautifulSoup(r.read('page'))
    page_list_dom = page.find_all('tr')
    for dom in page_list_dom:
        print(dom)


def snap(shot, name):
    if shot:
        r.snap('page', name)


def login():
    r.url('http://web.sanguosha.com/login/index.html')
    r.wait()

    if r.present(r'/html/body/div[2]/div/div[3]/div[1]/div[1]/div/div[2]/input'):
        r.type(element_identifier='/html/body/div[2]/div/div[3]/div[1]/div[1]/div/div[2]/input',
               text_to_type='laoshupi1995')
        r.type(element_identifier='/html/body/div[2]/div/div[3]/div[1]/div[1]/div/div[3]/input',
               text_to_type='Litiantong1995;')
        if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\term.png'):
            r.click('/html/body/div[2]/div/div[3]/div[1]/div[1]/div/div[6]/input')
        r.click('/html/body/div[2]/div/div[3]/div[1]/div[1]/div/div[5]/a')
        r.wait(1)
    r.wait()
    r.click('//*[@id="norWebSer"]/div[1]/img')
    r.click('//*[@id="newGoInGame"]')


def wait_util_present(img_url, timeout, img_url2 = None):
    timer = 0
    while (r.present(img_url) is False and r.present(img_url2) is False) and timer <= timeout:
        r.wait(5)
        timer += 5
    return


def game(shot):
    # login()
    login()
    r.keyboard('[f11]')
    wait_util_present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry.png', 120, r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\main_tip.png')
    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\main_tip.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\main_tip.png')
    while r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png')
    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry.png')
    else:
        send_log('Cant find entry!', shot)
        return
    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png')
    while r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_3.png')
    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_4.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_4.png')
    else:
        send_log('Cant find sub-entry!', shot)
        return

    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_5.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_5.png')
    else:
        send_log('Cant find sub-entry_2!', shot)
        return


    if r.present(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_6.png'):
        r.click(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\entry_6.png')
    else:
        send_log('Cant find GET button!', shot)
        return


def send_log(str, shot):
    snap(shot, 'error_' + now + '.png')
    msg = r.telegram(-1001497463795, str)



# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    r.init(visual_automation = True, chrome_browser = True)
    # r.wait(int(300 * random.random()))
    game(True)
    # text = r.read(r'C:\Users\<USER>\PycharmProjects\pythonProject\ocr\sgs\test.png')
    r.close()
