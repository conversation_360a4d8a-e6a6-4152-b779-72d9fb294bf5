from PyPDF2 import PdfReader, PdfWriter

def reverse_page_order(input_path, output_path):
    pdf_reader = PdfReader(input_path)
    pdf_writer = PdfWriter()

    num_pages = len(pdf_reader.pages)
    for i in range(num_pages - 1, -1, -1):
        page = pdf_reader.pages[i]
        pdf_writer.add_page(page)

    with open(output_path, 'wb') as output_file:
        pdf_writer.write(output_file)



if __name__ == '__main__':
    # 示例用法
    input_file = '/Volumes/MySSD/calibre-web/books/HMJM/羽生ありさ写真集 Melons (24)/羽生ありさ写真集 Melons - HMJM.pdf'
    output_file = '/Volumes/MySSD/calibre-web/books/HMJM/羽生ありさ写真集 Melons (24)/reversed_book.pdf'
    reverse_page_order(input_file, output_file)