import rpa as r
from bs4 import BeautifulSoup
import os
import datetime
import random
import glob
import json


now = datetime.datetime.now().strftime('%Y-%m-%d')



def main(url, cache_dir):
    his_urls = load_his_list(cache_dir)
    r.url(url)
    r.wait()
    failed_time = 0
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    main_content = soup.find('table', {"class": "gltc"})
    while main_content is None and failed_time < 5:
        r.url(url)
        r.wait()
        result = r.read("page")
        soup = BeautifulSoup(result, features="html.parser")
        main_content = soup.find('table', {"class": "gltc"})
        failed_time+=1
    if failed_time == 5:
        return
    recur(url, his_urls, cache_dir, 0)


def recur(url, his_urls, cache_dir, err):
    if err > 2:
        return
    r.url(url)
    r.wait()
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    main_content = soup.find('table', {"class": "gltc"})
    data = main_content.find_all('tr')
    data.pop(0)

    try:
        cates = list(map(lambda x: x.find('td', {"class": "glcat"}).getText(), data))
        titles = list(map(lambda x: x.find('div', {"class": "glink"}).getText(), data))
        hrefs = list(map(lambda x: x.find('td', {"class": "glname"}).find('a')['href'], data))
        # isEnd = has_end(his_urls, hrefs)
        tags = list(map(lambda x: list(map(lambda y: y.getText(), x.find_all('div', {"class": "gt"}))), data))
        merge = list(zip(cates, titles, tags, hrefs))
        # record(hrefs, cache_dir)
        merge = filter(merge)
        add_favourite(merge)
    except:
        recur(url, his_urls, cache_dir, err+1)



def load_his_list(cache_dir):
    list_of_files = glob.glob(cache_dir + '\\*')
    latest_file = max(list_of_files, key=os.path.getctime)
    with open(latest_file, 'r', encoding="utf-8") as log:
        urls = log.readlines()
    return urls

def record(urls, dir_path):
    with open(dir_path + '\\' + now + ".log", 'a+') as log:
        for link in urls:
            log.write(link)
            log.write("\n")



def add_favourite(merge):
    for item in merge:
        r.url(item[3])
        r.wait()
        if r.present(rpa_photo_path + 'warn_flag.png'):
            r.click(rpa_photo_path + 'warn_flag.png')
            r.wait()
        if r.present('//*[@id="favoritelink"]'):
            r.click('//*[@id="favoritelink"]')
            r.click(rpa_photo_path + 'favourite.png')
            r.click(rpa_photo_path + 'add_favourite.png')
        print()


def has_end(his, cur):
    for link in cur:
        if link in his:
            return True
    return False

def filter(merge):
    ret = list()
    for item in merge:
        if item[0] == 'Cosplay' or 'chinese' in item[2]:
            ret.append(item)
    return ret



# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    r.init(visual_automation = True, chrome_browser = True)
    main('https://e-hentai.org/watched', r'C:\Users\<USER>\Desktop\autorun\rpa_eh\check_list\watch')
    main('https://e-hentai.org/popular', r'C:\Users\<USER>\Desktop\autorun\rpa_eh\check_list\popular')

    r.close()
