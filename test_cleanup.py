#!/usr/bin/env python3
"""
测试清理和重新下载功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tg_img_download import cleanup_empty_folders_and_invalid_records

def test_cleanup():
    """
    测试清理功能
    """
    print("开始测试清理功能...")
    try:
        cleanup_empty_folders_and_invalid_records()
        print("清理功能测试完成")
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_cleanup()
