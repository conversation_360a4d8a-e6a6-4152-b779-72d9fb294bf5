import random
import sys
import time

from telethon import TelegramClient, events, sync

# These example values won't work. You must get your own api_id and
# api_hash from https://my.telegram.org, under API Development.
from telethon.tl.types import InputMessagesFilterPhotos, InputMessagesFilterUrl
import datetime
import telegram_sender

import pytz

utc = pytz.UTC

api_id = 21786636
api_hash = '792cfdcecaeab811010c87c98c200ec7'

yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday - datetime.timedelta(hours=8)
yesterday = utc.localize(yesterday)


def startClient():
    client = TelegramClient('session_name', api_id, api_hash)
    client.start()
    return client


def endClient(client):
    client.disconnect()


def getChatUrl(client, groupId, limit=30):
    msg = client.get_messages(groupId, limit=limit, filter=InputMessagesFilterUrl)
    ret = list()
    for m in msg:
        if hasattr(m, 'web_preview') and m.web_preview is not None:
            if m.date is not None and m.date > yesterday:
                ret.append((m.web_preview.title, m.web_preview.url))
    return ret


def batch_get_url(my_group_ids):
    client = startClient()
    ret = ""
    try:
        for groupId in my_group_ids:
            msg_list = getChatUrl(client, groupId)
            time.sleep(random.randint(1,10))
            for _, url in msg_list:
                ret = ret + url + "\n"
    finally:
        endClient(client)
    return ret


if __name__ == '__main__':

    now = datetime.datetime.now().strftime('%Y-%m-%d')
    list = batch_get_url(['MarioBase'])
