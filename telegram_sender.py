
import telegram

BOT_TOKEN = '**********************************************'
CHAT_ID = '-1001497463795'

bot = telegram.Bot(token=BOT_TOKEN)
def send_img(img_path, chat_id):
    # IMAGE_PATH = r'C:\Users\<USER>\Desktop\autorun\rpa_photo\auto_download.png'

    with open(img_path, 'rb') as f:
        bot.send_photo(chat_id=chat_id, photo=f)

def send_img2(url, chatId):
    # IMAGE_PATH = r'C:\Users\<USER>\Desktop\autorun\rpa_photo\auto_download.png'
    bot.send_photo(chat_id=chatId, photo=url)

def send_txt(txt, chatId):
    bot.send_message(chat_id=chatId, text=txt)

# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    # send_img2("https://imagecache.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/951abe5d-d67e-4c79-03d2-8eec269bfa00/width=512/276933", CHAT_ID)
    # send_img2('https://imagecache.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/7727c1c6-8577-475a-55fd-f502b01f5000/width=1024/01115-2051334866-best%20quality,%20ultra%20high%20res,%20(photorealistic_1.4),%201%20girl,%20bikini,%20(ulzzang-6500_1.0),%20%20(style-keta_0.8).png', CHAT_ID )
    # IMAGE_PATH = r'C:\Users\<USER>\Desktop\code\stable-diffusion-webui\outputs\txt2img-images\00308-907374736-(best quality_1.8),(high quality_1.8),((ultra-detailed)),ultra high res,(masterpiece_1.8),finely detail,highres,8k wallpaper,8k,.png'
    # send_img(IMAGE_PATH, "-865719971")
    send_txt("test", "-865719971")
