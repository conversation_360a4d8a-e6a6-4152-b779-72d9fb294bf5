import datetime
import os
import shutil
import subprocess
import time


def copy_file(source_folder, destination_folder):
    # 使用os.path.basename()获取源文件夹的名称
    folder_name = os.path.basename(source_folder)
    # 使用os.path.join()拼接目标文件夹路径和源文件夹名称
    destination_path = os.path.join(destination_folder, folder_name)
    time.sleep(1)
    subprocess.run(['rsync', '-av', '--progress', source_folder + '/', destination_path])
    # shutil.copytree(source_folder, destination_path, dirs_exist_ok=True)  # 移动文件

def copy_missing_folders(source_folder, destination_folder):
    # 获取源文件夹和目标文件夹中的文件夹列表
    source_folders = [folder for folder in os.listdir(source_folder) if os.path.isdir(os.path.join(source_folder, folder))]
    destination_folders = [folder for folder in os.listdir(destination_folder) if os.path.isdir(os.path.join(destination_folder, folder))]
    # 过滤掉目标文件夹中已经存在的文件夹
    folders_to_move = [folder for folder in source_folders if folder not in destination_folders]
    # 遍历剩余的文件夹，将它们移动到目标文件夹
    for folder in folders_to_move:
        print(f"start folder:{folder},src:{source_folder}")
        source_path = os.path.join(source_folder, folder)
        destination_path = os.path.join(destination_folder, folder)
        subprocess.run(['rsync', '-av', '--progress', source_path + '/', destination_path])
        time.sleep(1)
        # shutil.copytree(source_path, destination_path, dirs_exist_ok=True)

def copy_folders_by_date(src_f, des_f):
    prevD = datetime.datetime.now()
    src_f = os.path.join(src_f, prevD.strftime("%Y-%m"))
    des_f = os.path.join(des_f, prevD.strftime("%Y-%m"))
    os.makedirs(des_f, exist_ok=True)
    if not os.path.isdir(src_f):
        return
    copy_missing_folders(src_f, des_f)


def copy_missing_files(source_folder, destination_folder):
    # 获取源文件夹和目标文件夹中的文件列表
    source_files = [file for file in os.listdir(source_folder) if os.path.isfile(os.path.join(source_folder, file))]
    destination_files = [file for file in os.listdir(destination_folder) if os.path.isfile(os.path.join(destination_folder, file))]
    # 过滤掉目标文件夹中已经存在的文件
    files_to_move = [file for file in source_files if file not in destination_files]
    # 遍历剩余的文件，将它们移动到目标文件夹
    for file in files_to_move:
        source_path = os.path.join(source_folder, file)
        destination_path = os.path.join(destination_folder, file)
        subprocess.run(['rsync', '-av', '--progress', source_path + '/', destination_path])
        time.sleep(1)
        # shutil.copyfile(source_path, destination_path)
        print(f"成功将文件从 {source_path} 移动到 {destination_path}")


def mount():
    # smb_host = "************"  # 替换为实际的SMB服务器主机名或IP地址
    # smb_share = "MyHDD"  # 替换为实际的共享文件夹名称
    # smb_user = "jiml"  # 替换为实际的用户名
    # smb_password = "Litiantong1995;"  # 替换为实际的密码
    # directory = "mount"
    # if not os.path.exists(directory): os.makedirs(directory)
    # os.system(f"mount_smbfs //{smb_user}:{smb_password}@{smb_host}/{smb_share} ~/{directory}"

    smbpath = 'smb://*************/MyHDD'
    # photoprism = 'http://admin@************:2343/originals/'
    os.system(f"osascript -e 'mount volume \"{smbpath}\"'")
    # os.system(f"osascript -e 'mount volume \"{photoprism}\"'")
    print("Done")



def debug():
    copy_missing_folders('/Volumes/新加卷/telegra.ex/5star/', '/Volumes/MyHDD/Ehentai/DailyManga/')

def run():
    mount()
    copy_missing_folders('/Volumes/新加卷/telegra.ph/', '/Volumes/MyHDD/TG-Image/')
    copy_missing_folders('/Volumes/新加卷/telegra.ex/everia/', '/Volumes/MyHDD/Photos/everia')
    copy_missing_folders('/Volumes/aigo2/photos/xasiat/', '/Volumes/MyHDD/Photos/xasiat')
    copy_missing_folders('/Volumes/aigo2/photos/girldreamy/', '/Volumes/MyHDD/Photos/girldreamy')

    copy_folders_by_date('/Volumes/aigo2/tg-blog-image/everia', '/Volumes/MyHDD/tg-blog-image/everia')
    copy_folders_by_date('/Volumes/aigo2/tg-blog-image/reprintbooks', '/Volumes/MyHDD/tg-blog-image/reprintbooks')
    copy_folders_by_date('/Volumes/aigo2/tg-blog-image/xasiat', '/Volumes/MyHDD/tg-blog-image/xasiat')
    copy_folders_by_date('/Volumes/新加卷/tg-image/', '/Volumes/MyHDD/tg-image/')

    copy_missing_folders('/Volumes/新加卷/telegra.ex/video/', '/Volumes/MyHDD/Video/Porn/daily/')


    # copy_missing_folders('/Volumes/新加卷/telegra.ex/cosplay/', '/Volumes/MyHDD/Ehentai/DailyCosplay/')
    # copy_missing_folders('/Volumes/新加卷/telegra.ex/5star/', '/Volumes/MyHDD/Ehentai/DailyManga/')

    # copy_missing_folders('/Volumes/新加卷/Movie/', '/Volumes/MyHDD/Video/Movie/')
    # copy_missing_files('/Volumes/aigo2/tele-fa-video/收藏2/his/', '/Volumes/MyHDD/Video/Porn/fa/')


if __name__ == '__main__':
     run()