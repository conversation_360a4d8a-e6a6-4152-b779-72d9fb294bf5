import os
import shutil
import zipfile


def delete_folder(folder_path):
    if os.path.exists(folder_path):
        for root, dirs, files in os.walk(folder_path, topdown=False):
            for file_name in files:
                file_path = os.path.join(root, file_name)
                os.remove(file_path)
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                os.rmdir(dir_path)
        os.rmdir(folder_path)
        print(f"Deleted folder: {folder_path}")
    else:
        print(f"Folder does not exist: {folder_path}")
def unzip_file(file_path,target_path):
    folder_name = os.path.splitext(file_path)[0].split('/')[-1]  # 获取压缩包同名文件夹名
    folder = target_path  #
    if os.path.exists(folder + folder_name):
        print(f"skip file: {file_path}")
        return
    try:
        os.makedirs(folder + folder_name, exist_ok=True)  # 创建同名文件夹

        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            zip_ref.extractall(folder + folder_name)  # 解压文件到同名文件夹

        print(f"解压成功：{file_path}")
    except Exception as e:
        print(f"解压失败：{file_path}，错误信息：{str(e)}")
        delete_folder(folder + folder_name)


def unzip_folder(file_path, target_path):

    # 遍历下载文件夹中的文件，解压压缩包
    download_folder = file_path  # 替换为你的下载文件夹路径
    for file_name in os.listdir(download_folder):
        file_path = os.path.join(download_folder, file_name)
        if os.path.isfile(file_path) and file_name.lower().endswith(".zip"):  # 只处理ZIP文件
            unzip_file(file_path,target_path)

if __name__ == '__main__':
    unzip_folder("/Volumes/新加卷/qtorrent-rss/cosplay", "/Volumes/新加卷/qtorrent-rss/cosplay-view/")
    unzip_folder('/Volumes/新加卷/qtorrent-rss/eh-favourite', "/Volumes/新加卷/qtorrent-rss/eh-favourite-view/")

