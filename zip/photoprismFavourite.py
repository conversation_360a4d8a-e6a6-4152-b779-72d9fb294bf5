import requests
import json
import photoprism

# PhotoPrism服务器的URL和API密钥
server_url = 'http://************:2343'
api_key = 'your-api-key'

# 关键字和新相册名称
keyword = 'your-keyword'
new_album_name = 'your-new-album-name'


headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'zh',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Cookie': '_SSID=AEdBrpBQAhmB6Yni15HP4ey_t8ehj4Nvd_hf1Yyj6j8; stay_login=1; did=xMYhXwUFeIyaBNN_aAfv29tJ0RjXN7RN4JWwVKOGf3HL1ivdeQYbupYNVCUHprNuXfhMxRbtO0DZzUo-HVZfCA; _CrPoSt=cHJvdG9jb2w9aHR0cDo7IHBvcnQ9NTAwMDsgcGF0aG5hbWU9Lzs%3D; id=V0t7fgux4ytVS3DXLvEDM-AoPNaE6Ve6PVFemAF7unYnE2axElGpkbDj3rv14oEZ_VcaR-4omciM-poubTKw60; io=AAWYmDVjBFALJ7UnAAAF',
    'Host': '************:2343',
    'Pragma': 'no-cache',
    'Referer': 'http://************:2343/library/browse?view=mosaic&order=added&q=momo',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Client-Uri': '/static/build/app.8c1ae646f70298385e8b.js',
    'X-Client-Version': '231021-9abea5b55-Linux-AMD64-Plus',
    'X-Session-Id': '1234',
}
api = photoprism.Client(
    domain='http://************:2343',
    username='admin',
    password='19950102',
    debug=False
)
for k, v in api.session.headers.items():
    headers[k] = v


def search_favourite():
    params = {
        'offset': '0',
        'merged': 'true',
        'country': '',
        'camera': '0',
        'lens': '0',
        'label': '',
        'latlng': '',
        'year': '0',
        'month': '0',
        'color': '',
        'order': 'added',
        'q': '',
        'favorite': 'true'
    }
    items = api.get_photos(1000, **params)

    # # 搜索带有指定关键字的图片
    # search_url = f'{server_url}/api/v1/photos'
    #
    # response = requests.get(search_url, headers=headers, params=params)
    # items = response.json()
    photo_path = set([item['Path'] for item in items])
    return photo_path


def run():
    photo_paths = search_favourite()

    for photo_path in photo_paths:

        query_url = f"{server_url}/api/v1/albums?count=24&offset=0&q={photo_path}&category=&order=favorites&year=&type=folder"
        response = requests.get(query_url, headers=headers)
        items = response.json()
        if len(items) < 1:
            print(f"can't find folder: {photo_path}")
            continue
        folder_id = items[0]['UID']

        confirm_url = f"{server_url}/api/v1/albums/{folder_id}"
        response = requests.get(confirm_url, headers=headers)
        items = response.json()
        if items["Favorite"]:
            continue
        like_url = f"{server_url}/api/v1/albums/{folder_id}/like"
        response = requests.post(like_url, headers=headers)


def getFavouriteFolder():
    pageNo = 200
    validNo = 0
    offset = 0
    init = False
    folder_ids = set()

    while not init or validNo == pageNo:
        search_url = f'{server_url}/api/v1/albums'
        params = {
            'count': str(pageNo),
            'offset': str(offset),
            'merged': 'true',
            'country': '',
            'camera': '0',
            'lens': '0',
            'label': '',
            'latlng': '',
            'year': '0',
            'month': '0',
            'color': '',
            'type': 'folder',
            'order': 'name',
            'q': '',
            'favorite': 'true'
        }
        response = requests.get(search_url, headers=headers, params=params)
        print(response)
        items = response.json()
        validNo = len(items)
        offset += validNo
        init = True
        folder_id = set([item['UID'] for item in items])
        folder_ids = folder_ids.union(folder_id)
    return folder_ids
def run2():
    folder_ids = list(getFavouriteFolder())
    favourite_album_id = 'as3n6cop7q1cu8ep'
    add_to_album_url = f'{server_url}/api/v1/albums/{favourite_album_id}/clone'
    data = {'albums': folder_ids}
    response = requests.post(add_to_album_url, headers=headers, data=json.dumps(data))
    print(response)


if __name__ == '__main__':
    run()
    run2()