import requests
import json

# PhotoPrism服务器的URL和API密钥
server_url = 'http://************:2343'
api_key = 'your-api-key'

# 关键字和新相册名称
keyword = 'your-keyword'
new_album_name = 'your-new-album-name'

headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'zh',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Cookie': '_SSID=AEdBrpBQAhmB6Yni15HP4ey_t8ehj4Nvd_hf1Yyj6j8; stay_login=1; did=xMYhXwUFeIyaBNN_aAfv29tJ0RjXN7RN4JWwVKOGf3HL1ivdeQYbupYNVCUHprNuXfhMxRbtO0DZzUo-HVZfCA; _CrPoSt=cHJvdG9jb2w9aHR0cDo7IHBvcnQ9NTAwMDsgcGF0aG5hbWU9Lzs%3D; id=V0t7fgux4ytVS3DXLvEDM-AoPNaE6Ve6PVFemAF7unYnE2axElGpkbDj3rv14oEZ_VcaR-4omciM-poubTKw60; io=AAWYmDVjBFALJ7UnAAAF',
    'Host': '************:2343',
    'Pragma': 'no-cache',
    'Referer': 'http://************:2343/library/browse?view=mosaic&order=added&q=momo',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Client-Uri': '/static/build/app.8c1ae646f70298385e8b.js',
    'X-Client-Version': '231021-9abea5b55-Linux-AMD64-Plus',
    'X-Session-Id': '054c7d2641ec61240ab4014a3559f2039b31ef7ee04357ef',
}

def createAlbum(new_album_name):
    # 创建相册
    create_album_url = f'{server_url}/api/v1/albums'
    data = {'title': new_album_name}
    response = requests.post(create_album_url, headers=headers, data=json.dumps(data))
    response_json = response.json()
    album_id = response_json['UID']
    return album_id

def search_dir(keyword):
    # 搜索带有指定关键字的图片
    search_url = f'{server_url}/api/v1/photos?count=1080&offset=0&merged=true&country=&camera=0&lens=0&label=&latlng=&year=0&month=0&color=&order=added&q=stacks&photo=true'
    params = {
        # 'count': '1000',
        # 'offset': '0',
        # 'merged': 'true',
        # 'country': '',
        # 'camera': '0',
        # 'lens': '0',
        # 'label': '',
        # 'latlng': '',
        # 'year': '0',
        # 'month': '0',
        # 'color': '',
        # 'order': 'added',
        # 'q': keyword
    }
    response = requests.get(search_url, headers=headers, params=params)
    items = response.json()

    i = 0;
    if len(items) == 0:
        return False
    for item in items:
        photo_id = item['UID']
        # i+=1
        # if i>4:
        #     break
        for file in item['Files']:
            file_id = file['UID']
            create_album_url = f'{server_url}/api/v1/photos/{photo_id}/files/{file_id}/unstack'
            response = requests.post(create_album_url, headers=headers)

    return True

def run():
    response = search_dir(keyword)
    return response








if __name__ == '__main__':
    res = True
    for _ in range(19):
        if not res:
            break
        res = run()