import sys
from datetime import datetime, timedelta
import requests
import json
import logging

import photoprism
import mysql.connector
import MySQLDBHelper


# PhotoPrism服务器的URL和API密钥
server_url = 'http://************:2343'
api_key = 'your-api-key'

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)-8s %(message)s')

# 关键字和新相册名称
keyword = 'your-keyword'
new_album_name = 'your-new-album-name'

headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'zh',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Cookie': '_SSID=AEdBrpBQAhmB6Yni15HP4ey_t8ehj4Nvd_hf1Yyj6j8; stay_login=1; did=xMYhXwUFeIyaBNN_aAfv29tJ0RjXN7RN4JWwVKOGf3HL1ivdeQYbupYNVCUHprNuXfhMxRbtO0DZzUo-HVZfCA; _CrPoSt=cHJvdG9jb2w9aHR0cDo7IHBvcnQ9NTAwMDsgcGF0aG5hbWU9Lzs%3D; id=V0t7fgux4ytVS3DXLvEDM-AoPNaE6Ve6PVFemAF7unYnE2axElGpkbDj3rv14oEZ_VcaR-4omciM-poubTKw60; io=AAWYmDVjBFALJ7UnAAAF',
    'Host': '************:2343',
    'Pragma': 'no-cache',
    'Referer': 'http://************:2343/library/browse?view=mosaic&order=added&q=momo',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Client-Uri': '/static/build/app.8c1ae646f70298385e8b.js',
    'X-Client-Version': '231021-9abea5b55-Linux-AMD64-Plus',
    'X-Session-Id': '1234',
}
api = photoprism.Client(
    domain='http://************:2343',
    username='admin',
    password='19950102',
    debug=False
)
for k, v in api.session.headers.items():
    headers[k] = v


def createAlbum(new_album_name):
    # 创建相册
    create_album_url = f'{server_url}/api/v1/albums'
    data = {'title': new_album_name}
    response = requests.post(create_album_url, headers=headers, data=json.dumps(data))
    response_json = response.json()
    album_id = response_json['UID']
    return album_id

def search_dir(keyword):
    search_url = f'{server_url}/api/v1/albums'
    params = {
        'count': '1000',
        'offset': '0',
        'merged': 'true',
        'country': '',
        'camera': '0',
        'lens': '0',
        'label': '',
        'latlng': '',
        'year': '0',
        'month': '0',
        'color': '',
        'order': 'added',
        'q': keyword
    }
    response = requests.get(search_url, headers=headers, params=params)
    items = response.json()
    dir_ids = [item['UID'] for item in items if item["Type"]=="folder" and item["Category"]==''\
                 # 全部还是只看增量
            # and datetime.now().date() - timedelta(days=3) < datetime.strptime(item["CreatedAt"], "%Y-%m-%dT%H:%M:%SZ").date() \
                 ]
    return dir_ids

def search_photo(keyword, full):
    pageNo = 1000
    validNo = 0
    offset = 0
    init = False
    photo_ids = set()

    # 搜索带有指定关键字的图片
    while not init or validNo == pageNo:
        search_url = f'{server_url}/api/v1/photos'
        params = {
            'count': str(pageNo), 'offset': str(offset), 'merged': 'true', 'country': '', 'camera': '0', 'lens': '0', 'label': '',
            'latlng': '', 'year': '0', 'month': '0', 'color': '', 'order': 'added', 'unsorted': full,
            'q': keyword
        }
        response = requests.get(search_url, headers=headers, params=params)
        items = response.json()
        validNo = len(items)
        offset += validNo
        init = True
        photo_id = set([item['UID'] for item in items])
        photo_ids = photo_ids.union(photo_id)
    return list(photo_ids)


def addCategory(keywords, catgory_name):
    # keywordList = keywords.split(" ")
    # uids = set()
    # for keyword in keywordList:
    #     dir_ids = search_dir(keyword)
    #     uids = uids.union(dir_ids)
    #
    # if len(uids)==0:
    #     return
    #
    # for id in uids:
    #     search_url = f'{server_url}/api/v1/albums/{id}'
    #     response = requests.put(search_url, headers=headers, data=json.dumps({"Category": catgory_name}))
    #     print(response)

    helper = MySQLDBHelper.MySQLDBHelper()
    helper.connect()
    for str in keywords.split(" "):
        update_query = f" call my_procedure_name (%s, %s)"
        res = helper.execute(update_query, (str, catgory_name))
        logging.info(f"{keywords},{catgory_name}:受影响的行数：{res}")

    helper.close()

    # # 建立数据库连接
    # cnx = mysql.connector.connect(
    #     host="************:3307",  # 数据库主机地址
    #     user="photoprism",  # 数据库用户名
    #     password="Litiantong1995;",  # 数据库密码
    #     database="photoprism"  # 数据库名称
    # )
    # # 创建游标对象
    # cursor = cnx.cursor()
    # update_query = f"UPDATE albums SET album_category = '{catgory_name}' WHERE album_type = 'folder' \
    # and album_id in %s"
    # cursor.execute(update_query, list(uids))
    # # 提交更改
    # cnx.commit()
    # # 关闭游标和数据库连接
    # cursor.close()
    # cnx.close()








def run(keyword, new_album_name, unsorted='true'):

    #category process
    addCategory(keyword, new_album_name)

    # photo_ids = search_dir(keyword)
    album_id = createAlbum(new_album_name)
    # # 将图片添加到新相册
    # add_to_album_url = f'{server_url}/api/v1/albums/{album_id}/clone'
    # data = {'albums': photo_ids}
    # response = requests.post(add_to_album_url, headers=headers, data=json.dumps(data))
    #
    # if response.status_code == 200:
    #     print(f'成功将{len(photo_ids)}份影集添加到相册"{new_album_name}"')
    # else:
    #     print(f'将图片添加到相册"{new_album_name}"失败：{response.json()["message"]}')

    photo_ids = search_photo(keyword, unsorted)
    add_to_album_url = f'{server_url}/api/v1/albums/{album_id}/photos'
    if len(photo_ids) == 0:
        logging.info(f'Skip 相册"{new_album_name}"')

        return
    data = {'photos': photo_ids}
    response = requests.post(add_to_album_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        logging.info(f'成功将{len(photo_ids)}张照片添加到相册"{new_album_name}"')






if __name__ == '__main__':

    run('水淼aqua 水淼 aqua','水淼aqua')
    run('金鱼','金鱼')
    run('迷之呆梨','迷之呆梨')
    run('nagisa','nagisa魔物喵')
    run('凉凉子','凉凉子')
    run('雨波 HaneAme','雨波')
    run('习呆呆','习呆呆')
    run('桜桃喵','桜桃喵')
    run('雪琪sama','雪琪sama')
    run('爆机少女喵小吉 喵小吉','喵小吉')
    run('河北彩花','河北彩花')
    run('妲己','妲己')
    run('桜井宁宁','桜井宁宁')
    run('朱可儿','朱可儿')
    run('chunmomo 蠢沫沫','蠢沫沫')
    run('鱼子酱','鱼子酱')
    run('抖娘利世','[中国]抖娘利世')
    run('mimmi','Mimmi')
    run('星之迟迟','星之迟迟')
    run('神楽坂真冬','神楽坂真冬')
    run('秋和柯基','秋和柯基')
    run('二阶堂','二阶堂')
    run('柒柒不可爱','柒柒不可爱')
    run('脸红dearie','脸红dearie')
    run('黏黏团子兔','黏黏团子兔')
    run('就是阿朱啊','就是阿朱啊')
    run('花铃','花铃')
    run('桃暖酱','桃暖酱')

    run('jvid','[中国]JVID')
    run('yituyu','[中国]艺语图')
    run('djawa','[韩国]DJAWA')
    run('xingyan','[中国]星颜社')
    run('photochips','[韩国]Photochips')
    run('met','[欧美]MetArt')
    run('mozzi','[韩国]MozziMozzi')
    run('kimlemon','[韩国]KimLemon')
    run('xiaoyu XiaoYu语画界','[中国]语画界')
    run('森萝财团','[中国]森萝财团')
    run('Girlt果团网','[中国]果团网')
    run('lilynah','[韩国]Lilynah')
    run('glamarchive','[韩国]Glamarchive')
    run('taishu','[日本]Ex_Taishu')
    run('FRIDAY','[日本]FRIDAY')
    run('国模 喏閔 國模','[中国]国模')
    run('bobosocks','[中国]袜啵啵')
    run('youmi','[中国]尤蜜荟')
    run('BoLoLi波萝社','[中国]BoLoLi波萝社')
    run('milkybomb','[韩国]Milkybomb')
    run('umizine','[韩国]Umizine')
    run('moon night snap','[韩国]MoonNightSnap')
    run('metart','[欧美]MetArt')
    run('playboy','[日本]Playboy')
    run('bluecake','[韩国]blue-cake')
    run('pure media','[韩国]PureMedia')
    run('bimilstory','[韩国]Bimilstory')
    run('xiuren 秀人','[中国]秀人')
    run('graphis','[日本]Graphis')
    run('Espacia Korea','[韩国]Espacia Korea')
    run('ArtGravia','[韩国]ArtGravia')
    run('minisuka','[日本]Minisuka')
    run('girlz','[日本]Girlz-High')
    run('fantasy story','[韩国]FantasyStory')
    run('miss touch','[韩国]MissTouch')
    run('loozy','[韩国]Loozy')
    run('flower','[日本]Flower')
    run('sweetbox','[韩国]SweetBox')
    run('cosplay coser','[通用]Cosplay')

    run('folder:*写真集*','[日本]写真')
