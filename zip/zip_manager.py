import zipfile
import os
import shutil
import traceback

def unzipAll(path):
    # 判断路径是否存在
    if not os.path.exists(path):
        print(f'路径 {path} 不存在.')
        return

    # 获取路径中所有zip文件
    zip_files = [f for f in os.listdir(path) if f.endswith('.zip')]
    zip_files = list(filter(lambda x: not x.startswith("._"), zip_files))

    # 遍历所有zip文件
    for zip_file in zip_files:
        # print(f'start process {zip_file}')
        # 获取zip文件的绝对路径
        zip_path = os.path.join(path, zip_file)

        # 获取zip文件名的前缀（不包括扩展名）
        zip_name = os.path.splitext(zip_file)[0]

        # 如果同名文件夹已经存在，则不需要解压缩
        unzip_path = os.path.join(path, zip_name)
        if os.path.exists(unzip_path):
            zip_files = [f for f in os.listdir(unzip_path)]
            if len(zip_files) > 5:
                # print(f'文件夹 {zip_name} 已经存在，无需解压缩.')
                continue
            # else:
                # print(f"处理空文件夹{zip_name}")

        # 创建同名文件夹
        if not os.path.exists(unzip_path):
            os.makedirs(unzip_path)

        try:
            # 打开zip文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 找到zip文件中的第一个文件夹
                first_dir = zip_ref.namelist()[0]

                # 将zip文件中的所有内容解压缩到同名文件夹中
                zip_ref.extractall(unzip_path)

            # 获取解压后的第一个文件夹的绝对路径
            first_dir_path = os.path.join(unzip_path, first_dir)
            if not os.path.isdir(first_dir_path):
                continue
            move_file_to_parent(first_dir_path)
        except:
            # print(traceback.format_exc())
            continue
        print(f"process success {zip_file}")






def move_file_to_parent(first_dir_path):
    for file_name in os.listdir(first_dir_path):  # 遍历文件夹中的所有文件
        src_path = os.path.join(first_dir_path, file_name)  # 文件原路径
        dest_path = os.path.join(os.path.dirname((os.path.dirname(first_dir_path))), file_name)  # 文件目标路径
        shutil.move(src_path, dest_path)  # 移动文件

    os.rmdir(first_dir_path)  # 删除空文件夹


if __name__ == '__main__':
    unzipAll('/Volumes/新加卷/eh')

