from pymongo import MongoClient
import re
import mysql.connector

class MySQLDBHelper:
    def __init__(self, host='************',db_name='photoprism'):
        self.host = host
        self.db_name = db_name
        self.client = None
        self.db = None

    def connect(self):
        # 建立数据库连接
        self.cnx = mysql.connector.connect(
            host=self.host,  # 数据库主机地址
            port=3307,
            user="root",  # 数据库用户名
            password="Litiantong1995;",  # 数据库密码
            database=self.db_name  # 数据库名称
        )
        self.cursor = self.cnx.cursor()

    def execute(self, query, data):
        self.cursor.execute(query, data)
        self.cnx.commit()
        return self.cursor.rowcount

    def select(self, query, data):
        self.cursor.execute(query)
        return self.cursor.fetchall()

    def close(self):
        self.cursor.close()
        self.cnx.close()

if __name__ == '__main__':

    helper = MySQLDBHelper()
    helper.connect()
    update_query = f" %s"
    # helper.execute(update_query, list(""))
    res = helper.select("select * from albums limit 1", [])
    print(res)
    helper.close()
