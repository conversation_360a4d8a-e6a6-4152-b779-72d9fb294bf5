import os


def clean_and_rename_folders(directory):
    """
    递归遍历给定目录，清理并重命名所有子文件夹。
    它将执行以下操作：
    1. 移除文件夹名开头和结尾的空格。
    2. 将文件夹名中的换行符（\n和\r）替换为下划线。

    Args:
        directory (str): 要处理的根目录路径。
    """
    if not os.path.isdir(directory):
        print(f"错误: 路径 '{directory}' 不存在或不是一个目录。")
        return

        # topdown=False 确保先处理子目录，避免因父目录改名而导致路径错误
    for root, dirs, files in os.walk(directory, topdown=False):
        for name in dirs:
            # 1. 首先，替换文件夹名中的换行符
            # 这里将换行符替换为下划线，你也可以改为其他字符，如一个空格 ' '
            new_name = name.replace('\n', '_').replace('\r', '')

            # 2. 然后，移除新的文件名开头和结尾的空格
            new_name = new_name.strip()

            # 如果新的名字与旧的名字不同，则进行重命名
            if new_name != name:
                old_path = os.path.join(root, name)
                new_path = os.path.join(root, new_name)

                print(f"重命名: \n  旧: {old_path}\n  新: {new_path}")

                try:
                    os.rename(old_path, new_path)
                    print("  成功!")
                except Exception as e:
                    print(f"  失败! 错误: {e}")


if __name__ == "__main__":
    # 在这里输入你想要处理的文件夹路径
    # 例如: C:\\Users\\<USER>\\Downloads\\problem_folder
    # 或 /Users/<USER>/Downloads/problem_folder
    folder_path = r"/Volumes/新加卷/tg-image"  # 请将此路径替换为你的实际路径

    clean_and_rename_folders(folder_path)