
import scrapy
import os
from datetime import date

# 获取当天日期
today = date.today()

class ImageSpider(scrapy.Spider):
    name = 'image_spider'
    start_urls = []  # 添加你要爬取的URL列表

    def start_requests(self):
        save_dir = self.save_dir + '/' + today.strftime("%Y-%m")
        urls = self.urls

        os.makedirs(save_dir, exist_ok=True)

        for url,title in urls:
            yield scrapy.Request(url=url, callback=self.parse, meta={'save_dir': save_dir, 'title': title})

    def parse(self, response):
        save_dir = response.meta['save_dir']
        title = response.css('title::text').get() if response.meta['title'] is None else response.meta['title']

        # 创建保存图片的目录
        save_dir = os.path.join(save_dir, title[:100])
        os.makedirs(save_dir, exist_ok=True)

        # 提取图片URL并下载保存
        img_urls = response.css('img::attr(src)').getall()
        # 跟踪图片编号
        img_number = 1
        for img_url in img_urls:
            img_url = response.urljoin(img_url)  # 构建完整的图片URL

            img_filename = img_url.split("/")[-1]
            last_dot_index = img_filename.rfind(".")

            # 提取后缀
            if last_dot_index != -1:
                file_extension = img_filename[last_dot_index:]
            else:
                file_extension = ".jpg"

            # 发送请求并保存图片
            img_filename = f"{img_number:06}{file_extension}"  # 使用字符串格式化设置编号为6位长度
            img_path = os.path.join(save_dir, img_filename)
            request = scrapy.Request(img_url, callback=self.save_image)
            request.meta['img_path'] = img_path
            yield request
            img_number += 1

    def save_image(self, response):
        img_path = response.meta['img_path']
        with open(img_path, 'wb') as f:
            f.write(response.body)
        self.log(f"Saved image: {img_path}")
