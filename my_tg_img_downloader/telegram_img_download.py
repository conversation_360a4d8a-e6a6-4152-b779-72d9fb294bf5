from datetime import datetime

import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urlparse
from mongo.MongoDBHelper import MongoDBHelper
from my_tg_img_downloader import pyrogramMsgUtil
from my_tg_img_downloader.link_fetcher import load_telegram_links

# 在你的独立Python程序中调用Scrapy
from scrapy.crawler import CrawlerProcess
from my_tg_img_downloader.ImageCrawler import ImageSpider
from scrapy.settings import Settings # 导入 Settings 类

# --- Scrapy 配置 ---
# 创建一个 Settings 对象或者直接使用字典
scrapy_settings = Settings()

# 基本设置
scrapy_settings.set('BOT_NAME', 'my_image_downloader') # 设置一个爬虫名称
scrapy_settings.set('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

# 遵循 Robots.txt (根据需要调整)
scrapy_settings.set('ROBOTSTXT_OBEY', False) # 设为 False 则不遵守 robots.txt

# 下载延迟和并发控制 (非常重要，防止被封)
scrapy_settings.set('DOWNLOAD_DELAY', 2)  # 每个请求之间的延迟时间（秒），例如 1, 2, 3
scrapy_settings.set('CONCURRENT_REQUESTS_PER_DOMAIN', 4) # 对单个域名的最大并发请求数，默认为8，可以调小
# scrapy_settings.set('CONCURRENT_REQUESTS_PER_IP', 4)    # 对单个IP的最大并发请求数

# 自动限速 (推荐启用)
scrapy_settings.set('AUTOTHROTTLE_ENABLED', True)
scrapy_settings.set('AUTOTHROTTLE_START_DELAY', 5)     # 初始下载延迟
scrapy_settings.set('AUTOTHROTTLE_MAX_DELAY', 60)    # 最大下载延迟
scrapy_settings.set('AUTOTHROTTLE_TARGET_CONCURRENCY', 1.0) # Scrapy应与远程服务器并行的平均请求数
# scrapy_settings.set('AUTOTHROTTLE_DEBUG', True)     # 设置为True可以查看限速的详细日志


urls = []
process = CrawlerProcess(settings=scrapy_settings)
g_save_dir = '/Volumes/新加卷/tg-image'
def load_page2(url,title, save_dir='/Volumes/新加卷/telegra.ph'):
    urls.append((url,title))

def startScrapy():
    if len(urls) > 0:
        process.crawl(ImageSpider, urls=urls, save_dir=g_save_dir)
        process.start()

def load_page(url,title, save_dir='/Volumes/新加卷/tg-image'):
    # 请求URL并获取网页内容
    response = requests.get(url)
    content = response.text

    # 解析网页内容，提取所有的图片URL
    soup = BeautifulSoup(content, "html.parser")
    img_tags = soup.find_all("img")
    img_urls = [img["src"] for img in img_tags]
    save_dir = save_dir + "/" + title[0:100]
    # 创建保存图片的目录
    os.makedirs(save_dir, exist_ok=True)
    parsed_url = urlparse(url)
    domain = parsed_url.netloc

    counter = 0
    # 遍历图片URL并下载保存
    for img_url in img_urls:
        # 获取完整的图片URL
        if not img_url.startswith("http"):
            img_url = 'https://' + domain + img_url

        # 发送请求并保存图片
        img_filename = img_url.split("/")[-1]
        last_dot_index = img_filename.rfind(".")

        # 提取后缀
        if last_dot_index != -1:
            file_extension = img_filename[last_dot_index:]
        else:
            file_extension = ".jpg"
        img_filename = str(counter).zfill(6) + file_extension
        counter+=1
        img_path = os.path.join(save_dir, img_filename)
        try:
            img_data = requests.get(img_url).content
        except Exception as e:
            print(f"Exception: {e}.")

        with open(img_path, "wb") as f:
            f.write(img_data)
    if len(os.listdir(save_dir)) == 0:
        os.removedirs(save_dir)
    return save_dir


def load_image_today(date, channel, save_dir):
    c = MongoDBHelper()
    c.connect()
    query = {"date": date}
    if channel is not None:
        query = {
            "date": date,
            "channel": channel,
            "status": None
        }
    items = list(c.find_data('tele_img', query))
    for item in items:
        if item["link"] is not None:
            try:
                path = load_page(item["link"], item["title"], save_dir)
                status = "Success"
            except Exception as e:
                path = None
                status = f"Exception: {e}"
            c.update_data('tele_img', {"link": item["link"]}, {"path": path, "status": status})
    c.close()


def loadImgByChannel(channel,save_dir):
    load_telegram_links(channel)
    load_image_today(datetime.now().strftime('%Y-%m-%d'), channel, save_dir)

def run():
    # loadImgByChannel('exhentai5starcosplay', '/Volumes/新加卷/telegra.ex/cosplay')
    # loadImgByChannel('exhentai5star', '/Volumes/新加卷/telegra.ex/5star')
    # loadImgByChannel('exhentai5starfullcolor', '/Volumes/新加卷/telegra.ex/5star')

    pyrogramMsgUtil.get_img_message_id('exhentai5star', 42448)
    pyrogramMsgUtil.get_img_message_id('exhentai5starcosplay', 17896)
    pyrogramMsgUtil.get_img_message_id('exhentai5starfullcolor', 20421)

if __name__ == '__main__':
    # run()
    # parent_dir = '/Volumes/新加卷/telegra.ex/5star/'
    # dirs = os.listdir(parent_dir)
    # for dir in dirs:
    #     if len(os.listdir(parent_dir +dir)) == 0:
    #         os.removedirs(parent_dir +dir)
    load_page2('https://telegra.ph/%E7%B4%A7%E6%80%A5%E4%BC%81%E5%88%92-%E5%A5%B6%E6%98%94---%E5%B1%85%E5%AE%B6-143P-01-18',
              'test','/Users/<USER>/Desktop/test/')
