
import datetime
import sys

from bs4 import BeautifulSoup
import tagui_2 as r
from mongo.MongoDBHelper import MongoDBHelper

now = datetime.datetime.now().strftime('%Y-%m-%d')
mongo = MongoDBHelper()


def load_telegram_links(channel):
    r.init(True)
    r.url('https://web.telegram.org/k/#@' + channel)
    # r.click('//*[@id="column-center"]/div/div/div[2]/div[1]/div[1]/div/avatar-element/img')
    # r.wait(2)
    # r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[4]/div')
    # r.wait(5)

    r.click('//*[@id="column-center"]/div/div/div[2]/div[1]/div[1]/div/div[1]/img')
    r.wait(2)
    r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[5]/div')
    r.wait(5)

    content = r.read('page')
    r.close()
    soup = BeautifulSoup(content, features="html.parser")
    div = soup.find("div", {"id": "column-right"})
    links = div.findAll("a")
    links = list(filter(lambda x: x, links))
    links2 = list(filter(lambda x: 'href' in x.attrs and 'https://telegra.ph' in x.attrs['href'], links))
    links3 = list(map(lambda x: x.attrs['href'], links2))

    try:
        mongo.connect()
        his_link = list(mongo.find_data('tele_img', {'channel': channel, 'link': {'$in': links3}}))
        his_link = set(map(lambda x: x["link"], his_link))
        links2 = list(filter(lambda x: x.attrs['href'] not in his_link, links2))
        for link in links2:
            mongo.insert_data('tele_img', {
                "link": link.attrs['href'],
                "title": link.next[19:],
                "channel": channel,
                "date": datetime.datetime.now().strftime('%Y-%m-%d')
            })
    except Exception as e:
        print(e)
    finally:
        mongo.close()
    links3 = list(map(lambda x: x.attrs['href'], links2))
    return links3

def run():
        r.init(True)
        r.close()

if __name__ == '__main__':
    run()