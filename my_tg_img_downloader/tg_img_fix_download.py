# fixer_script.py (移除重试功能，修改数据库名)

import os
import shutil
import time  # time.sleep 仍然可能在 trigger_single_item_redownload_with_requests 中用于小延迟
from datetime import datetime, timedelta
import re
import urllib.parse

import requests
from bs4 import BeautifulSoup

from pymongo import MongoClient  # 或者你的 MongoDBHelper

# --- 配置 ---
MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB_NAME = "mydatabase"  # <--- 修改数据库名
MONGO_COLLECTION_NAME = "tele_img"

SMALL_FILE_THRESHOLD_BYTES = 20 * 1024
# MAX_REDOWNLOAD_ATTEMPTS = 3 # <--- 移除或注释掉
# RETRY_WAIT_SECONDS = 30 * 60  # <--- 移除或注释掉

BASE_TG_IMAGE_PATH = "/Volumes/新加卷/tg-image/"


# (get_mongodb_collection, clear_directory_contents, validate_downloaded_path 函数保持不变)
# (trigger_single_item_redownload_with_requests 和 get_extension_from_content_type 函数也保持不变)
def get_mongodb_collection():
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB_NAME]  # 使用新的数据库名
    return db[MONGO_COLLECTION_NAME]


def clear_directory_contents(directory_path):
    if not os.path.isdir(directory_path):
        print(f"Warning: Directory {directory_path} does not exist. Cannot clear contents.")
        return
    print(f"Clearing contents of directory: {directory_path}")
    for item_name in os.listdir(directory_path):
        item_path = os.path.join(directory_path, item_name)
        try:
            if os.path.isfile(item_path) or os.path.islink(item_path):
                os.unlink(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
        except Exception as e:
            print(f"Failed to delete {item_path}. Reason: {e}")


def validate_downloaded_path(directory_path, threshold_bytes):
    if not os.path.isdir(directory_path):
        print(f"Validation failed: Path {directory_path} is not a directory or does not exist.")
        return True
    try:
        files = [f for f in os.listdir(directory_path) if os.path.isfile(os.path.join(directory_path, f))]
    except FileNotFoundError:
        print(f"Validation failed: Path {directory_path} not found during listdir.")
        return True
    if not files:
        print(f"Validation failed: Path {directory_path} is empty.")
        return True
    for filename in files:
        file_path = os.path.join(directory_path, filename)
        try:
            if os.path.getsize(file_path) >= threshold_bytes:
                print(
                    f"Validation success: Found valid file {filename} (size >= {threshold_bytes}B) in {directory_path}.")
                return False
        except OSError:
            print(f"Warning: Could not get size of file {file_path} in {directory_path}.")
            continue
    print(f"Validation failed: All checkable files in {directory_path} are smaller than {threshold_bytes} bytes.")
    return True


def get_extension_from_content_type(content_type_str):
    if not content_type_str: return None
    content_type_str = content_type_str.lower()
    if 'image/jpeg' in content_type_str or 'image/jpg' in content_type_str: return ".jpg"
    if 'image/png' in content_type_str: return ".png"
    if 'image/gif' in content_type_str: return ".gif"
    if 'image/webp' in content_type_str: return ".webp"
    if content_type_str.startswith('image/'):
        parts = content_type_str.split('/')
        if len(parts) > 1:
            ext_part = parts[1].split(';')[0].strip()
            if 0 < len(ext_part) <= 4 and ext_part.isalpha(): return "." + ext_part
    return ".jpg"


def trigger_single_item_redownload_with_requests(item_document, effective_target_path):
    page_url = item_document.get('link')
    page_title = item_document.get('title')

    if not all([page_url, page_title, effective_target_path]):
        print(
            f"Error: Item document missing link/title, or effective_target_path is invalid for re-download. Doc ID: {item_document.get('_id')}")
        return False

    print(f"Attempting re-download (requests) for: '{page_title}' (URL: {page_url}) into path: {effective_target_path}")
    headers2 = {
        'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'i',
        'referer': 'https://telegra.ph/',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'image',
        'sec-fetch-mode': 'no-cors',
        'sec-fetch-site': 'cross-site',
        'sec-fetch-storage-access': 'active',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    headers = {
        'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://telegra.ph/',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'image',
        'sec-fetch-mode': 'no-cors',
        'sec-fetch-site': 'cross-site',
        'sec-fetch-storage-access': 'active',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }

    main_page_response = None
    for i in range(5):
        try:
            time.sleep(0.2)
            s = requests.Session()
            s.headers.update(headers)  # 将你的原始 headers 应用到 session

            main_page_response = s.get(page_url, headers=headers, timeout=30)
            main_page_response.raise_for_status()
            print(f"Successfully fetched main page: {page_url}")
            break
        except requests.exceptions.RequestException as e:
            print(f"Error fetching main page {page_url}: {e}")
    if main_page_response is None:
        return False

    soup = BeautifulSoup(main_page_response.content, "html.parser")
    img_tags = soup.find_all("img")

    if not img_tags:
        print(f"No <img> tags found on page: {page_url}.")
        os.makedirs(effective_target_path, exist_ok=True)
        return True

    print(f"Found {len(img_tags)} <img> tags on page {page_url}.")
    os.makedirs(effective_target_path, exist_ok=True)
    downloaded_image_count = 0
    img_counter = 1

    for img_tag in img_tags:
        img_src = img_tag.get('src')
        if not img_src or not img_src.strip():
            print(f"Skipping an img tag with empty or missing src.")
            continue
        retry = 5
        img_counter += 1
        for i in range(retry):
            try:
                time.sleep(0.2)
                img_full_url = urllib.parse.urljoin(page_url, img_src)
                print(f"  Downloading image {img_counter}: {img_full_url}")
                img_response = s.get(img_full_url, headers=headers2, timeout=20, stream=True)
                img_response.raise_for_status()
                content_type = img_response.headers.get('Content-Type', '')
                file_extension = get_extension_from_content_type(content_type)

                content_length_str = img_response.headers.get('Content-Length')

                img_filename = f"{img_counter:06d}{file_extension}"
                img_save_path = os.path.join(effective_target_path, img_filename)
                with open(img_save_path, 'wb') as f:
                    for chunk in img_response.iter_content(chunk_size=8192):
                        f.write(chunk)
                print(f"    Successfully saved (image {img_counter}): {img_save_path}")
                downloaded_image_count += 1
                break
            except requests.exceptions.RequestException as img_req_exc:
                print(f"    Error downloading image {img_full_url}: {img_req_exc}")
            except IOError as img_io_exc:
                print(f"    Error saving image {img_full_url} to {locals().get('img_save_path', 'N/A')}: {img_io_exc}")
            except Exception as img_generic_exc:
                print(f"    An unexpected error occurred for image {img_full_url}: {img_generic_exc}")

    if downloaded_image_count > 0:
        print(f"Finished processing page '{page_title}'. Successfully downloaded {downloaded_image_count} images.")
        return True
    else:
        print(f"Finished processing page '{page_title}'. No images were successfully downloaded.")
        return False


def run_fixer():
    collection = get_mongodb_collection()
    now = datetime.now()
    three_months_ago_str = (now - timedelta(days=1 * 30)).strftime("%Y-%m-%d")
    initial_path_filter_regex = r"^" + re.escape(BASE_TG_IMAGE_PATH)

    query = {
        "tg_msg_id": {"$exists": True, "$nin": [None, ""]},
        "date": {"$gte": three_months_ago_str},
        "link": {"$exists": True, "$nin": [None, ""]},
        "title": {"$exists": True, "$nin": [ "无pikpak网盘的进群搜编号下载压缩包"]},
        "path": {"$regex": initial_path_filter_regex}
    }

    items_to_check = list(collection.find(query))  # DEBUG: 先处理少量数据
    print(f"Found {len(items_to_check)} items matching initial criteria.")

    for item in items_to_check:
        item_id = item.get('_id')
        original_path = item.get('path')
        date_str = item.get('date')
        link = item.get('link')
        title = item.get('title')

        print(f"\n--- Checking item ID: {item_id}, Original Path: {original_path}, Date: {date_str} ---")

        if not all([original_path, date_str, link, title]):
            print(f"Item ID: {item_id} is missing critical fields. Skipping.")
            collection.update_one({"_id": item_id}, {
                "$set": {"status_fixer": "missing_critical_fields", "fixer_last_checked": datetime.utcnow()}})
            continue

        if not original_path.startswith(BASE_TG_IMAGE_PATH):
            print(
                f"Item ID: {item_id} path '{original_path}' does not start with base '{BASE_TG_IMAGE_PATH}'. Skipping.")
            collection.update_one({"_id": item_id}, {
                "$set": {"status_fixer": "path_invalid_base_prefix", "fixer_last_checked": datetime.utcnow()}})
            continue

        path_to_validate_and_download = original_path
        update_path_in_db_on_success = False

        relative_path_from_base = original_path[len(BASE_TG_IMAGE_PATH):]
        path_segments_after_base = relative_path_from_base.split(os.sep)
        potential_ym_segment = path_segments_after_base[0]

        if re.fullmatch(r"\d{4}-\d{2}", potential_ym_segment) and len(path_segments_after_base) > 1:
            print(f"Item ID: {item_id} - Path '{original_path}' identified as Type A. Validating directly.")
        else:
            print(f"Item ID: {item_id} - Path '{original_path}' identified as Type B or needing restructure.")
            year_month_from_date = date_str[:7]
            if not relative_path_from_base or relative_path_from_base in ['.', '..']:
                print(f"Item ID: {item_id} - Invalid relative path part '{relative_path_from_base}'. Skipping.")
                collection.update_one({"_id": item_id}, {
                    "$set": {"status_fixer": "path_invalid_relative_part", "fixer_last_checked": datetime.utcnow()}})
                continue
            derived_path = os.path.join(BASE_TG_IMAGE_PATH, year_month_from_date, relative_path_from_base)
            print(f"Item ID: {item_id} - Derived path for validation/download: {derived_path}")
            path_to_validate_and_download = derived_path
            if os.path.normpath(original_path).lower() != os.path.normpath(derived_path).lower():
                update_path_in_db_on_success = True

        is_bad_download = validate_downloaded_path(path_to_validate_and_download, SMALL_FILE_THRESHOLD_BYTES)

        if is_bad_download:
            print(
                f"Item ID: {item_id} at (effective) path '{path_to_validate_and_download}' identified as problematic. Attempting fix (single attempt)...")

            clear_directory_contents(path_to_validate_and_download)
            try:
                os.makedirs(path_to_validate_and_download, exist_ok=True)
            except OSError as e:
                print(
                    f"Error creating directory {path_to_validate_and_download}: {e}. Skipping download for this item.")
                collection.update_one({"_id": item_id}, {
                    "$set": {"status_fixer": "failed_create_dir", "fixer_last_failed": datetime.utcnow()}})
                continue  # 跳到下一个MongoDB条目

            # --- 开始单次下载尝试 ---
            download_process_ok = trigger_single_item_redownload_with_requests(item, path_to_validate_and_download)


        else:  # is_bad_download is False (初始验证通过)
            print(f"Item ID: {item_id} at (effective) path '{path_to_validate_and_download}' seems OK.")
            update_payload_ok = {"$set": {
                "status_fixer": "checked_ok_requests",
                "fixer_last_checked": datetime.utcnow()
            }}
            if update_path_in_db_on_success and os.path.normpath(original_path).lower() != os.path.normpath(
                    path_to_validate_and_download).lower():
                update_payload_ok["$set"]["path"] = path_to_validate_and_download
                print(f"Item ID: {item_id} - Path in DB updated (structure corrected): {path_to_validate_and_download}")
            collection.update_one({"_id": item_id}, update_payload_ok)

    print("\n--- Fixer script finished ---")
    # client.close() # 如果你显式创建了 client 而不是在 get_mongodb_collection 内部管理


if __name__ == "__main__":
    run_fixer()