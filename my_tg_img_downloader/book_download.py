import datetime
import sys

import requests
import os
import pyrogram
from pyrogram import Client, filters
from pyrogram.errors import Flood<PERSON><PERSON>
from mongo.MongoDBHelper import MongoDBHelper

# 获取Telegram Bot API的token
bot_token = '**********************************************'
# 准备下载目录
download_dir = '/Volumes/aigo2/tele-book'
api_hash= '792cfdcecaeab811010c87c98c200ec7'
api_id= '21786636'

app = pyrogram.Client(
        "media_downloader",
        api_id=api_id,
        api_hash=api_hash,
        # bot_token=bot_token,
        proxy={
            "hostname": "127.0.0.1",
            "port": 7890,
            "scheme": "socks5"
        })

def get_video_message_id(channel, def_msg_id=2885):
    c = MongoDBHelper()
    videos = c.my_find_data('tele_book', {"channel": channel})
    if videos is None or len(videos) == 0:
        last_msg_id = def_msg_id + 1
    else:
        last_msg_id = max(v["msg_id"] for v in videos) + 1
    with app:
        message_ids = [last_msg_id + x for x in range(100)]
        message_info = app.get_messages(channel, message_ids=message_ids)
        message_info = list(filter(lambda x: x.document is not None, message_info))

        for msg in message_info:
            try:
                file_name = download_dir + "/" + channel + "/" + msg.document.file_name
                app.download_media(msg, file_name=file_name)
                c.my_insert_data('tele_book', {
                    "path": file_name,
                    "channel": channel,
                    "msg_id": msg.id,
                    "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
            except Exception as e:
                print(e)

    # return message_info

def run():
    # msg = get_video_message_id('MarioBase')
    get_video_message_id('Qikan2023', 2470)
    # get_video_message_id('MarioBase', 34413)


if __name__ == '__main__':
    run()