# my_image_spider.py
import scrapy
import os
from datetime import date
import re
from urllib.parse import urlparse

today = date.today()


def sanitize_filename(name):
    if name is None: name = "untitled"
    name = str(name)
    name = re.sub(r'[<>:"/\\|?*]', '_', name)
    name = re.sub(r'[\x00-\x1f\x7f]', '', name)
    name = re.sub(r'[\s_]+', '_', name)
    name = name.strip('._')
    return name if name else "untitled"


class ImageSpider(scrapy.Spider):
    name = 'image_spider'

    # 1. 添加显式的 __init__ 方法
    def __init__(self, urls=None, save_dir=None, *args, **kwargs):
        super(ImageSpider, self).__init__(*args, **kwargs)  # 调用父类的初始化方法
        self.urls = urls if urls is not None else []  # 确保 self.urls 被初始化为一个列表
        self.save_dir = save_dir  # save_dir 正常接收

        # 添加日志来确认接收到的参数
        self.logger.info(f"ImageSpider Initialized. Received urls (type: {type(self.urls)}): {self.urls}")
        self.logger.info(f"ImageSpider Initialized. Received save_dir: {self.save_dir}")

        if not self.urls:
            self.logger.warning(
                "ImageSpider initialized with an empty 'urls' list. This might lead to no requests being made if not intended.")

        # 在修复模式下，save_dir 可能是 None 或虚拟值，这没关系，因为修复模式的路径直接来自urls元组的第三个元素
        # 但如果是非修复模式（urls中的元组只有2个元素），则 self.save_dir 必须有效
        is_normal_mode_item_present = any(len(item) == 2 for item in self.urls)
        if is_normal_mode_item_present and not self.save_dir:
            self.logger.error(
                "ImageSpider has 'save_dir=None' or empty, but 'urls' contains items for normal mode (2-element tuples). This will cause errors.")

    def start_requests(self):
        # 由于 __init__ 中已确保 self.urls 是列表，这里的检查可以简化
        if not self.urls:
            self.logger.error(
                "self.urls is empty in start_requests. No requests will be made. Please check the 'urls' argument passed to the spider.")
            return

        for item_data in self.urls:
            page_url, page_title = None, None
            is_fixer_item = False
            base_dir_for_parse_meta = None  # 将在下面根据模式设置

            if len(item_data) == 3:  # 修复模式: (url, title, exact_output_path)
                page_url, page_title, exact_output_path = item_data
                is_fixer_item = True
                base_dir_for_parse_meta = exact_output_path
                self.logger.info(
                    f"[Fixer Mode] Preparing request for page: '{page_title}', Exact Path: {exact_output_path}")
                os.makedirs(exact_output_path, exist_ok=True)
            elif len(item_data) == 2:  # 普通模式: (url, title)
                page_url, page_title = item_data
                is_fixer_item = False
                if not self.save_dir:  # 对普通模式，self.save_dir 必须有效
                    self.logger.error(
                        f"Cannot process normal mode item ('{page_url}', '{page_title}') because self.save_dir is not set. Skipping.")
                    continue
                monthly_save_dir = os.path.join(self.save_dir, today.strftime("%Y-%m"))
                base_dir_for_parse_meta = monthly_save_dir
                self.logger.info(
                    f"[Normal Mode] Preparing request for page: '{page_title}', Monthly Dir: {monthly_save_dir}")
                os.makedirs(monthly_save_dir, exist_ok=True)
            else:
                self.logger.error(f"Invalid item data in self.urls (expected 2 or 3 elements): {item_data}. Skipping.")
                continue

            if not page_url:  # 再次检查，尽管不太可能发生
                self.logger.error(f"Page URL is None for item_data: {item_data}. Skipping.")
                continue

            yield scrapy.Request(url=page_url, callback=self.parse,
                                 meta={'base_dir_for_parse': base_dir_for_parse_meta,
                                       'page_title_from_meta': page_title,
                                       'is_fixer_item': is_fixer_item})

    def parse(self, response):
        base_dir_for_parse = response.meta['base_dir_for_parse']
        page_title_from_meta = response.meta['page_title_from_meta']
        is_fixer_item = response.meta['is_fixer_item']

        current_page_title = page_title_from_meta
        if current_page_title is None:  # 理论上在我们的逻辑中，这个title总会被填充
            title_from_css = response.css('title::text').get()
            current_page_title = title_from_css if title_from_css else "Untitled_Page_From_Parse"

        page_specific_save_dir = None
        if is_fixer_item:
            page_specific_save_dir = base_dir_for_parse  # 修复模式下，这就是最终路径
        else:
            # 普通模式下，base_dir_for_parse 是月度目录
            sanitized_page_title = sanitize_filename(current_page_title[:100])
            page_specific_save_dir = os.path.join(base_dir_for_parse, sanitized_page_title)
            os.makedirs(page_specific_save_dir, exist_ok=True)  # 确保目录存在

        self.logger.info(
            f"Processing page '{current_page_title}'. Final save directory for images: {page_specific_save_dir}")

        img_urls = response.css('img::attr(src)').getall()
        self.logger.info(f"Found {len(img_urls)} image sources on page '{current_page_title}' ({response.url})")

        img_number = 1
        found_valid_img_src = False
        for img_src in img_urls:
            if not img_src or not img_src.strip():
                self.logger.warning(f"Empty image src found on page {response.url} for title '{current_page_title}'")
                continue

            found_valid_img_src = True
            img_full_url = response.urljoin(img_src)
            img_path_base = os.path.join(page_specific_save_dir, f"{img_number:06}")  # 不含后缀

            yield scrapy.Request(img_full_url, callback=self.save_image,
                                 meta={'img_path_base': img_path_base,
                                       'img_original_url': img_full_url,
                                       'page_title_log': current_page_title
                                       })
            img_number += 1

        if not found_valid_img_src:  # 如果循环未执行或所有src都为空
            self.logger.info(
                f"No valid image URLs found to process for page '{current_page_title}'. Directory '{page_specific_save_dir}' might remain empty or only contain previous files if not cleared.")

    def save_image(self, response):
        img_path_base = response.meta['img_path_base']
        img_original_url = response.meta['img_original_url']
        page_title_log = response.meta.get('page_title_log', 'N/A')

        if response.status != 200:
            self.logger.warning(
                f"Failed to download image for '{page_title_log}' from {img_original_url}. Status: {response.status}.")
            return

        content_type = response.headers.get('Content-Type', b'').decode('utf-8', 'ignore').lower()
        file_extension = None
        if 'image/jpeg' in content_type or 'image/jpg' in content_type:
            file_extension = ".jpg"
        elif 'image/png' in content_type:
            file_extension = ".png"
        elif 'image/gif' in content_type:
            file_extension = ".gif"
        elif 'image/webp' in content_type:
            file_extension = ".webp"
        elif 'image/' in content_type:
            maybe_ext = content_type.split('/')[-1].split(';')[0].strip()
            if len(maybe_ext) > 0 and len(maybe_ext) <= 4 and maybe_ext.isalpha():
                file_extension = "." + maybe_ext
            else:
                self.logger.warning(
                    f"Uncommon image Content-Type for '{page_title_log}' from {img_original_url}: {content_type}. Defaulting extension to .img")
                file_extension = ".img"

        if not file_extension:
            self.logger.warning(
                f"URL for '{page_title_log}' ({img_original_url}) did not return a recognized image Content-Type: '{content_type}'. Body size: {len(response.body)} bytes. Skipping save.")
            return

        img_path_final = img_path_base + file_extension

        # (可选) 检查文件大小，但主要依赖修复脚本的 validate_downloaded_path
        # min_meaningful_size_bytes = 2 * 1024
        # if len(response.body) < min_meaningful_size_bytes:
        #      self.logger.warning(f"Image for '{page_title_log}' from {img_original_url} is very small ({len(response.body)} bytes), saved to {img_path_final}.")

        try:
            os.makedirs(os.path.dirname(img_path_final), exist_ok=True)
            with open(img_path_final, 'wb') as f:
                f.write(response.body)
            self.logger.info(
                f"Successfully saved image for '{page_title_log}': {img_path_final} (from {img_original_url})")
        except Exception as e:
            self.logger.error(f"Error saving image for '{page_title_log}' to {img_path_final}: {e}")