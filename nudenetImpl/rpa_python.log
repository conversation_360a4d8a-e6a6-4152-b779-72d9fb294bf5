
START - automation started - Tue Feb 11 2025 07:30:05 GMT+0800 (+08)

LIVE MODE - type done to quit
 [RPA][STARTED]
[RPA][0] - listening for inputs
[RPA][1] - https://pcgamestorrents.com/page/0
[RPA][1] - listening for inputs
[RPA][2] - read page to read_result
[RPA][2] - listening for inputs
[RPA][3] - dump read_result to rpa_python.txt
[RPA][3] - listening for inputs
[RPA][4] - https://pcgamestorrents.com/door-kickers-2-task-force-north-skidrow.html
[RPA][4] - listening for inputs
[RPA][5] - exist_result = exist('//article/h1').toString()
[RPA][5] - listening for inputs
[RPA][6] - dump exist_result to rpa_python.txt
[RPA][6] - listening for inputs
[RPA][7] - read //article/h1 to read_result
[RPA][7] - listening for inputs
[RPA][8] - dump read_result to rpa_python.txt
[RPA][8] - listening for inputs
[RPA][9] - exist_result = exist('//article/div[2]/p[5]').toString()
[RPA][9] - listening for inputs
[RPA][10] - dump exist_result to rpa_python.txt
[RPA][10] - listening for inputs
[RPA][11] - read //article/div[2]/p[5] to read_result
[RPA][11] - listening for inputs
[RPA][12] - dump read_result to rpa_python.txt
[RPA][12] - listening for inputs
[RPA][13] - exist_result = exist('//article/div[2]/p[10]').toString()
[RPA][13] - listening for inputs
[RPA][14] - dump exist_result to rpa_python.txt
[RPA][14] - listening for inputs
[RPA][15] - read //article/div[2]/p[10] to read_result
[RPA][15] - listening for inputs
[RPA][16] - dump read_result to rpa_python.txt
[RPA][16] - listening for inputs
[RPA][17] - exist_result = exist('//article/div[2]/p[11]').toString()
[RPA][17] - listening for inputs
[RPA][18] - dump exist_result to rpa_python.txt
[RPA][18] - listening for inputs
[RPA][19] - read //article/div[2]/p[11] to read_result
[RPA][19] - listening for inputs
[RPA][20] - dump read_result to rpa_python.txt
[RPA][20] - listening for inputs
[RPA][21] - exist_result = exist('//article/div[2]/p[16]').toString()
[RPA][21] - listening for inputs
[RPA][22] - dump exist_result to rpa_python.txt
[RPA][22] - listening for inputs
[RPA][23] - read //article/div[2]/p[16] to read_result
[RPA][23] - listening for inputs
[RPA][24] - dump read_result to rpa_python.txt
[RPA][24] - listening for inputs
[RPA][25] - exist_result = exist('//article/div[2]/p[15]').toString()
[RPA][25] - listening for inputs
[RPA][26] - dump exist_result to rpa_python.txt
[RPA][26] - listening for inputs
[RPA][27] - read //article/div[2]/p[15] to read_result
[RPA][27] - listening for inputs
[RPA][28] - dump read_result to rpa_python.txt
[RPA][28] - listening for inputs
[RPA][29] - exist_result = exist('//article/div[2]/p[18]/a').toString()
[RPA][29] - listening for inputs
[RPA][30] - dump exist_result to rpa_python.txt
[RPA][30] - listening for inputs
[RPA][31] - click //article/div[2]/p[18]/a
[RPA][31] - listening for inputs
[RPA][32] - exist_result = exist('//*[@id="nut"]').toString()
[RPA][32] - listening for inputs
[RPA][33] - dump exist_result to rpa_python.txt
[RPA][33] - listening for inputs
[RPA][34] - click //*[@id="nut"]
[RPA][34] - listening for inputs
[RPA][35] - exist_result = exist('/html/body/input').toString()
[RPA][35] - listening for inputs
[RPA][36] - dump exist_result to rpa_python.txt
[RPA][36] - listening for inputs
[RPA][37] - https://pcgamestorrents.com/page/0
