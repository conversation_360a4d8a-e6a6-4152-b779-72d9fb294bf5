import cv2
import numpy as np
import requests
from nudenet import NudeDetector
from typing import List, Dict, Union
import os
import concurrent.futures
from pathlib import Path
import json
from datetime import datetime


class NudeDetectorProcessor:
    def __init__(self):
        """初始化NudeDetector检测器"""
        self.detector = NudeDetector(model_path="/Users/<USER>/PycharmProjects/pythonProject/nudenetImpl/640m.onnx", inference_resolution=640)
        # self.detector = NudeDetector()
        # self.label_translations = {
        #     "FEMALE_GENITALIA_COVERED": "阴部（遮盖）",
        #     "FACE_FEMALE": "面部",
        #     "BUTTOCKS_EXPOSED": "臀部（暴露）",
        #     "FEMALE_BREAST_EXPOSED": "胸部（暴露）",
        #     "FEMALE_GENITALIA_EXPOSED": "阴部（暴露）",
        #     "MALE_BREAST_EXPOSED": "胸部（暴露）",
        #     "ANUS_EXPOSED": "肛门（暴露）",
        #     "FEET_EXPOSED": "脚（暴露）",
        #     "BELLY_COVERED": "腹部（遮盖）",
        #     "FEET_COVERED": "脚（遮盖）",
        #     "ARMPITS_COVERED": "腋下（遮盖）",
        #     "ARMPITS_EXPOSED": "腋下（暴露）",
        #     "FACE_MALE": "面部",
        #     "BELLY_EXPOSED": "腹部（暴露）",
        #     "MALE_GENITALIA_EXPOSED": "男性生殖器（暴露）",
        #     "ANUS_COVERED": "肛门（遮盖）",
        #     "FEMALE_BREAST_COVERED": "胸部（遮盖）",
        #     "BUTTOCKS_COVERED": "臀部（遮盖）"
        # }

        self.label_translations = {

            "FEMALE_GENITALIA_COVERED": "私处含羞藏",  # 奔放地表达隐藏的热情与保护感。
            "FACE_FEMALE": "玉颜活力现",  # 生动描绘面部的光彩与生机，激发活力。
            "FEMALE_BREAST_EXPOSED": "芳胸激情绽",  # 用激情绽放激发读者的想象与兴奋。
            "FEMALE_GENITALIA_EXPOSED": "私处自由展",  # 奔放地展现自由与活力。
            "ANUS_EXPOSED": "狗颠屁股显",  # 直接而有活力地表达显露的状态。
            "FEET_EXPOSED": "足影奔放舞",  # 用奔放的舞蹈激发运动与自由的热情。
            "FEET_COVERED": "足影奔放舞",  # 热情地掩盖，带来期待感。
            "MALE_GENITALIA_EXPOSED": "男体狂野现",  # 狂野地呈现，激发力量与自由感。
            "ANUS_COVERED": "狗颠屁股显",  # 热情地锁定，带来神秘的兴奋。
            "FEMALE_BREAST_COVERED": "芳胸含羞藏",  # 狂野地隐藏，激发好奇与热情。
        }

    def detect_single_image(self, image_path: str) -> List[Dict]:
        """
        检测单张图片并返回结果

        Args:
            image_path (str): 图片路径

        Returns:
            List[Dict]: 检测结果列表，每个结果包含标签、置信度和位置信息
        """
        try:
            return self.detector.detect(image_path)
        except Exception as e:
            print(f"处理图片 {image_path} 时出错: {str(e)}")
            return []
    def detect_url_batch(self, url_batch_list: List[str],
                         confidence_threshold: float = 0.5,
                         batch_size: int = 4) -> Dict[str, float]:
        try:
            batch_results = self.detector.detect_batch(url_batch_list, batch_size)
        except:
            batch_results = []
            for url in url_batch_list:
                try:
                    batch_results.append(self.detector.detect(url))
                except:
                    pass

        character_map = {}
        for res in batch_results:
            for label in res:
                if not label["class"] in character_map:
                    character_map[label["class"]] = 0
                character_map[label["class"]] += 1

        trans_map = {}
        for k in character_map.keys():
            character_map[k] /= len(url_batch_list)
            if k in self.label_translations:
                trans_map[self.label_translations[k]] = character_map[k]
        trans_map = dict(sorted(trans_map.items(), key=lambda x: x[1], reverse=True))
        return trans_map

    def detect_directory(self,
                         directory_path: str,
                         supported_formats: List[str] = ['.jpg', '.jpeg', '.png'],
                         confidence_threshold: float = 0.5,
                         batch_size: int = 4) -> Dict[str, List[Dict]]:
        """
        处理指定目录下的所有图片

        Args:
            directory_path (str): 目录路径
            supported_formats (List[str]): 支持的图片格式列表
            confidence_threshold (float): 置信度阈值
            batch_size (int): 批处理大小

        Returns:
            Dict[str, List[Dict]]: 所有图片的检测结果
        """
        image_paths = []
        # 获取所有支持格式的图片路径
        for root, _, files in os.walk(directory_path):
            for file in files:
                # 检查文件是否以点开头
                if not file.startswith('.'):
                    # 检查文件扩展名
                    if any(file.lower().endswith(fmt) for fmt in supported_formats):
                        full_path = os.path.join(root, file)
                        image_paths.append(full_path)
                    else:
                        print(f"跳过无效图片: {full_path}")

        if not image_paths:
            print("没有找到有效的图片文件")
        print(f"找到 {len(image_paths)} 个有效图片文件")
        batch_results = self.detector.detect_batch(image_paths, batch_size)

        character_map = {}
        for res in batch_results:
            for label in res:
                if not label["class"] in character_map:
                    character_map[label["class"]] = 0
                character_map[label["class"]]+=1

        trans_map = {}
        for k in character_map.keys():
            character_map[k] /= len(image_paths)
            trans_map[self.label_translations[k]] = character_map[k]
        trans_map = dict(sorted(trans_map.items(), key=lambda x: x[1], reverse=True))
        return trans_map



if __name__ == '__main__':
    n = NudeDetectorProcessor()
    # single
    # res = n.detect_single_image('/Volumes/新加卷/tg-image/2024-12/AnchorPic-_-[Bimilstory]SURL-Vol.03See-throughSeraUniform/000002.jpg')
    # print(res)
    dir = '/Volumes/新加卷/tg-image/2024-12/'
    for root, _, files in os.walk('/Volumes/新加卷/tg-image/2024-12/'):
        if root == dir:
            continue
        print(root)
        res = n.detect_directory(root)
        print(res)