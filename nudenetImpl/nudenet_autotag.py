import datetime as datetime
import sys
import os
import json
import requests
from bs4 import BeautifulSoup
from nudenetImpl import NudeDetectorProcessor
from urllib.parse import urlparse
from PIL import Image
import math
import pytz
import datetime
from hangul_utils import split_syllables
from pymongo import MongoClient
import tempfile
import shutil
import numpy as np
import io
import fix_path as f
IST = pytz.timezone('Asia/Shanghai')


class ImageProcessor:
    def __init__(self):
        self.root_dir = "/Volumes/新加卷/tg-image"
        self.tags_dir = os.path.join(self.root_dir, 'api/tags')
        self.tags_file = os.path.join(self.root_dir, 'api/tags.json')
        self.temp_dir = os.path.join(tempfile.gettempdir(), 'nudenet_images')
        self.ensure_directories()
        self.init_mongodb()

    def init_mongodb(self):
        """初始化MongoDB连接"""
        try:
            self.client = MongoClient('mongodb://192.168.0.106:27017/')
            self.db = self.client['mydatabase']
            self.nudenet_collection = self.db['nudenet']
            print("MongoDB connection established")
        except Exception as e:
            print(f"Error connecting to MongoDB: {str(e)}")
            raise

    def ensure_directories(self):
        """确保必要的目录存在"""
        if not os.path.exists(self.tags_dir):
            os.makedirs(self.tags_dir)
        if not os.path.exists(self.tags_file):
            self.save_tags_list([])
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)

    def get_cached_results(self, month, subfolder):
        """
        从MongoDB获取缓存的检测结果

        参数:
            month (str): 月份文件夹
            subfolder (str): 子文件夹名称

        返回:
            dict: 缓存的检测结果，如果没有缓存则返回None
        """
        try:
            result = self.nudenet_collection.find_one({
                'month': month,
                'subfolder': subfolder
            })
            if result:
                return result.get('results', None)
            return None
        except Exception as e:
            print(f"Error reading from MongoDB for {month}/{subfolder}: {str(e)}")
            return None

    def save_cache_results(self, month, subfolder, results, total):
        """
        保存检测结果到MongoDB

        参数:
            month (str): 月份文件夹
            subfolder (str): 子文件夹名称
            results (dict): 检测结果
        """
        try:
            # 使用upsert确保不会重复插入
            self.nudenet_collection.update_one(
                {
                    'month': month,
                    'subfolder': subfolder
                },
                {
                    '$set': {
                        'month': month,
                        'subfolder': subfolder,
                        'results': results,
                        'updated_at': datetime.datetime.now(),
                        "total": total
                    }
                },
                upsert=True
            )
        except Exception as e:
            print(f"Error saving to MongoDB for {month}/{subfolder}: {str(e)}")

    def export_to_json(self):
        """将MongoDB中的数据导出到JSON文件"""
        try:
            # 获取所有标签
            all_tags = set()
            for doc in self.nudenet_collection.find():
                results = doc.get('results', {})
                for tag in results.keys():
                    if results[tag] > 0.5:  # 只导出置信度大于0.5的标签
                        all_tags.add(tag)

            # 创建标签列表
            tags_list = [{"name": tag} for tag in sorted(all_tags)]
            self.save_tags_list(tags_list)

            # 为每个标签创建文件夹列表
            for tag in all_tags:
                folders = []
                for doc in self.nudenet_collection.find():
                    results = doc.get('results', {})
                    if results.get(tag, 0) > 0.5:
                        folders.append({
                            "month": doc['month'],
                            "subfolder": doc['subfolder'],
                            "displayName": doc['subfolder'].replace('_', ' ').title()
                        })
                if folders:
                    self.save_tag_folders(tag, folders)

            print("Successfully exported data to JSON files")
        except Exception as e:
            print(f"Error exporting to JSON: {str(e)}")

    def save_tags_list(self, tags):
        """保存标签列表到tags.json"""
        with open(self.tags_file, 'w', encoding='utf-8') as f:
            json.dump({"tags": tags}, f, ensure_ascii=False, indent=2)

    def save_tag_folders(self, tag_name, folders):
        """保存特定标签的文件夹列表"""
        tag_file = os.path.join(self.tags_dir, f"{tag_name}.json")
        with open(tag_file, 'w', encoding='utf-8') as f:
            json.dump({"folders": folders}, f, ensure_ascii=False, indent=2)

    def update_tag_structure(self, tag_name, folder_info):
        """更新标签结构，添加新的文件夹信息"""
        # 更新tags.json
        with open(self.tags_file, 'r', encoding='utf-8') as f:
            tags_data = json.load(f)

        # 检查标签是否存在
        tag_exists = any(tag['name'] == tag_name for tag in tags_data['tags'])
        if not tag_exists:
            tags_data['tags'].append({"name": tag_name})
            self.save_tags_list(tags_data['tags'])

        # 更新标签的文件夹列表
        tag_file = os.path.join(self.tags_dir, f"{tag_name}.json")
        if os.path.exists(tag_file):
            with open(tag_file, 'r', encoding='utf-8') as f:
                folders_data = json.load(f)
        else:
            folders_data = {"folders": []}

        # # 检查文件夹是否已存在
        folder_exists = any(f['month'] == folder_info['month'] and
                            f['subfolder'] == folder_info['subfolder']
                            for f in folders_data['folders'])

        if not folder_exists:
            folders_data['folders'].append(folder_info)
            self.save_tag_folders(tag_name, folders_data['folders'])

    def get_images_from_folder(self, month, subfolder):
        """
        从指定文件夹获取所有图片路径

        参数:
            month (str): 月份文件夹，如 "2024-03"
            subfolder (str): 子文件夹名称

        返回:
            list: 图片路径列表
        """
        try:
            folder_path = os.path.join(self.root_dir, month, subfolder)
            if not os.path.exists(folder_path):
                print(f"Folder does not exist: {folder_path}")
                return []

            image_paths = []
            for filename in os.listdir(folder_path):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                    image_paths.append(os.path.join(folder_path, filename))

            return image_paths

        except Exception as e:
            print(f"Error getting images from folder {month}/{subfolder}: {str(e)}")
            return []

    def get_image_content(self, image_url):
        """
        获取图片内容

        参数:
            image_url (str): 图片URL

        返回:
            bytes: 图片内容
        """
        try:
            response = requests.get(image_url)
            if response.status_code == 200:
                return response.content
            return None
        except Exception as e:
            print(f"Error getting image content from {image_url}: {str(e)}")
            return None

    def is_valid_month_folder(self, folder_name):
        """
        检查文件夹名是否为有效的月份格式 (YYYY-MM)

        参数:
            folder_name (str): 文件夹名称

        返回:
            bool: 是否为有效的月份文件夹
        """
        try:
            # 检查格式是否为 YYYY-MM
            if len(folder_name) != 7 or folder_name[4] != '-':
                return False

            # 尝试解析日期
            year = int(folder_name[:4])
            month = int(folder_name[5:])

            # 检查年份和月份是否在合理范围内
            if not (2000 <= year <= 2100 and 1 <= month <= 12):
                return False

            return True
        except:
            return False

    def download_image(self, url, temp_path):
        """
        下载图片到临时目录

        参数:
            url (str): 图片URL
            temp_path (str): 临时文件路径

        返回:
            bool: 下载是否成功
        """
        try:
            response = requests.get(url, stream=True)
            if response.status_code == 200:
                with open(temp_path, 'wb') as f:
                    response.raw.decode_content = True
                    shutil.copyfileobj(response.raw, f)
                return True
            return False
        except Exception as e:
            print(f"Error downloading image {url}: {str(e)}")
            return False

    def process_folder(self, month, subfolder):
        """
        处理单个文件夹

        参数:
            month (str): 月份文件夹
            subfolder (str): 子文件夹名称
        """
        try:
            # 首先检查是否有缓存
            cached_results = self.get_cached_results(month, subfolder)
            if cached_results:
                print(f"Using cached results for {month}/{subfolder}")
                # 使用缓存的结果更新标签结构
                for tag_name, confidence in cached_results.items():
                    if confidence > 0.5:
                        folder_info = {
                            "month": month,
                            "subfolder": subfolder,
                            "displayName": subfolder.replace('_', ' ').title()
                        }
                        folder_info = f.get_correct_path(folder_info)
                        print(f"folder info{folder_info}")
                        self.update_tag_structure(tag_name, folder_info)
                        print(f"Updated JSON files for tag: {tag_name}")
                return

            print(f"Processing folder {month}/{subfolder}")
            # 如果没有缓存，则获取图片路径并处理
            image_paths = self.get_images_from_folder(month, subfolder)
            if not image_paths:
                print(f"No images found in {month}/{subfolder}")
                return

            # 读取图片并转换为numpy数组
            image_arrays = []
            for path in image_paths:
                try:
                    image = Image.open(path)
                    image_array = np.array(image)
                    image_arrays.append(image_array)
                except Exception as e:
                    print(f"Error processing image {path}: {str(e)}")
                    continue

            if not image_arrays:
                print(f"No valid images processed for {month}/{subfolder}")
                return

            # 使用NudeNet检测标签
            n = NudeDetectorProcessor()
            res = n.detect_url_batch(image_arrays)
            count = len(image_arrays)

            # 保存检测结果到MongoDB
            self.save_cache_results(month, subfolder, res, total=count)

            # 处理检测到的标签并立即更新JSON文件
            for tag_name, confidence in res.items():
                if confidence > 0.5 or count * confidence > 30:
                    folder_info = {
                        "month": month,
                        "subfolder": subfolder,
                        "displayName": subfolder.replace('_', ' ').title(),
                        "total": count
                    }
                    folder_info = f.get_correct_path(folder_info)
                    print(f"folder info{folder_info}")
                    self.update_tag_structure(tag_name, folder_info)
                    print(f"Updated JSON files for tag: {tag_name}")

        except Exception as e:
            print(f"Error processing folder {month}/{subfolder}: {str(e)}")

    def batch_process_folders(self, single=True):
        """
        批量处理所有文件夹

        参数:
            single (bool): 是否只处理一个月份
        """
        try:
            # 获取所有月份文件夹
            month_folders = []
            for item in os.listdir(self.root_dir):
                if self.is_valid_month_folder(item):
                    month_folders.append(item)

            # 按月份逆序排序
            month_folders.sort(reverse=True)
            print(f"Found {len(month_folders)} month folders: {month_folders}")

            # 处理每个月份文件夹
            for month in month_folders:
                try:
                    print(f"\nProcessing month: {month}")
                    month_path = os.path.join(self.root_dir, month)

                    # 获取月份下的所有子文件夹
                    subfolders = []
                    for item in os.listdir(month_path):
                        item_path = os.path.join(month_path, item)
                        if os.path.isdir(item_path):
                            subfolders.append(item)

                    print(f"Found {len(subfolders)} subfolders in {month}")

                    # 处理每个子文件夹
                    for subfolder in subfolders:
                        print(f"Processing subfolder: {month}/{subfolder}")
                        self.process_folder(month, subfolder)

                except Exception as e:
                    print(f"Error processing month {month}: {str(e)}")
                    continue

                if single:
                    break

        except Exception as e:
            print(f"Error in batch processing: {str(e)}")


if __name__ == '__main__':
    processor = ImageProcessor()
    processor.batch_process_folders(single=True)