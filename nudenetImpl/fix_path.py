import requests
import j<PERSON>
from typing import Dict, Any
import re
import os
from pathlib import Path
from bs4 import BeautifulSoup
import urllib.parse

# Cache for nginx responses
nginx_cache = {}


def clean_folder_name(name: str) -> str:
    """
    Clean folder name by removing special characters and spaces.

    Args:
        name: The folder name to clean

    Returns:
        Cleaned folder name
    """
    # Remove special characters, spaces, and convert to lowercase
    return re.sub(r'[^\w\s-]', '', name).strip().lower()


def get_nginx_directory(month: str) -> str:
    """
    Get directory listing from nginx with caching.

    Args:
        month: The month to get directory listing for

    Returns:
        The HTML response from nginx
    """
    # Check cache first
    if month in nginx_cache:
        print(f"Using cached response for {month}")
        return nginx_cache[month]

    # If not in cache, make request
    nginx_url = f"http://*************:8888/{month}"
    try:
        response = requests.get(nginx_url)
        response.raise_for_status()

        # Store in cache
        nginx_cache[month] = response.text
        print(f"Cached response for {month}")

        return response.text
    except requests.RequestException as e:
        print(f"Error making request to {nginx_url}: {e}")
        return ""


def get_correct_path(folder_item: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a folder item and return the updated version with correct subfolder path.

    Args:
        folder_item: Dictionary containing folder information with month and subfolder

    Returns:
        Updated folder item with corrected subfolder path
    """
    month = folder_item['month']
    original_subfolder = folder_item['subfolder']

    # Get nginx response (using cache)
    nginx_response = get_nginx_directory(month)
    if not nginx_response:
        return folder_item

    # Clean the original subfolder name
    clean_name = clean_folder_name(original_subfolder)

    # Find matching file in nginx response
    matching_url = find_matching_url(nginx_response, clean_name)

    if matching_url:
        # Update the subfolder with the href value
        folder_item['subfolder'] = matching_url
        print(f"Updated subfolder for {original_subfolder} to {matching_url}")
    else:
        print(f"Warning: No matching URL found for {original_subfolder}")

    return folder_item


def find_matching_url(nginx_response: str, clean_name: str) -> str:
    """
    Find the matching URL in the nginx response based on the clean name.

    Args:
        nginx_response: The HTML response from nginx
        clean_name: The cleaned name to match against

    Returns:
        The matching URL or empty string if no match found
    """
    try:
        # Parse HTML using BeautifulSoup
        soup = BeautifulSoup(nginx_response, 'html.parser')

        # Find all links in the pre tag
        pre_tag = soup.find('pre')
        if not pre_tag:
            return ""

        links = pre_tag.find_all('a')

        # Process each link
        for link in links:
            href = link.get('href', '')
            if not href or href == '../':
                continue

            # Decode URL-encoded characters for comparison
            decoded_href = urllib.parse.unquote(href)

            # Clean the link text for comparison
            clean_link_text = clean_folder_name(decoded_href)

            # Check if the cleaned names match
            if clean_name in clean_link_text:
                # Return the original href value
                return href.rstrip('/')

    except Exception as e:
        print(f"Error parsing nginx response: {e}")

    return ""


def process_json_file(input_file: str, output_file: str):
    """
    Process a single JSON file and save the updated version.

    Args:
        input_file: Path to input JSON file
        output_file: Path to save the updated JSON file
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Process each folder item
        if 'folders' in data:
            data['folders'] = [get_correct_path(item) for item in data['folders']]

        # Save the updated JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"Error processing JSON file {input_file}: {e}")


def process_directory(input_dir: str, output_dir: str = None):
    """
    Process all JSON files in the specified directory.

    Args:
        input_dir: Directory containing JSON files to process
        output_dir: Directory to save processed files (if None, will use input_dir)
    """
    if output_dir is None:
        output_dir = input_dir

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get all JSON files in the input directory
    json_files = list(Path(input_dir).glob('**/*.json'))

    if not json_files:
        print(f"No JSON files found in {input_dir}")
        return

    print(f"Found {len(json_files)} JSON files to process")

    # Process each JSON file
    for json_file in json_files:
        # Create output path
        rel_path = json_file.relative_to(input_dir)
        output_path = Path(output_dir) / rel_path

        print(f"Processing {json_file}...")
        process_json_file(str(json_file), str(output_path))
        print(f"Saved updated file to {output_path}")


if __name__ == "__main__":
    # Example usage
    input_directory = "/Volumes/新加卷/tg-image/api/tags"  # Replace with your input directory path
    output_directory = "/Volumes/新加卷/tg-image/api/tags/tags2"  # Replace with your output directory path (optional)

    process_directory(input_directory, output_directory)
