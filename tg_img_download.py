# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
import math

# import rpa as r
import datetime
import random
import sys

import telegram_reciever
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
import tagui_2 as r
import image_after_check
import signal
import shutil
import asyncio
import pyrogram
from pyrogram.errors import FloodWait

import pytz
import traceback

from collections import defaultdict
from mongo.MongoDBHelper import MongoDBHelper
from my_tg_img_downloader import pyrogramMsgUtil
from my_tg_img_downloader import telegram_img_download
import logging


pyrogram_logger = logging.getLogger("pyrogram")
mongo_logger = logging.getLogger("pymongo")
# 将其级别设置为 INFO 或更高
pyrogram_logger.setLevel(logging.ERROR)
mongo_logger.setLevel(logging.ERROR)

utc = pytz.UTC
IST = pytz.timezone('Asia/Shanghai')

root = tk.Tk()

SCREEN_WIDTH = root.winfo_screenwidth()
SCREEN_HEIGHT = root.winfo_screenheight()

file_path = '/Volumes/新加卷/download_list/download'
file_path_dir = '/Volumes/新加卷/download_list/*'
now = datetime.datetime.now().strftime('%Y-%m-%d')

yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday - datetime.timedelta(hours=8)
yesterday = utc.localize(yesterday)
his_set = set()

mongo = MongoDBHelper()

# Pyrogram配置
api_hash = '792cfdcecaeab811010c87c98c200ec7'
api_id = '21786636'


async def get_dict(app, channel, object_list):
    result_dict = defaultdict(list)
    result_dict2 = defaultdict(list)
    dict3 = {}
    for obj in object_list:
        result_dict[obj.media_group_id].append(obj)
    result_dict = dict(result_dict)

    for key, messages in result_dict.items():
        try:
            comments_as = app.get_discussion_replies(channel, messages[0].id)
            comments = []
            async for comment in comments_as:  # 处理异步生成器
                comments.append(comment)
            comments.sort(key=lambda obj: obj.id)
            messages.extend(comments)
        except Exception as e:
            pass
        message_info_f = list(filter(lambda x: (x.media is not None and x.media.name == 'PHOTO') \
            or (x.media is not None and x.media.name == 'VIDEO') \
        or (x.sender_chat is not None and x.sender_chat.photo is not None) \
        or (x.sender_chat is not None and x.sender_chat.video is not None) \
                                     , messages))
        if len(message_info_f) < 20:
            continue
        result_dict2[key] = message_info_f

    return result_dict2

async def redownload_media_group(channel, tg_msg_id, target_dir, title):
    """
    根据MongoDB记录重新从Telegram下载图片
    """
    print(f"开始重新下载: {title} (频道: {channel}, 消息ID: {tg_msg_id})")

    async with pyrogram.Client(
        "media_downloader",
        api_id=api_id,
        api_hash=api_hash,
        proxy={
            "hostname": "127.0.0.1",
            "port": 7890,
            "scheme": "socks5"
        }) as app:

        try:

            message_ids = [tg_msg_id + x for x in range(20)]
            message = await app.get_messages(channel, message_ids=message_ids)
            media_group_id = message[0].media_group_id
            message = list(filter(lambda x: x.media_group_id == media_group_id, message))

            if not message:
                print(f"无法找到消息 {tg_msg_id} 在频道 {channel}")
                return False

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            group_dict = await get_dict(app, channel, message)
            if len(group_dict.items())==0:
                return False

            messages = list(group_dict.values())[0]

            media_messages = [media for media in messages if media.photo is not None or media.video is not None]

            if not media_messages:
                print(f"消息 {tg_msg_id} 不包含媒体文件")
                return False

            print(f"找到 {len(media_messages)} 个媒体文件，开始下载...")

            # 下载所有媒体文件
            for i, media in enumerate(media_messages):
                await download_media_file(app, media, i + 1, target_dir)

            # 检查下载文件大小
            if check_and_cleanup_small_files(target_dir):
                print(f"检测到小文件，已删除文件夹: {target_dir}")
                print("退出程序")
                sys.exit(1)

            print(f"成功重新下载 {len(media_messages)} 个文件到: {target_dir}")
            return True

        except FloodWait as e:
            print("停止下载，关闭程序")
            sys.exit(1)
        except Exception as e:
            print(f"重新下载失败: {e}")
            traceback.print_exc()
            return False

async def download_media_file(app, media, index, dir_name):
    """
    下载单个媒体文件
    """
    if media.photo is not None:
        file_path = os.path.join(dir_name, f"{'{:06d}'.format(index)}.jpg")
        file = media.photo
        media_type = "photo"
    elif media.video is not None:
        if media.video.file_size >= 20971520:  # 20MB限制
            print(f"跳过大文件 (索引 {index}): 文件大小 {media.video.file_size} 字节")
            return

        # 根据MIME类型确定文件扩展名
        if media.video.mime_type == 'video/mp4':
            file_type = "mp4"
        elif media.video.mime_type == 'video/webm':
            file_type = "webm"
        elif media.video.mime_type == 'video/quicktime':
            file_type = "mov"
        else:
            file_type = media.video.mime_type.split('/')[-1] if media.video.mime_type else "mp4"

        file_path = os.path.join(dir_name, f"{'{:06d}'.format(index)}.{file_type}")
        file = media.video
        media_type = "video"
    else:
        return

    result = await app.download_media(file, file_path)
    print(f"成功下载 {media_type} (索引 {index}): {os.path.basename(file_path)}")




async def find_and_redownload_yesterday_folders():
    """
    找到所有前一天下载的文件夹，然后尝试重新下载
    """
    print("开始查找前一天下载的文件夹...")

    # 计算前一天的日期
    yesterday_date = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    yesterday_month = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m')

    print(f"查找日期: {yesterday_date}")

    try:
        mongo.connect()

        # 查询前一天下载的记录
        query = {
            "date": yesterday_date,
            "path": {"$ne": None, "$exists": True}
        }

        yesterday_records = list(mongo.find_data('tele_img', query))
        print(f"找到 {len(yesterday_records)} 条前一天的下载记录")

        if not yesterday_records:
            print("没有找到前一天的下载记录")
            mongo.close()
            return

        # 统计信息
        redownload_count = 0
        success_count = 0
        failed_count = 0

        for record in yesterday_records:
            folder_path = record.get('path')
            channel = record.get('channel')
            tg_msg_id = record.get('tg_msg_id')
            title = record.get('title')
            record_id = record.get('_id')

            if channel == 'simisebaisi':
                continue

            if not all([folder_path, channel, tg_msg_id, title]):
                print(f"记录信息不完整，跳过: {record}")
                continue

            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                print(f"文件夹不存在，跳过: {folder_path}")
                continue

            # 检查文件夹是否需要重新下载（文件数量少或文件很小）
            if should_redownload_folder(folder_path):
                print(f"准备重新下载文件夹: {folder_path}")
                redownload_count += 1

                try:
                    # 备份原文件夹（如果需要的话）
                    backup_path = folder_path + "_backup_" + datetime.datetime.now().strftime('%H%M%S')
                    if os.path.exists(folder_path) and os.listdir(folder_path):
                        # remove all folder content
                        shutil.rmtree(folder_path)

                    # 重新创建文件夹
                    os.makedirs(folder_path, exist_ok=True)

                    # 异步重新下载
                    success = await redownload_media_group(channel, tg_msg_id, folder_path, title)

                    if success:
                        success_count += 1
                        print(f"成功重新下载: {folder_path}")

                        # 删除备份文件夹
                        if os.path.exists(backup_path):
                            shutil.rmtree(backup_path)
                            print(f"删除备份文件夹: {backup_path}")

                        # 更新MongoDB记录状态
                        mongo.update_data('tele_img', {"_id": record_id}, {
                            "status": "Redownloaded_Yesterday",
                            "redownload_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    else:
                        failed_count += 1
                        print(f"重新下载失败: {folder_path}")

                        # 恢复备份文件夹
                        if os.path.exists(backup_path):
                            if os.path.exists(folder_path):
                                shutil.rmtree(folder_path)
                            shutil.move(backup_path, folder_path)
                            print(f"已恢复备份文件夹: {folder_path}")

                        # 更新MongoDB记录状态
                        mongo.update_data('tele_img', {"_id": record_id}, {
                            "status": "Redownload_Failed_Yesterday",
                            "redownload_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })

                except Exception as e:
                    failed_count += 1
                    print(f"重新下载过程中出错: {folder_path}, 错误: {e}")
                    traceback.print_exc()

                    # 尝试恢复备份
                    backup_path = folder_path + "_backup_" + datetime.datetime.now().strftime('%H%M%S')
                    if os.path.exists(backup_path):
                        try:
                            if os.path.exists(folder_path):
                                shutil.rmtree(folder_path)
                            shutil.move(backup_path, folder_path)
                            print(f"已恢复备份文件夹: {folder_path}")
                        except Exception as restore_e:
                            print(f"恢复备份失败: {restore_e}")
            else:
                print(f"文件夹状态良好，无需重新下载: {folder_path}")

        print(f"\n前一天文件夹重新下载完成:")
        print(f"- 需要重新下载的文件夹: {redownload_count}")
        print(f"- 成功重新下载: {success_count}")
        print(f"- 重新下载失败: {failed_count}")

        mongo.close()

    except Exception as e:
        print(f"查找前一天文件夹时出错: {e}")
        traceback.print_exc()
        try:
            mongo.close()
        except:
            pass


def should_redownload_folder(folder_path):
    """
    判断文件夹是否需要重新下载
    条件：
    1. 文件夹为空
    2. 图片文件数量少于5个
    3. 大部分图片文件小于20kb
    """
    if not os.path.exists(folder_path):
        return False

    # 获取所有图片和视频文件
    media_files = []
    for file_name in os.listdir(folder_path):
        if file_name.lower().startswith("."):
            continue
        file_path = os.path.join(folder_path, file_name)
        if os.path.isfile(file_path):
            # 检查是否为媒体文件
            if file_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.mp4', '.webm', '.mov')):
                media_files.append(file_path)

    # 条件1: 文件夹为空或没有媒体文件
    if len(media_files) == 0:
        print(f"文件夹为空或无媒体文件: {folder_path}")
        return True

    # 条件2: 媒体文件数量少于5个
    if len(media_files) < 5:
        print(f"媒体文件数量过少 ({len(media_files)}): {folder_path}")
        return True

    # 条件3: 检查小文件比例
    small_files = 0
    for file_path in media_files:
        try:
            if os.path.getsize(file_path) < 20000:  # 20kb
                small_files += 1
        except OSError:
            small_files += 1  # 无法读取大小的文件也算作小文件

    small_file_ratio = small_files / len(media_files)
    if small_file_ratio > 0.5:  # 超过50%的文件都很小
        print(f"小文件比例过高 ({small_file_ratio:.2%}): {folder_path}")
        return True

    return False


def cleanup_empty_folders_and_invalid_records():
    """
    清理任务：
    1. 遍历 /Volumes/新加卷/tg-image/YYYY-MM (当前月份) 的所有子文件夹，
       如果发现子文件夹里的图片都是0kb就删掉这个子文件夹
    2. 查询mongo数据库，如果发现保存的path路径不存在，就删掉对应的mongo数据库记录
    """
    print("开始执行清理任务...")

    # 任务1: 清理空图片文件夹
    current_month = datetime.datetime.now().strftime('%Y-%m')
    base_path = f'/Volumes/新加卷/tg-image/{current_month}'

    if os.path.exists(base_path):
        print(f"检查路径: {base_path}")

        for folder_name in os.listdir(base_path):
            folder_path = os.path.join(base_path, folder_name)
            parent_dir = os.path.dirname(folder_path)
            folder_name = os.path.basename(folder_path)

            cleaned_name = folder_name.strip()

            if folder_name != cleaned_name:
                new_path = os.path.join(parent_dir, cleaned_name)
                shutil.move(folder_path, new_path)
                print(f'已将文件夹 "{folder_name}" 重命名为 "{cleaned_name}"。')

        # 遍历当前月份目录下的所有子文件夹
        for folder_name in os.listdir(base_path):
            folder_path = os.path.join(base_path, folder_name)

            if os.path.isdir(folder_path):
                # print(f"检查文件夹: {folder_path}")

                # 检查文件夹中的所有图片文件
                image_files = []
                for file_name in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file_name)
                    if os.path.isfile(file_path):
                        # 检查是否为图片文件（根据扩展名）
                        if not file_name.lower().startswith(".") and file_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                            image_files.append(file_path)

                # 如果有图片文件，检查并删除小于20kb的图片
                if image_files:
                    valid_images = []
                    deleted_small_images = []

                    for img_file in image_files:
                        if os.path.getsize(img_file) < 20000:
                            # 记录要删除的小图片
                            deleted_small_images.append(img_file)
                            try:
                                os.remove(img_file)
                                print(f"删除小于20kb的图片: {img_file}")
                            except Exception as e:
                                print(f"删除图片失败 {img_file}: {e}")
                        else:
                            valid_images.append(img_file)

                    # 根据删除的小图片数量决定操作
                    deleted_count = len(deleted_small_images)

                    if deleted_count > 10:
                        # 超过10张小图片被删除，需要重新下载
                        print(f"文件夹 {folder_path} 中删除了 {deleted_count} 张小于20kb的图片，准备重新下载...")

                        # 查找对应的MongoDB记录
                        try:
                            mongo.connect()
                            # 根据路径查找记录
                            record = list(mongo.find_data('tele_img', {"path": folder_path}))
                            if not record:
                                # 尝试根据文件夹名查找
                                folder_title = os.path.basename(folder_path)
                                record = list(mongo.find_data('tele_img', {"title": folder_title}))

                            if record:
                                record = record[0]  # 取第一条记录
                                channel = record.get('channel')
                                tg_msg_id = record.get('tg_msg_id')
                                title = record.get('title')

                                if channel and tg_msg_id:

                                    if channel == 'simisebaisi':
                                        continue
                                    # 清空文件夹准备重新下载
                                    shutil.rmtree(folder_path)
                                    os.makedirs(folder_path, exist_ok=True)

                                    # 异步重新下载
                                    success = asyncio.run(redownload_media_group(channel, tg_msg_id, folder_path, title))
                                    if success:
                                        print(f"成功重新下载: {folder_path}")
                                        # 更新MongoDB记录状态
                                        mongo.update_data('tele_img', {"_id": record["_id"]},
                                                        {"status": "Redownloaded", "redownload_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                                    else:
                                        print(f"重新下载失败: {folder_path}")
                                        mongo.update_data('tele_img', {"_id": record["_id"]},
                                                        {"status": "Redownload_Failed", "redownload_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                                else:
                                    print(f"MongoDB记录缺少必要信息，无法重新下载: {folder_path}")
                            else:
                                print(f"未找到对应的MongoDB记录: {folder_path}")

                            mongo.close()
                        except Exception as e:
                            print(f"重新下载过程中出错: {e}")
                            try:
                                mongo.close()
                            except:
                                pass

                    elif deleted_count > 0 and len(valid_images) < 10:
                        # 删除的图片少于10张，且有效图片也很少，删除整个文件夹
                        print(f"文件夹 {folder_path} 中删除了 {deleted_count} 张小图片，有效图片只有 {len(valid_images)} 张，删除整个文件夹")
                        try:
                            shutil.rmtree(folder_path)
                            print(f"成功删除文件夹: {folder_path}")
                        except Exception as e:
                            print(f"删除文件夹失败 {folder_path}: {e}")
                    else:
                        print(f"文件夹 {folder_path} 包含 {len(valid_images)} 个有效图片，删除了 {deleted_count} 张小图片，保留文件夹")
                else:
                    print(f"文件夹中没有图片文件: {folder_path}")
    else:
        print(f"当前月份目录不存在: {base_path}")

    # 任务2: 清理MongoDB中路径不存在的记录
    print("开始清理MongoDB中的无效记录...")

    try:
        mongo.connect()
        # 查询所有有path字段的记录
        records = list(mongo.find_data('tele_img', {"path": {"$ne": None, "$exists": True}}))

        deleted_count = 0
        for record in records:
            path = record.get('path')
            parent_dir = os.path.dirname(path)
            # delete trailing space of file_name
            file_name = os.path.basename(path)
            cleaned_file_name = file_name.strip()
            new_path = os.path.join(parent_dir,cleaned_file_name)

            if path and not os.path.exists(path) and not os.path.exists(new_path):
                print(f"删除无效路径记录: {path}")
                try:
                    mongo.delete_data('tele_img', {"_id": record["_id"]})
                    deleted_count += 1
                except Exception as e:
                    print(f"删除记录失败 {record['_id']}: {e}")

        print(f"共删除了 {deleted_count} 条无效路径记录")
        mongo.close()

    except Exception as e:
        print(f"清理MongoDB记录时出错: {e}")
        try:
            mongo.close()
        except:
            pass

    print("清理任务完成")

def load_his_urls():
    global his_set
    list_of_files = glob.glob(file_path_dir)
    latest_file = max(list_of_files, key=os.path.getctime)
    with open(latest_file, 'r') as log:
        lines = log.readlines()

    url_list = set(lines)
    url_list = set(map(lambda x: x.rstrip('\n'), url_list))
    his_set = url_list

def load_telegram_links(channel):
    r.url('https://web.telegram.org/k/#@' + channel)
    r.click('//*[@id="column-center"]/div/div/div[2]/div[1]/div[1]/div/div[1]/img')
    # r.click('/html/body/div[1]/div/div[2]/div/div/div[2]/div[1]/div[1]/div/div[1]/img')
    r.wait(2)
    # r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[4]/div')
    r.click('//*[@id="column-right"]/div/div/div[2]/div/div/div[3]/div[1]/div/nav/div[5]/div')
    r.wait(5)

    content = r.read('page')
    soup = BeautifulSoup(content, features="html.parser")
    div = soup.find("div", {"id": "column-right"})
    links = div.findAll("a")
    links = list(filter(lambda x: x, links))
    links2 = list(filter(lambda x: 'href' in x.attrs and 'https://telegra.ph' in x.attrs['href'], links))

    links3 = list(map(lambda x: x.attrs['href'], links2))
    with open(file_path + now + ".log", 'a') as log:
        for link in links3:
            log.write(link)
            log.write("\n")
    links3 = list(filter(lambda x: x not in his_set, links3))
    print(links3)

    try:
        mongo.connect()
        links2 = list(filter(lambda x: x.attrs['href'] not in his_set, links2))
        for link in links2:
            data = mongo.count_data('tele_img', {"link": link.attrs['href']})
            if data == 0:
                mongo.insert_data('tele_img', {
                    "link": link.attrs['href'],
                    "title": link.next[19:],
                    "channel": channel,
                    "date": datetime.datetime.now().strftime('%Y-%m-%d')
                })
        mongo.close()
    except Exception as e:
        print(e)
    return links3

def download_from_link(content):
    r.run(r'/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args "chrome-extension://dbjbempljhcmhlfpfacalomonjpalpko/multiUrlExtractor.html" --start-maximized')

    r.clipboard(content)
    # r.wait(5)
    r.click(400, 550)
    r.keyboard('[cmd]v')
    r.click(300, 830)
    r.wait(20)
    r.keyboard('[ctrl]a')
    r.keyboard('[ctrl]d')
    r.wait(2)
    r.click(1218,418)
    r.wait(2)
    r.click(1400,420)



def main_func():
    load_his_urls()
    link_list = []
    link_list.extend(load_telegram_links('AnchorPic'))
    link_list.extend(load_telegram_links('douza23333'))
    link_list.extend(load_telegram_links('meitu520'))
    link_list.extend(load_telegram_links('miaotuya'))
    link_list.extend(load_telegram_links('MarioBase'))
    link_list.extend(load_telegram_links('TAOT808'))

    msg = ''
    for link in link_list:
        msg += link + '\n'

    print(msg)
    download_from_link(msg)
    r.telegram(-1001497463795, "I'va done today's work")


def run():
    # 在正式运行前执行清理任务
    cleanup_empty_folders_and_invalid_records()

    # v1
    # r.init(True)
    # main_func()
    # r.close()

    # v2
    # pyrogramMsgUtil.get_img_message_id('AnchorPic', 4998)
    pyrogramMsgUtil.get_img_message_id('douza23333', 52534)
    pyrogramMsgUtil.get_img_message_id('miaotuya', 39468)
    pyrogramMsgUtil.get_img_message_id('MarioBase', 35346)
    # pyrogramMsgUtil.get_img_message_id('TAOT808', 4998)
    telegram_img_download.startScrapy()


def run_yesterday_redownload():
    """
    专门用于重新下载前一天文件夹的函数
    """
    print("开始执行前一天文件夹重新下载任务...")
    asyncio.run(find_and_redownload_yesterday_folders())
    print("前一天文件夹重新下载任务完成")


if __name__ == '__main__':
    # run()
    # run_yesterday_redownload()
    cleanup_empty_folders_and_invalid_records()



# See PyCharm help at https://www.jetbrains.com/help/pycharm/
