import urllib.request
import os
import requests


def download(web_url, path):
    # headers = {
    #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'}

    opener = urllib.request.build_opener()
    opener.addheaders = [('User-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36')]
    urllib.request.install_opener(opener)
    urllib.request.urlretrieve(web_url, path)




if __name__ == '__main__':
    download('https://imagecache.civitai.com/xG1nkqKTMzGDvpLrqFT7WA/f6dff574-d6a6-447e-3961-51444402e100/width=720/233802', r'C:\Users\<USER>\Desktop\autorun\sd\test.png')
