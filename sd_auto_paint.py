
import datetime
import sys

from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import sd_prmpt
import tkinter as tk
import tagui_2 as r
import csv_processor


import pytz

import telegram_sender

utc = pytz.UTC
IST = pytz.timezone('Asia/Shanghai')

root = tk.Tk()

SCREEN_WIDTH = root.winfo_screenwidth()
SCREEN_HEIGHT = root.winfo_screenheight()

TIME_OUT = 300

now = datetime.datetime.now().strftime('%Y-%m-%d')

yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = yesterday - datetime.timedelta(hours=8)
yesterday = utc.localize(yesterday)


def init_sd_server():

    r.keyboard('[win]r')
    r.clipboard(r'C:\Users\<USER>\Desktop\code\stable-diffusion-webui\webui-user.bat')
    r.keyboard('[ctrl]v')
    r.keyboard('[enter]')
    timer = 0
    timeout = 60
    while (r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\server_done.png') is False) and timer <= timeout:
        r.wait(5)
        timer += 5
    if timer > timeout:
        r.telegram(-1001497463795, "stable diffusion server init error!")
        r.close()
        sys.exit()
    r.keyboard('[alt][tab]')


def close_server():
    r.keyboard('[win]r')
    r.clipboard(r'cmd')
    r.keyboard('[ctrl]v')
    r.keyboard('[enter]')
    r.clipboard("""for /f "skip=1" %p in ('wmic process where "commandline like '%webui-user.bat%'" get processid') do taskkill /PID %p""")
    r.keyboard('[ctrl]v')
    r.keyboard('[enter]')
    r.wait(5)
    r.keyboard('[alt][f4]')


def init_sd():
    close_server()
    init_sd_server()
    r.url('http://localhost:7860/')
    r.wait(5)
    if r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\remove_popover.png'):
        r.click(r'C:\Users\<USER>\Desktop\autorun\sd_webui\remove_popover.png')
        r.keyboard('[esc]')
    if not r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\prmpt_input.png'):
        # r.telegram(-1001497463795, "stable diffusion init error!")
        # r.close()
        # sys.exit()
        init_sd_server()
        r.url('http://localhost:7860/')
        r.wait(20)
        if not r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\prmpt_input.png'):
            r.telegram(-1001497463795, "stable diffusion init error!")
            r.close()
            sys.exit()

def multi_txt2img(prompt, times, dir_name):
    list_of_files = glob.glob(r'C:\Users\<USER>\Desktop\code\stable-diffusion-webui\outputs\txt2img-images\*')
    r.url('http://localhost:7860/')
    r.wait(5)
    if not r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\prmpt_input.png'):
        r.wait(10)
    r.click(r'C:\Users\<USER>\Desktop\autorun\sd_webui\prmpt_input.png')
    r.type(r'C:\Users\<USER>\Desktop\autorun\sd_webui\prmpt_input.png', '11')
    r.wait(1)
    r.keyboard('[ctrl]a')
    r.keyboard('[delete]')
    r.clipboard(prompt)
    r.keyboard('[ctrl]v')
    r.wait(5)
    r.click(r'C:\Users\<USER>\Desktop\autorun\sd_webui\autofill.png')
    r.wait(5)
    for i in range(times):
        if txt2img(dir_name) is False:
            break
    move_img(times, dir_name, prompt, list_of_files)



def txt2img(dir_name):

    r.click(r'C:\Users\<USER>\Desktop\autorun\sd_webui\generate.png')
    r.wait(10)

    wait_sec = 10
    r.wait(30)
    while r.present(r'C:\Users\<USER>\Desktop\autorun\sd_webui\working_state.png'):
        r.wait(10)
        wait_sec += 10
        if wait_sec > TIME_OUT:
            r.telegram(-1001497463795, "txt2img generate img timeout! Dir Name:" + dir_name)
            return False

    try:
        send_img()
    except Exception as e:
        r.telegram(e)
    return True

def move_img(times, dir_name, prmt, his_list):
    list_of_files = glob.glob(r'C:\Users\<USER>\Desktop\code\stable-diffusion-webui\outputs\txt2img-images\*')
    files = [x for x in list_of_files if x not in his_list]
    file_dir = r'C:\Users\<USER>\Desktop\autorun\sd' + '\\' + dir_name
    if not os.path.exists(file_dir):
        os.mkdir(file_dir)
        with open(file_dir + '\\' + "config.log", 'a+') as log:
            log.write(prmt)
    for file in files:
        file_name = os.path.basename(file)
        os.rename(file, file_dir + '\\' + file_name)

last_send_img = None
def send_img():
    global last_send_img
    list_of_files = glob.glob(r'C:\Users\<USER>\Desktop\code\stable-diffusion-webui\outputs\txt2img-images\*')
    latest_file = max(list_of_files, key=os.path.getctime)
    if last_send_img == latest_file:
        return
    last_send_img = latest_file
    # now = datetime.datetime.now()
    # before_one_hour = now - datetime.timedelta(hours=2)

    telegram_sender.send_img(latest_file, "-865719971")
    # for file in files:
    #     telegram_sender.send_img(file)


def main_func():
    init_sd()
    for prmpt, times, dir_name in sd_prmpt.prompts:
        multi_txt2img(prmpt, times, dir_name)
        # multi_txt2img(prmpt, 1)
    r.telegram(-1001497463795, "txt2img done")
    close_server()








if __name__ == '__main__':
    r.init(True)
    main_func()
    # send_img()
    r.close()

