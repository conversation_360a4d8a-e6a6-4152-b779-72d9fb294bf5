import datetime

from mongo.MongoDBHelper import MongoDBHelper

now = datetime.datetime.now().strftime('%Y-%m-%d')

# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    # 测试用例
    # 创建实例
    helper = MongoDBHelper()

    helper.connect()

    helper.insert_data('tele_img', {
        "link": "https:\/\/telegra.ph\/%E6%98%8E%E6%97%A5%E8%A6%8B%E6%9C%AA%E6%9D%A51st%E5%86%99%E7%9C%9F%E9%9B%86%E3%81%82%E3%81%99%E3%81%BF%E3%81%BF%E5%9B%B3%E9%91%91118P-08-19",
        "title": "明日見未来1st写真集あすみみ図鑑118P-08-19",
        "channel": "AnchorPic",
        "date": "2023-08-20",
        "path": "\/Volumes\/新加卷\/telegra.ph\/明日見未来1st写真集『あすみみ図鑑』118P_–_Telegraph",
        "status": None
    })

    # result = helper.count_data('tele_img',
    #                  {"link": 'https://telegra.ph/%E7%89%A9%E6%81%8B%E4%BC%A0%E6%B7%B1%E9%82%83%E7%9A%84%E5%85%89-150P1V-08-19'})
    # for item in result:
    #     print (item)
    # print(result)
    helper.close()
