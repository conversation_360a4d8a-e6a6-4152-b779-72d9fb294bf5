import asyncio


# def get_video_list(user):
    # url = "https://api.bilibili.com/x/space/wbi/arc/search?mid=" + user
    #
    # headers = {
    #     "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    #     "Accept": "application/json, text/javascript, */*; q=0.01",
    #     "Accept-Encoding": "gzip, deflate, br",
    #     "Accept-Language": "en-US,en;q=0.5",
    #     "Referer": "https://space.bilibili.com/",
    #     "Connection": "keep-alive",
    # }
    #
    # response = requests.get(url, headers=headers)
    # data = json.loads(response.content)
    # result_list = []
    # if "data" in data and "list" in data["data"] and "vlist" in data["data"]["list"]:
    #     vlist = data["data"]["list"]["vlist"]
    #     for item in vlist:
    #         if "title" in item and "bvid" in item and "created" in item and "typeid" in item and item["typeid"]==130 and item["bvid"] not in his_set:
    #             if item["bvid"] == 'BV1Yc411K7jy':
    #                 break
    #             result_list.append({
    #                 "title": item["title"],
    #                 "bvid": item["bvid"],
    #                 "created": item["created"],
    #             })

    # result_list = asyncio.get_event_loop().run_until_complete(scrapter.main())
    # result_list = list(filter(lambda x: x['bvid'] not in his_set, result_list))

    # with open('/Volumes/新加卷/music/bilibili/bilibili-log/bilibili_download'+now+'.log', 'a') as log:
    #     for item in result_list:
    #         log.write(item["bvid"])
    #         log.write("\n")
    # return result_list