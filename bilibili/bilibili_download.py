import asyncio
import os
import random
import re
import shutil
import time
import glob

import requests
import json
from you_get import common
import datetime

import scrapter

his_set = set()
file_path_dir = '/Volumes/新加卷/music/bilibili/bilibili-log/*'
now = datetime.datetime.now().strftime('%Y-%m-%d')

def load_his_urls():
    global his_set
    list_of_files = glob.glob(file_path_dir)
    latest_file = max(list_of_files, key=os.path.getctime)
    with open(latest_file, 'r') as log:
        lines = log.readlines()

    url_list = set(lines)
    url_list = set(map(lambda x: x.rstrip('\n'), url_list))
    his_set = url_list

def get_video_list(user):
    result_list = asyncio.get_event_loop().run_until_complete(scrapter.main(user))
    for data in result_list:
        data['bvid'] = 'BV' + data['bvid']
    result_list = list(filter(lambda x: x['bvid'] not in his_set, result_list))

    with open('/Volumes/新加卷/music/bilibili/bilibili-log/bilibili_download'+now+'.log', 'a') as log:
        for item in result_list:
            log.write(item["bvid"])
            log.write("\n")
    return result_list


def download(bvid, save_dir='/Volumes/新加卷/music/bilibili'):
    v_url = 'https://www.bilibili.com/video/' + bvid
    # 保存格式
    v_format = None
    common.any_download(url=v_url, stream_id=v_format, info_only=False, output_dir=save_dir, merge=True)


def run():
    load_his_urls()
    link_list = get_video_list('318886391')
    link_list.extend(get_video_list('286664495'))
    for item in link_list:
        download(item["bvid"])
        time.sleep(random.Random().randint(5, 15))

if __name__ == '__main__':
    run()


