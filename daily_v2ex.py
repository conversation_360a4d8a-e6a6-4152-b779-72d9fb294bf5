import math

import rpa as r
from bs4 import BeautifulSoup
from PIL import Image
import os
import json
import time
import glob
import os
import tkinter as tk
#import tagui_2 as r
import signal
import datetime
import pytz

utc = pytz.UTC
yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
yesterday = utc.localize(yesterday)
link_list = []

def load_his_link():
    global link_list
    with open(r'C:\Users\<USER>\Desktop\autorun\news\v2ex.txt', 'r', encoding="utf-8") as log:
        link_list = log.readlines()
    link_list = list(map(lambda x: x.rstrip('\n'), link_list))



def run():
    r.url("https://v2ex.com/?tab=hot")
    r.wait(5)
    r.keyboard('[esc]')
    r.keyboard('[esc]')
    r.keyboard('[space]')
    r.keyboard('[space]')
    result = r.read("page")
    soup = BeautifulSoup(result, features="html.parser")
    articles = soup.findAll("div", {"class": "cell item"})
    send_list = []
    new_link_list = []
    outdate = False

    for article in articles:
        titleCell = article.find("a", {"class": "topic-link"})
        if titleCell is None:
            continue
        title, link = titleCell.getText(), 'https://v2ex.com' + titleCell['href']
        link = link.split('#')[0]
        if link not in link_list:
            link_list.append(link)
            send_text = title + "\n" + link
            new_link_list.append(link)
            send_list.append(send_text)

    # with open(r'C:\Users\<USER>\Desktop\autorun\news\v2ex.txt', 'a+', encoding="utf-8") as log:
    #     for link in new_link_list:
    #         log.write(link)
    #         log.write("\n")


    for str_1 in send_list:
        r.telegram(-949844566, str_1)
    return outdate




if __name__ == '__main__':
    r.init(visual_automation=True, chrome_browser=True, headless_mode=True,turbo_mode=True  )
    # load_his_link()
    run()

    r.close()