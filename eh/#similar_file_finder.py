import os
from difflib import SequenceMatcher
from collections import defaultdict

def similar(a, b):
    return SequenceMatcher(None, a, b).ratio()

def find_similar_files(directory, threshold):
    similar_files = []

    # 获取指定目录下的所有文件和子目录
    entries = os.listdir(directory)

    for entry in entries:
        if entry.startswith("."):
            continue
        for entry2 in entries:
            if entry == entry2 or entry2.startswith("."):
                continue
            similarity = similar(entry, entry2)
            if similarity > threshold:
                print(f"find similar file: {(entry,entry2)}")
                similar_files.append((entry,entry2))



    return similar_files


if __name__ == '__main__':
    # 设置根目录和相似度阈值
    root_directory = '/Volumes/新加卷/telegra.ph'
    similarity_threshold = 0.95

    # 查找相似文件
    similar_files = find_similar_files(root_directory, similarity_threshold)

    # # 打印相似文件列表
    # for file in similar_files:
    #     print(file)